(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[878],{61457:function(De){function re(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(t=>{const i=e[t],u=typeof i;(u==="object"||u==="function")&&!Object.isFrozen(i)&&re(i)}),e}class ce{constructor(t){t.data===void 0&&(t.data={}),this.data=t.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function oe(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function T(e,...t){const i=Object.create(null);for(const u in e)i[u]=e[u];return t.forEach(function(u){for(const b in u)i[b]=u[b]}),i}const Ce="</span>",ae=e=>!!e.scope,ve=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const i=e.split(".");return[`${t}${i.shift()}`,...i.map((u,b)=>`${u}${"_".repeat(b+1)}`)].join(" ")}return`${t}${e}`};class Le{constructor(t,i){this.buffer="",this.classPrefix=i.classPrefix,t.walk(this)}addText(t){this.buffer+=oe(t)}openNode(t){if(!ae(t))return;const i=ve(t.scope,{prefix:this.classPrefix});this.span(i)}closeNode(t){ae(t)&&(this.buffer+=Ce)}value(){return this.buffer}span(t){this.buffer+=`<span class="${t}">`}}const le=(e={})=>{const t={children:[]};return Object.assign(t,e),t};class Z{constructor(){this.rootNode=le(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(t){this.top.children.push(t)}openNode(t){const i=le({scope:t});this.add(i),this.stack.push(i)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(t){return this.constructor._walk(t,this.rootNode)}static _walk(t,i){return typeof i=="string"?t.addText(i):i.children&&(t.openNode(i),i.children.forEach(u=>this._walk(t,u)),t.closeNode(i)),t}static _collapse(t){typeof t!="string"&&t.children&&(t.children.every(i=>typeof i=="string")?t.children=[t.children.join("")]:t.children.forEach(i=>{Z._collapse(i)}))}}class He extends Z{constructor(t){super(),this.options=t}addText(t){t!==""&&this.add(t)}startScope(t){this.openNode(t)}endScope(){this.closeNode()}__addSublanguage(t,i){const u=t.root;i&&(u.scope=`language:${i}`),this.add(u)}toHTML(){return new Le(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function P(e){return e?typeof e=="string"?e:e.source:null}function ue(e){return D("(?=",e,")")}function Pe(e){return D("(?:",e,")*")}function je(e){return D("(?:",e,")?")}function D(...e){return e.map(i=>P(i)).join("")}function Ue(e){const t=e[e.length-1];return typeof t=="object"&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}function J(...e){return"("+(Ue(e).capture?"":"?:")+e.map(u=>P(u)).join("|")+")"}function fe(e){return new RegExp(e.toString()+"|").exec("").length-1}function $e(e,t){const i=e&&e.exec(t);return i&&i.index===0}const Ge=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function V(e,{joinWith:t}){let i=0;return e.map(u=>{i+=1;const b=i;let _=P(u),c="";for(;_.length>0;){const r=Ge.exec(_);if(!r){c+=_;break}c+=_.substring(0,r.index),_=_.substring(r.index+r[0].length),r[0][0]==="\\"&&r[1]?c+="\\"+String(Number(r[1])+b):(c+=r[0],r[0]==="("&&i++)}return c}).map(u=>`(${u})`).join(t)}const We=/\b\B/,ge="[a-zA-Z]\\w*",q="[a-zA-Z_]\\w*",he="\\b\\d+(\\.\\d+)?",pe="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",de="\\b(0b[01]+)",Ke="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",ze=(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=D(t,/.*\b/,e.binary,/\b.*/)),T({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(i,u)=>{i.index!==0&&u.ignoreMatch()}},e)},j={begin:"\\\\[\\s\\S]",relevance:0},Fe={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[j]},Xe={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[j]},Ye={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},$=function(e,t,i={}){const u=T({scope:"comment",begin:e,end:t,contains:[]},i);u.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const b=J("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return u.contains.push({begin:D(/[ ]+/,"(",b,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),u},Ze=$("//","$"),Je=$("/\\*","\\*/"),Ve=$("#","$"),qe={scope:"number",begin:he,relevance:0},Qe={scope:"number",begin:pe,relevance:0},me={scope:"number",begin:de,relevance:0},et={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[j,{begin:/\[/,end:/\]/,relevance:0,contains:[j]}]},tt={scope:"title",begin:ge,relevance:0},nt={scope:"title",begin:q,relevance:0},it={begin:"\\.\\s*"+q,relevance:0};var G=Object.freeze({__proto__:null,APOS_STRING_MODE:Fe,BACKSLASH_ESCAPE:j,BINARY_NUMBER_MODE:me,BINARY_NUMBER_RE:de,COMMENT:$,C_BLOCK_COMMENT_MODE:Je,C_LINE_COMMENT_MODE:Ze,C_NUMBER_MODE:Qe,C_NUMBER_RE:pe,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(t,i)=>{i.data._beginMatch=t[1]},"on:end":(t,i)=>{i.data._beginMatch!==t[1]&&i.ignoreMatch()}})},HASH_COMMENT_MODE:Ve,IDENT_RE:ge,MATCH_NOTHING_RE:We,METHOD_GUARD:it,NUMBER_MODE:qe,NUMBER_RE:he,PHRASAL_WORDS_MODE:Ye,QUOTE_STRING_MODE:Xe,REGEXP_MODE:et,RE_STARTERS_RE:Ke,SHEBANG:ze,TITLE_MODE:tt,UNDERSCORE_IDENT_RE:q,UNDERSCORE_TITLE_MODE:nt});function st(e,t){e.input[e.index-1]==="."&&t.ignoreMatch()}function rt(e,t){e.className!==void 0&&(e.scope=e.className,delete e.className)}function ct(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=st,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function ot(e,t){Array.isArray(e.illegal)&&(e.illegal=J(...e.illegal))}function at(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function lt(e,t){e.relevance===void 0&&(e.relevance=1)}const ut=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const i=Object.assign({},e);Object.keys(e).forEach(u=>{delete e[u]}),e.keywords=i.keywords,e.begin=D(i.beforeMatch,ue(i.begin)),e.starts={relevance:0,contains:[Object.assign(i,{endsParent:!0})]},e.relevance=0,delete i.beforeMatch},ft=["of","and","for","in","not","or","if","then","parent","list","value"],gt="keyword";function Ee(e,t,i=gt){const u=Object.create(null);return typeof e=="string"?b(i,e.split(" ")):Array.isArray(e)?b(i,e):Object.keys(e).forEach(function(_){Object.assign(u,Ee(e[_],t,_))}),u;function b(_,c){t&&(c=c.map(r=>r.toLowerCase())),c.forEach(function(r){const l=r.split("|");u[l[0]]=[_,ht(l[0],l[1])]})}}function ht(e,t){return t?Number(t):pt(e)?0:1}function pt(e){return ft.includes(e.toLowerCase())}const be={},C=e=>{console.error(e)},_e=(e,...t)=>{console.log(`WARN: ${e}`,...t)},L=(e,t)=>{be[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),be[`${e}/${t}`]=!0)},W=new Error;function Me(e,t,{key:i}){let u=0;const b=e[i],_={},c={};for(let r=1;r<=t.length;r++)c[r+u]=b[r],_[r+u]=!0,u+=fe(t[r-1]);e[i]=c,e[i]._emit=_,e[i]._multi=!0}function dt(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw C("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),W;if(typeof e.beginScope!="object"||e.beginScope===null)throw C("beginScope must be object"),W;Me(e,e.begin,{key:"beginScope"}),e.begin=V(e.begin,{joinWith:""})}}function Et(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw C("skip, excludeEnd, returnEnd not compatible with endScope: {}"),W;if(typeof e.endScope!="object"||e.endScope===null)throw C("endScope must be object"),W;Me(e,e.end,{key:"endScope"}),e.end=V(e.end,{joinWith:""})}}function bt(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function _t(e){bt(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),dt(e),Et(e)}function Mt(e){function t(c,r){return new RegExp(P(c),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(r?"g":""))}class i{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(r,l){l.position=this.position++,this.matchIndexes[this.matchAt]=l,this.regexes.push([l,r]),this.matchAt+=fe(r)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const r=this.regexes.map(l=>l[1]);this.matcherRe=t(V(r,{joinWith:"|"}),!0),this.lastIndex=0}exec(r){this.matcherRe.lastIndex=this.lastIndex;const l=this.matcherRe.exec(r);if(!l)return null;const x=l.findIndex((U,m)=>m>0&&U!==void 0),M=this.matchIndexes[x];return l.splice(0,x),Object.assign(l,M)}}class u{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(r){if(this.multiRegexes[r])return this.multiRegexes[r];const l=new i;return this.rules.slice(r).forEach(([x,M])=>l.addRule(x,M)),l.compile(),this.multiRegexes[r]=l,l}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(r,l){this.rules.push([r,l]),l.type==="begin"&&this.count++}exec(r){const l=this.getMatcher(this.regexIndex);l.lastIndex=this.lastIndex;let x=l.exec(r);if(this.resumingScanAtSamePosition()&&!(x&&x.index===this.lastIndex)){const M=this.getMatcher(0);M.lastIndex=this.lastIndex+1,x=M.exec(r)}return x&&(this.regexIndex+=x.position+1,this.regexIndex===this.count&&this.considerAll()),x}}function b(c){const r=new u;return c.contains.forEach(l=>r.addRule(l.begin,{rule:l,type:"begin"})),c.terminatorEnd&&r.addRule(c.terminatorEnd,{type:"end"}),c.illegal&&r.addRule(c.illegal,{type:"illegal"}),r}function _(c,r){const l=c;if(c.isCompiled)return l;[rt,at,_t,ut].forEach(M=>M(c,r)),e.compilerExtensions.forEach(M=>M(c,r)),c.__beforeBegin=null,[ct,ot,lt].forEach(M=>M(c,r)),c.isCompiled=!0;let x=null;return typeof c.keywords=="object"&&c.keywords.$pattern&&(c.keywords=Object.assign({},c.keywords),x=c.keywords.$pattern,delete c.keywords.$pattern),x=x||/\w+/,c.keywords&&(c.keywords=Ee(c.keywords,e.case_insensitive)),l.keywordPatternRe=t(x,!0),r&&(c.begin||(c.begin=/\B|\b/),l.beginRe=t(l.begin),!c.end&&!c.endsWithParent&&(c.end=/\B|\b/),c.end&&(l.endRe=t(l.end)),l.terminatorEnd=P(l.end)||"",c.endsWithParent&&r.terminatorEnd&&(l.terminatorEnd+=(c.end?"|":"")+r.terminatorEnd)),c.illegal&&(l.illegalRe=t(c.illegal)),c.contains||(c.contains=[]),c.contains=[].concat(...c.contains.map(function(M){return wt(M==="self"?c:M)})),c.contains.forEach(function(M){_(M,l)}),c.starts&&_(c.starts,r),l.matcher=b(l),l}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=T(e.classNameAliases||{}),_(e)}function we(e){return e?e.endsWithParent||we(e.starts):!1}function wt(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(t){return T(e,{variants:null},t)})),e.cachedVariants?e.cachedVariants:we(e)?T(e,{starts:e.starts?T(e.starts):null}):Object.isFrozen(e)?T(e):e}var xt="11.11.1";class Ot extends Error{constructor(t,i){super(t),this.name="HTMLInjectionError",this.html=i}}const Q=oe,xe=T,Oe=Symbol("nomatch"),Rt=7,Re=function(e){const t=Object.create(null),i=Object.create(null),u=[];let b=!0;const _="Could not find the language '{}', did you forget to load/include a language module?",c={disableAutodetect:!0,name:"Plain text",contains:[]};let r={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:He};function l(n){return r.noHighlightRe.test(n)}function x(n){let a=n.className+" ";a+=n.parentNode?n.parentNode.className:"";const h=r.languageDetectRe.exec(a);if(h){const d=I(h[1]);return d||(_e(_.replace("{}",h[1])),_e("Falling back to no-highlight mode for this block.",n)),d?h[1]:"no-highlight"}return a.split(/\s+/).find(d=>l(d)||I(d))}function M(n,a,h){let d="",w="";typeof a=="object"?(d=n,h=a.ignoreIllegals,w=a.language):(L("10.7.0","highlight(lang, code, ...args) has been deprecated."),L("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),w=n,d=a),h===void 0&&(h=!0);const S={code:d,language:w};z("before:highlight",S);const B=S.result?S.result:U(S.language,S.code,h);return B.code=S.code,z("after:highlight",B),B}function U(n,a,h,d){const w=Object.create(null);function S(s,o){return s.keywords[o]}function B(){if(!f.keywords){O.addText(E);return}let s=0;f.keywordPatternRe.lastIndex=0;let o=f.keywordPatternRe.exec(E),g="";for(;o;){g+=E.substring(s,o.index);const p=A.case_insensitive?o[0].toLowerCase():o[0],R=S(f,p);if(R){const[k,$t]=R;if(O.addText(g),g="",w[p]=(w[p]||0)+1,w[p]<=Rt&&(Y+=$t),k.startsWith("_"))g+=o[0];else{const Gt=A.classNameAliases[k]||k;N(o[0],Gt)}}else g+=o[0];s=f.keywordPatternRe.lastIndex,o=f.keywordPatternRe.exec(E)}g+=E.substring(s),O.addText(g)}function F(){if(E==="")return;let s=null;if(typeof f.subLanguage=="string"){if(!t[f.subLanguage]){O.addText(E);return}s=U(f.subLanguage,E,!0,Be[f.subLanguage]),Be[f.subLanguage]=s._top}else s=ee(E,f.subLanguage.length?f.subLanguage:null);f.relevance>0&&(Y+=s.relevance),O.__addSublanguage(s._emitter,s.language)}function y(){f.subLanguage!=null?F():B(),E=""}function N(s,o){s!==""&&(O.startScope(o),O.addText(s),O.endScope())}function Ae(s,o){let g=1;const p=o.length-1;for(;g<=p;){if(!s._emit[g]){g++;continue}const R=A.classNameAliases[s[g]]||s[g],k=o[g];R?N(k,R):(E=k,B(),E=""),g++}}function ke(s,o){return s.scope&&typeof s.scope=="string"&&O.openNode(A.classNameAliases[s.scope]||s.scope),s.beginScope&&(s.beginScope._wrap?(N(E,A.classNameAliases[s.beginScope._wrap]||s.beginScope._wrap),E=""):s.beginScope._multi&&(Ae(s.beginScope,o),E="")),f=Object.create(s,{parent:{value:f}}),f}function Te(s,o,g){let p=$e(s.endRe,g);if(p){if(s["on:end"]){const R=new ce(s);s["on:end"](o,R),R.isMatchIgnored&&(p=!1)}if(p){for(;s.endsParent&&s.parent;)s=s.parent;return s}}if(s.endsWithParent)return Te(s.parent,o,g)}function Lt(s){return f.matcher.regexIndex===0?(E+=s[0],1):(se=!0,0)}function Ht(s){const o=s[0],g=s.rule,p=new ce(g),R=[g.__beforeBegin,g["on:begin"]];for(const k of R)if(k&&(k(s,p),p.isMatchIgnored))return Lt(o);return g.skip?E+=o:(g.excludeBegin&&(E+=o),y(),!g.returnBegin&&!g.excludeBegin&&(E=o)),ke(g,s),g.returnBegin?0:o.length}function Pt(s){const o=s[0],g=a.substring(s.index),p=Te(f,s,g);if(!p)return Oe;const R=f;f.endScope&&f.endScope._wrap?(y(),N(o,f.endScope._wrap)):f.endScope&&f.endScope._multi?(y(),Ae(f.endScope,s)):R.skip?E+=o:(R.returnEnd||R.excludeEnd||(E+=o),y(),R.excludeEnd&&(E=o));do f.scope&&O.closeNode(),!f.skip&&!f.subLanguage&&(Y+=f.relevance),f=f.parent;while(f!==p.parent);return p.starts&&ke(p.starts,s),R.returnEnd?0:o.length}function jt(){const s=[];for(let o=f;o!==A;o=o.parent)o.scope&&s.unshift(o.scope);s.forEach(o=>O.openNode(o))}let X={};function Ie(s,o){const g=o&&o[0];if(E+=s,g==null)return y(),0;if(X.type==="begin"&&o.type==="end"&&X.index===o.index&&g===""){if(E+=a.slice(o.index,o.index+1),!b){const p=new Error(`0 width match regex (${n})`);throw p.languageName=n,p.badRule=X.rule,p}return 1}if(X=o,o.type==="begin")return Ht(o);if(o.type==="illegal"&&!h){const p=new Error('Illegal lexeme "'+g+'" for mode "'+(f.scope||"<unnamed>")+'"');throw p.mode=f,p}else if(o.type==="end"){const p=Pt(o);if(p!==Oe)return p}if(o.type==="illegal"&&g==="")return E+=`
`,1;if(ie>1e5&&ie>o.index*3)throw new Error("potential infinite loop, way more iterations than matches");return E+=g,g.length}const A=I(n);if(!A)throw C(_.replace("{}",n)),new Error('Unknown language: "'+n+'"');const Ut=Mt(A);let ne="",f=d||Ut;const Be={},O=new r.__emitter(r);jt();let E="",Y=0,v=0,ie=0,se=!1;try{if(A.__emitTokens)A.__emitTokens(a,O);else{for(f.matcher.considerAll();;){ie++,se?se=!1:f.matcher.considerAll(),f.matcher.lastIndex=v;const s=f.matcher.exec(a);if(!s)break;const o=a.substring(v,s.index),g=Ie(o,s);v=s.index+g}Ie(a.substring(v))}return O.finalize(),ne=O.toHTML(),{language:n,value:ne,relevance:Y,illegal:!1,_emitter:O,_top:f}}catch(s){if(s.message&&s.message.includes("Illegal"))return{language:n,value:Q(a),illegal:!0,relevance:0,_illegalBy:{message:s.message,index:v,context:a.slice(v-100,v+100),mode:s.mode,resultSoFar:ne},_emitter:O};if(b)return{language:n,value:Q(a),illegal:!1,relevance:0,errorRaised:s,_emitter:O,_top:f};throw s}}function m(n){const a={value:Q(n),illegal:!1,relevance:0,_top:c,_emitter:new r.__emitter(r)};return a._emitter.addText(n),a}function ee(n,a){a=a||r.languages||Object.keys(t);const h=m(n),d=a.filter(I).filter(Ne).map(y=>U(y,n,!1));d.unshift(h);const w=d.sort((y,N)=>{if(y.relevance!==N.relevance)return N.relevance-y.relevance;if(y.language&&N.language){if(I(y.language).supersetOf===N.language)return 1;if(I(N.language).supersetOf===y.language)return-1}return 0}),[S,B]=w,F=S;return F.secondBest=B,F}function yt(n,a,h){const d=a&&i[a]||h;n.classList.add("hljs"),n.classList.add(`language-${d}`)}function te(n){let a=null;const h=x(n);if(l(h))return;if(z("before:highlightElement",{el:n,language:h}),n.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",n);return}if(n.children.length>0&&(r.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(n)),r.throwUnescapedHTML))throw new Ot("One of your code blocks includes unescaped HTML.",n.innerHTML);a=n;const d=a.textContent,w=h?M(d,{language:h,ignoreIllegals:!0}):ee(d);n.innerHTML=w.value,n.dataset.highlighted="yes",yt(n,h,w.language),n.result={language:w.language,re:w.relevance,relevance:w.relevance},w.secondBest&&(n.secondBest={language:w.secondBest.language,relevance:w.secondBest.relevance}),z("after:highlightElement",{el:n,result:w,text:d})}function St(n){r=xe(r,n)}const Nt=()=>{K(),L("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function At(){K(),L("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let ye=!1;function K(){function n(){K()}if(document.readyState==="loading"){ye||window.addEventListener("DOMContentLoaded",n,!1),ye=!0;return}document.querySelectorAll(r.cssSelector).forEach(te)}function kt(n,a){let h=null;try{h=a(e)}catch(d){if(C("Language definition for '{}' could not be registered.".replace("{}",n)),b)C(d);else throw d;h=c}h.name||(h.name=n),t[n]=h,h.rawDefinition=a.bind(null,e),h.aliases&&Se(h.aliases,{languageName:n})}function Tt(n){delete t[n];for(const a of Object.keys(i))i[a]===n&&delete i[a]}function It(){return Object.keys(t)}function I(n){return n=(n||"").toLowerCase(),t[n]||t[i[n]]}function Se(n,{languageName:a}){typeof n=="string"&&(n=[n]),n.forEach(h=>{i[h.toLowerCase()]=a})}function Ne(n){const a=I(n);return a&&!a.disableAutodetect}function Bt(n){n["before:highlightBlock"]&&!n["before:highlightElement"]&&(n["before:highlightElement"]=a=>{n["before:highlightBlock"](Object.assign({block:a.el},a))}),n["after:highlightBlock"]&&!n["after:highlightElement"]&&(n["after:highlightElement"]=a=>{n["after:highlightBlock"](Object.assign({block:a.el},a))})}function Dt(n){Bt(n),u.push(n)}function Ct(n){const a=u.indexOf(n);a!==-1&&u.splice(a,1)}function z(n,a){const h=n;u.forEach(function(d){d[h]&&d[h](a)})}function vt(n){return L("10.7.0","highlightBlock will be removed entirely in v12.0"),L("10.7.0","Please use highlightElement now."),te(n)}Object.assign(e,{highlight:M,highlightAuto:ee,highlightAll:K,highlightElement:te,highlightBlock:vt,configure:St,initHighlighting:Nt,initHighlightingOnLoad:At,registerLanguage:kt,unregisterLanguage:Tt,listLanguages:It,getLanguage:I,registerAliases:Se,autoDetection:Ne,inherit:xe,addPlugin:Dt,removePlugin:Ct}),e.debugMode=function(){b=!1},e.safeMode=function(){b=!0},e.versionString=xt,e.regex={concat:D,lookahead:ue,either:J,optional:je,anyNumberOfTimes:Pe};for(const n in G)typeof G[n]=="object"&&re(G[n]);return Object.assign(e,G),e},H=Re({});H.newInstance=()=>Re({}),De.exports=H,H.HighlightJS=H,H.default=H}}]);
