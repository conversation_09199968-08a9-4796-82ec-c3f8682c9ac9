"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[877],{24924:function(C){C.exports=Object.getOwnPropertyDescriptor},69511:function(C,T,s){var g=s(24924);if(g)try{g([],"length")}catch(j){g=null}C.exports=g},70708:function(C,T,s){var g=typeof Symbol!="undefined"&&Symbol,j=s(62804);C.exports=function(){return typeof g!="function"||typeof Symbol!="function"||typeof g("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:j()}},62804:function(C){C.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var s={},g=Symbol("test"),j=Object(g);if(typeof g=="string"||Object.prototype.toString.call(g)!=="[object Symbol]"||Object.prototype.toString.call(j)!=="[object Symbol]")return!1;var x=42;s[g]=x;for(var V in s)return!1;if(typeof Object.keys=="function"&&Object.keys(s).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(s).length!==0)return!1;var k=Object.getOwnPropertySymbols(s);if(k.length!==1||k[0]!==g||!Object.prototype.propertyIsEnumerable.call(s,g))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var O=Object.getOwnPropertyDescriptor(s,g);if(O.value!==x||O.enumerable!==!0)return!1}return!0}},13728:function(C,T,s){var g=Function.prototype.call,j=Object.prototype.hasOwnProperty,x=s(2073);C.exports=x.call(g,j)},1689:function(C){C.exports=s;var T=/[#.]/g;function s(g,j){for(var x=g||"",V=j||"div",k={},O=0,A,K,M;O<x.length;)T.lastIndex=O,M=T.exec(x),A=x.slice(O,M?M.index:x.length),A&&(K?K==="#"?k.id=A:k.className?k.className.push(A):k.className=[A]:V=A,O+=A.length),M&&(K=M[0],O++);return{type:"element",tagName:V,properties:k,children:[]}}},45128:function(C,T,s){var g=s(92113),j=s(13725),x=s(1689),V=s(40997).Q,k=s(48692).Q;C.exports=A;var O={}.hasOwnProperty;function A(m,u,f){var b=f?z(f):null;return F;function F($,I){var N=x($,u),L=Array.prototype.slice.call(arguments,2),B=N.tagName.toLowerCase(),S;if(N.tagName=b&&O.call(b,B)?b[B]:B,I&&K(I,N)&&(L.unshift(I),I=null),I)for(S in I)Y(N.properties,S,I[S]);return R(N.children,L),N.tagName==="template"&&(N.content={type:"root",children:N.children},N.children=[]),N}function Y($,I,N){var L,B,S;N==null||N!==N||(L=g(m,I),B=L.property,S=N,typeof S=="string"&&(L.spaceSeparated?S=V(S):L.commaSeparated?S=k(S):L.commaOrSpaceSeparated&&(S=V(k(S).join(" ")))),B==="style"&&typeof N!="string"&&(S=X(S)),B==="className"&&$.className&&(S=$.className.concat(S)),$[B]=U(L,B,S))}}function K(m,u){return typeof m=="string"||"length"in m||M(u.tagName,m)}function M(m,u){var f=u.type;return m==="input"||!f||typeof f!="string"?!1:typeof u.children=="object"&&"length"in u.children?!0:(f=f.toLowerCase(),m==="button"?f!=="menu"&&f!=="submit"&&f!=="reset"&&f!=="button":"value"in u)}function R(m,u){var f,b;if(typeof u=="string"||typeof u=="number"){m.push({type:"text",value:String(u)});return}if(typeof u=="object"&&"length"in u){for(f=-1,b=u.length;++f<b;)R(m,u[f]);return}if(typeof u!="object"||!("type"in u))throw new Error("Expected node, nodes, or string, got `"+u+"`");m.push(u)}function U(m,u,f){var b,F,Y;if(typeof f!="object"||!("length"in f))return W(m,u,f);for(F=f.length,b=-1,Y=[];++b<F;)Y[b]=W(m,u,f[b]);return Y}function W(m,u,f){var b=f;return m.number||m.positiveNumber?!isNaN(b)&&b!==""&&(b=Number(b)):(m.boolean||m.overloadedBoolean)&&typeof b=="string"&&(b===""||j(f)===j(u))&&(b=!0),b}function X(m){var u=[],f;for(f in m)u.push([f,m[f]].join(": "));return u.join("; ")}function z(m){for(var u=m.length,f=-1,b={},F;++f<u;)F=m[f],b[F.toLowerCase()]=F;return b}},65393:function(C,T,s){var g=s(85318),j=s(45128),x=j(g,"div");x.displayName="html",C.exports=x},7889:function(C,T,s){C.exports=s(65393)},25498:function(C,T,s){s.d(T,{v:function(){return Y}});var g=s(80804),j=s(69245),x=s(92068);const V=/[ \t\n\f\r]/g;function k(t){return typeof t=="object"?t.type==="text"?O(t.value):!1:O(t)}function O(t){return t.replace(V,"")===""}var A=s(29447),K=s(41714),M=s(88858),R=s(18384),U=s(38853),W=s(34183),X=s(38029);const z={}.hasOwnProperty,m=new Map,u=/[A-Z]/g,f=new Set(["table","tbody","thead","tfoot","tr"]),b=new Set(["td","th"]),F="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function Y(t,e){if(!e||e.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=e.filePath||void 0;let i;if(e.development){if(typeof e.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");i=h(n,e.jsxDEV)}else{if(typeof e.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof e.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");i=c(n,e.jsx,e.jsxs)}const o={Fragment:e.Fragment,ancestors:[],components:e.components||{},create:i,elementAttributeNameCase:e.elementAttributeNameCase||"react",evaluater:e.createEvaluater?e.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:e.ignoreInvalidStyle||!1,passKeys:e.passKeys!==!1,passNode:e.passNode||!1,schema:e.space==="svg"?A.YP:A.dy,stylePropertyNameCase:e.stylePropertyNameCase||"dom",tableCellAlignToStyle:e.tableCellAlignToStyle!==!1},a=$(o,t,void 0);return a&&typeof a!="string"?a:o.create(t,o.Fragment,{children:a||void 0},void 0)}function $(t,e,n){if(e.type==="element")return I(t,e,n);if(e.type==="mdxFlowExpression"||e.type==="mdxTextExpression")return N(t,e);if(e.type==="mdxJsxFlowElement"||e.type==="mdxJsxTextElement")return B(t,e,n);if(e.type==="mdxjsEsm")return L(t,e);if(e.type==="root")return S(t,e,n);if(e.type==="text")return r(t,e)}function I(t,e,n){const i=t.schema;let o=i;e.tagName.toLowerCase()==="svg"&&i.space==="html"&&(o=A.YP,t.schema=o),t.ancestors.push(e);const a=Q(t,e.tagName,!1),d=y(t,e);let w=E(t,e);return f.has(e.tagName)&&(w=w.filter(function(J){return typeof J=="string"?!k(J):!0})),v(t,d,a,e),l(d,w),t.ancestors.pop(),t.schema=i,t.create(e,a,d,n)}function N(t,e){if(e.data&&e.data.estree&&t.evaluater){const i=e.data.estree.body[0];return(0,j.ok)(i.type==="ExpressionStatement"),t.evaluater.evaluateExpression(i.expression)}Z(t,e.position)}function L(t,e){if(e.data&&e.data.estree&&t.evaluater)return t.evaluater.evaluateProgram(e.data.estree);Z(t,e.position)}function B(t,e,n){const i=t.schema;let o=i;e.name==="svg"&&i.space==="html"&&(o=A.YP,t.schema=o),t.ancestors.push(e);const a=e.name===null?t.Fragment:Q(t,e.name,!0),d=p(t,e),w=E(t,e);return v(t,d,a,e),l(d,w),t.ancestors.pop(),t.schema=i,t.create(e,a,d,n)}function S(t,e,n){const i={};return l(i,E(t,e)),t.create(e,t.Fragment,i,n)}function r(t,e){return e.value}function v(t,e,n,i){typeof n!="string"&&n!==t.Fragment&&t.passNode&&(e.node=i)}function l(t,e){if(e.length>0){const n=e.length>1?e:e[0];n&&(t.children=n)}}function c(t,e,n){return i;function i(o,a,d,w){const G=Array.isArray(d.children)?n:e;return w?G(a,d,w):G(a,d)}}function h(t,e){return n;function n(i,o,a,d){const w=Array.isArray(a.children),J=(0,W.Pk)(i);return e(o,a,d,w,{columnNumber:J?J.column-1:void 0,fileName:t,lineNumber:J?J.line:void 0},void 0)}}function y(t,e){const n={};let i,o;for(o in e.properties)if(o!=="children"&&z.call(e.properties,o)){const a=P(t,o,e.properties[o]);if(a){const[d,w]=a;t.tableCellAlignToStyle&&d==="align"&&typeof w=="string"&&b.has(e.tagName)?i=w:n[d]=w}}if(i){const a=n.style||(n.style={});a[t.stylePropertyNameCase==="css"?"text-align":"textAlign"]=i}return n}function p(t,e){const n={};for(const i of e.attributes)if(i.type==="mdxJsxExpressionAttribute")if(i.data&&i.data.estree&&t.evaluater){const a=i.data.estree.body[0];(0,j.ok)(a.type==="ExpressionStatement");const d=a.expression;(0,j.ok)(d.type==="ObjectExpression");const w=d.properties[0];(0,j.ok)(w.type==="SpreadElement"),Object.assign(n,t.evaluater.evaluateExpression(w.argument))}else Z(t,e.position);else{const o=i.name;let a;if(i.value&&typeof i.value=="object")if(i.value.data&&i.value.data.estree&&t.evaluater){const w=i.value.data.estree.body[0];(0,j.ok)(w.type==="ExpressionStatement"),a=t.evaluater.evaluateExpression(w.expression)}else Z(t,e.position);else a=i.value===null?!0:i.value;n[o]=a}return n}function E(t,e){const n=[];let i=-1;const o=t.passKeys?new Map:m;for(;++i<e.children.length;){const a=e.children[i];let d;if(t.passKeys){const J=a.type==="element"?a.tagName:a.type==="mdxJsxFlowElement"||a.type==="mdxJsxTextElement"?a.name:void 0;if(J){const G=o.get(J)||0;d=J+"-"+G,o.set(J,G+1)}}const w=$(t,a,d);w!==void 0&&n.push(w)}return n}function P(t,e,n){const i=(0,K.s)(t.schema,e);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=i.commaSeparated?(0,g.P)(n):(0,R.P)(n)),i.property==="style"){let o=typeof n=="object"?n:D(t,String(n));return t.stylePropertyNameCase==="css"&&(o=H(o)),["style",o]}return[t.elementAttributeNameCase==="react"&&i.space?M.D[i.property]||i.property:i.attribute,n]}}function D(t,e){try{return U(e,{reactCompat:!0})}catch(n){if(t.ignoreInvalidStyle)return{};const i=n,o=new X.$("Cannot parse `style` attribute",{ancestors:t.ancestors,cause:i,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw o.file=t.filePath||void 0,o.url=F+"#cannot-parse-style-attribute",o}}function Q(t,e,n){let i;if(!n)i={type:"Literal",value:e};else if(e.includes(".")){const o=e.split(".");let a=-1,d;for(;++a<o.length;){const w=(0,x.u2)(o[a])?{type:"Identifier",name:o[a]}:{type:"Literal",value:o[a]};d=d?{type:"MemberExpression",object:d,property:w,computed:!!(a&&w.type==="Literal"),optional:!1}:w}(0,j.ok)(d,"always a result"),i=d}else i=(0,x.u2)(e)&&!/^[a-z]/.test(e)?{type:"Identifier",name:e}:{type:"Literal",value:e};if(i.type==="Literal"){const o=i.value;return z.call(t.components,o)?t.components[o]:o}if(t.evaluater)return t.evaluater.evaluateExpression(i);Z(t)}function Z(t,e){const n=new X.$("Cannot handle MDX estrees without `createEvaluater`",{ancestors:t.ancestors,place:e,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=t.filePath||void 0,n.url=F+"#cannot-handle-mdx-estrees-without-createevaluater",n}function H(t){const e={};let n;for(n in t)z.call(t,n)&&(e[_(n)]=t[n]);return e}function _(t){let e=t.replace(u,q);return e.slice(0,3)==="ms-"&&(e="-"+e),e}function q(t){return"-"+t.toLowerCase()}},80831:function(C,T,s){s.d(T,{l:function(){return f}});var g=s(83368);const j=function(r,v,l,c,h){const y=x(v);if(l!=null&&(typeof l!="number"||l<0||l===Number.POSITIVE_INFINITY))throw new Error("Expected positive finite `index`");if(c!=null&&(!c.type||!c.children))throw new Error("Expected valid `parent`");if(l==null!=(c==null))throw new Error("Expected both `index` and `parent`");return K(r)?y.call(h,r,l,c):!1},x=function(r){if(r==null)return A;if(typeof r=="string")return k(r);if(typeof r=="object")return V(r);if(typeof r=="function")return O(r);throw new Error("Expected function, string, or array as `test`")};function V(r){const v=[];let l=-1;for(;++l<r.length;)v[l]=x(r[l]);return O(c);function c(...h){let y=-1;for(;++y<v.length;)if(v[y].apply(this,h))return!0;return!1}}function k(r){return O(v);function v(l){return l.tagName===r}}function O(r){return v;function v(l,c,h){return!!(K(l)&&r.call(this,l,typeof c=="number"?c:void 0,h||void 0))}}function A(r){return!!(r&&typeof r=="object"&&"type"in r&&r.type==="element"&&"tagName"in r&&typeof r.tagName=="string")}function K(r){return r!==null&&typeof r=="object"&&"type"in r&&"tagName"in r}const M=/\n/g,R=/[\t ]+/g,U=x("br"),W=x(B),X=x("p"),z=x("tr"),m=x(["datalist","head","noembed","noframes","noscript","rp","script","style","template","title",L,S]),u=x(["address","article","aside","blockquote","body","caption","center","dd","dialog","dir","dl","dt","div","figure","figcaption","footer","form,","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","legend","li","listing","main","menu","nav","ol","p","plaintext","pre","section","ul","xmp"]);function f(r,v){const l=v||{},c="children"in r?r.children:[],h=u(r),y=N(r,{whitespace:l.whitespace||"normal",breakBefore:!1,breakAfter:!1}),p=[];(r.type==="text"||r.type==="comment")&&p.push(...Y(r,{whitespace:y,breakBefore:!0,breakAfter:!0}));let E=-1;for(;++E<c.length;)p.push(...b(c[E],r,{whitespace:y,breakBefore:E?void 0:h,breakAfter:E<c.length-1?U(c[E+1]):h}));const P=[];let D;for(E=-1;++E<p.length;){const Q=p[E];typeof Q=="number"?D!==void 0&&Q>D&&(D=Q):Q&&(D!==void 0&&D>-1&&P.push(`
`.repeat(D)||" "),D=-1,P.push(Q))}return P.join("")}function b(r,v,l){return r.type==="element"?F(r,v,l):r.type==="text"?l.whitespace==="normal"?Y(r,l):$(r):[]}function F(r,v,l){const c=N(r,l),h=r.children||[];let y=-1,p=[];if(m(r))return p;let E,P;for(U(r)||z(r)&&(0,g.U)(v,r,z)?P=`
`:X(r)?(E=2,P=2):u(r)&&(E=1,P=1);++y<h.length;)p=p.concat(b(h[y],r,{whitespace:c,breakBefore:y?void 0:E,breakAfter:y<h.length-1?U(h[y+1]):P}));return W(r)&&(0,g.U)(v,r,W)&&p.push("	"),E&&p.unshift(E),P&&p.push(P),p}function Y(r,v){const l=String(r.value),c=[],h=[];let y=0;for(;y<=l.length;){M.lastIndex=y;const P=M.exec(l),D=P&&"index"in P?P.index:l.length;c.push(I(l.slice(y,D).replace(/[\u061C\u200E\u200F\u202A-\u202E\u2066-\u2069]/g,""),y===0?v.breakBefore:!0,D===l.length?v.breakAfter:!0)),y=D+1}let p=-1,E;for(;++p<c.length;)c[p].charCodeAt(c[p].length-1)===8203||p<c.length-1&&c[p+1].charCodeAt(0)===8203?(h.push(c[p]),E=void 0):c[p]?(typeof E=="number"&&h.push(E),h.push(c[p]),E=0):(p===0||p===c.length-1)&&h.push(0);return h}function $(r){return[String(r.value)]}function I(r,v,l){const c=[];let h=0,y;for(;h<r.length;){R.lastIndex=h;const p=R.exec(r);y=p?p.index:r.length,!h&&!y&&p&&!v&&c.push(""),h!==y&&c.push(r.slice(h,y)),h=p?y+p[0].length:y}return h!==y&&!l&&c.push(""),c.join(" ")}function N(r,v){if(r.type==="element"){const l=r.properties||{};switch(r.tagName){case"listing":case"plaintext":case"xmp":return"pre";case"nobr":return"nowrap";case"pre":return l.wrap?"pre-wrap":"pre";case"td":case"th":return l.noWrap?"nowrap":v.whitespace;case"textarea":return"pre-wrap";default:}}return v.whitespace}function L(r){return!!(r.properties||{}).hidden}function B(r){return r.tagName==="td"||r.tagName==="th"}function S(r){return r.tagName==="dialog"&&!(r.properties||{}).open}}}]);
