{"name": "hchatdata-chat-sdk", "version": "0.0.0", "main": "dist/index.es.js", "module": "dist/index.es.js", "unpkg": "dist/index.umd.js", "types": "dist/index.d.ts", "dependencies": {"@ant-design/icons": "^4.7.0", "@microsoft/fetch-event-source": "^2.0.1", "@uiw/react-watermark": "^0.0.5", "ahooks": "^3.7.8", "antd": "^5.17.4", "axios": "^0.21.1", "classnames": "^2.3.2", "dayjs": "^1.11.10", "echarts": "^5.4.2", "genie-ui": "^0.1.0", "github-markdown-css": "^5.5.1", "highlight.js": "^11.9.0", "lodash": "^4.17.11", "moment": "^2.29.4", "react-copy-to-clipboard": "^5.1.0", "react-grid-layout": "^1.4.4", "react-markdown": "^9.0.1", "react-speech-recognition": "^4.0.1", "react-spinners": "^0.13.8", "react-syntax-highlighter": "^15.5.0", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "sql-formatter": "^15.6.1", "tslib": "^2.5.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "scripts": {"start": "npm run start:dev", "start:dev": "node scripts/start.js", "watch": "rollup -c rollup/rollup.esm.config.mjs --watch", "watch:bg": "rollup -c rollup/rollup.esm.config.mjs --watch &", "clean": "rimraf ./dist", "build": "npm run clean && npm run build-es", "test": "node scripts/test.js", "build-ts": "tsc -p tsconfig.build.json", "build-css": "lessc ./src/styles/index.less ./dist/index.css", "build-es": "rollup --config rollup/rollup.esm.config.mjs", "build-umd": "rollup --config rollup/rollup.umd.config.mjs"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"react-hooks/exhaustive-deps": 0}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "defaults", "not ie < 8", "last 2 versions", "> 1%", "iOS 7", "last 3 iOS versions"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.16.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-url": "^8.0.2", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.198", "@types/node": "^16.18.31", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.14", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "cssnano": "^6.0.1", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "http-proxy-middleware": "^2.0.6", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.4.5", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-modules": "^6.0.0", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^18.3.1", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^18.3.0", "react-refresh": "^0.11.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "rimraf": "^5.0.1", "rollup": "^3.22.1", "rollup-plugin-exclude-dependencies-from-bundle": "^1.1.23", "rollup-plugin-less": "^1.1.3", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-typescript2": "^0.36.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}, "engines": {"node": ">=16"}, "packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee"}