.chatFooter {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
  flex-shrink: 0;

  .tools {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    column-gap: 8px;

    .toolItem {
      display: flex;
      align-items: center;
      height: 26px;
      padding: 2px 6px;
      color: var(--text-color-secondary);
      font-size: 12px;
      column-gap: 6px;
      background-color: #f6f6f6;
      border-radius: 14.5px;
      cursor: pointer;
      .toolIcon{
        svg {
          width: 12px;
          height: 12px;
        }
      }

      &:hover {
        background: #EEF1FF;
      }
    }
  }

  .composer {
    display: flex;
    flex-direction: column; /* 垂直布局：文件区域在上，输入框在下 */
    min-height: 80px; /* 改为最小高度，允许内容撑开 */

    .collapseBtn {
      height: 46px;
      margin: 0 10px;
      color: var(--text-color-third);
      font-size: 20px;
      line-height: 46px;
      cursor: pointer;

      &:hover {
        color: var(--chat-blue);
      }
    }

    .addConversation {
      height: 46px;
      margin: 0 20px 0 10px;
      color: var(--text-color-fourth);
      font-size: 26px;
      line-height: 54px;
      cursor: pointer;

      &:hover {
        color: var(--chat-blue);
      }
    }

    .composerInputWrapper {
      position: relative;
      /* flex: 1; 在垂直布局中不需要 */

      .composerInput {
        width: 100%;
        // height: 100%;
        height: 56px;
        :global {
          .ant-select-selector {
            box-sizing: border-box;
            height: 100%;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.87);
            font-size: 14px;
            word-break: break-all;
            // background: #f9f9f9;
            background: #fff;
            border: 1px solid #4A72F5;
            border-radius: 8px;
            transition: border-color 0.15s ease-in-out;
            resize: none;
            display: flex;
            align-items: center;

            .ant-select-selection-search-input {
              height: 32px !important;
              padding: 0 12px;
              line-height: 32px !important;
            }

            .ant-select-selection-search {
              right: 0 !important;
              left: 0 !important;
              display: flex;
              align-items: center;
              height: 100%;
            }

            .ant-select-selection-placeholder {
              line-height: 32px;
              margin-bottom: 0;
              display: flex;
              align-items: center;
              height: 100%;
              padding: 0 12px;
            }
          }

          .ant-select-clear {
            right: auto;
            left: 500px;
            width: 16px;
            height: 16px;
            margin-top: -8px;
            font-size: 16px;
          }
        }


      }

      :global {
        .ant-select-focused {
          .ant-select-selector {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
          }
        }
      }
    }

    .footerAiTip {
      font-size: 12px;
      color: #a3a3a3;
      margin: 4px 0 10px 0;
      line-height: 14px;
      text-align: center;
    }
  }

  .sendBtn {
    position: absolute;
    right: 10px;
    bottom: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #fff;
    font-size: 20px;
    background-color: rgb(184, 184, 191);
    border: unset;
    border-radius: 50%;
    transition: background-color 0.3s ease 0s;

    &.sendBtnActive {
      cursor: pointer;
      background-color: var(--chat-blue);
    }
  }

  .pauseBtn {
    cursor: pointer;
    position: absolute;
    right: 10px;
    bottom: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #fff;
    font-size: 20px;
    background-color: var(--chat-blue);
    border: unset;
    border-radius: 50%;
    transition: background-color 0.3s ease 0s;

    // 呼吸灯效果
    animation: breathingLight 2s ease-in-out infinite;

    svg {
      width: 10px;
      height: 10px;
      background-color: #fff;
      border-radius: 2px;
    }
  }

  // 呼吸灯动画关键帧
  @keyframes breathingLight {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.8);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 0 12px rgba(255, 193, 7, 0);
      transform: scale(1.15);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
      transform: scale(1);
    }
  }

  .uploadBtn {
    cursor: pointer;
    position: absolute;
    right: 50px;
    bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #666;
    font-size: 16px;
    border: unset;
    transition: all 0.3s ease 0s;
    svg {
      width: 20px;
      height: 20px;
    }

    &:hover {
      color: var(--chat-blue);
      // background-color: #e6f7ff;
    }
  }

  .audioBtn {
    cursor: pointer;
    position: absolute;
    right: 50px;
    bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #666;
    font-size: 16px;
    border: unset;
    transition: all 0.3s ease 0s;
    svg {
      width: 20px;
      height: 20px;
    }

    &:hover {
      color: var(--chat-blue);
      // background-color: #e6f7ff;
    }
  }

  .voiceRecordingEffect {
    position: absolute;
    left: 11px;
    top: 1px;
    width: 550px;
    height: calc(100% - 2px);
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 12px;
    background: #fff;
    border-radius: 7px;
    z-index: 100;
    pointer-events: none;

    .voiceWave {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2px;

      .waveBar {
        width: 2px;
        height: 12px;
        background: #4A72F5;
        border-radius: 1px;
        animation: voiceWave 1.2s ease-in-out infinite;

        &:nth-child(1) {
          animation-delay: 0s;
        }
        &:nth-child(2) {
          animation-delay: 0.1s;
        }
        &:nth-child(3) {
          animation-delay: 0.2s;
        }
        &:nth-child(4) {
          animation-delay: 0.3s;
        }
        &:nth-child(5) {
          animation-delay: 0.4s;
        }
      }
    }

    .recordingText {
      color: #4A72F5;
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;
    }
  }

  @keyframes voiceWave {
    0%, 100% {
      height: 6px;
      opacity: 0.5;
    }
    50% {
      height: 16px;
      opacity: 1;
    }
  }


  &.mobile {
    margin: 6px 10px 10px;

    .addConversation {
      height: 40px;
      margin: 0 12px 0 4px;
    }

    .composer {
      height: 40px;
      :global {
        .ant-select-selector {
          font-size: 14px !important;
          display: flex;
          align-items: center;
        }

        .ant-select-selection-search-input {
          padding: 0 10px !important;
          line-height: 38px !important;
        }

        .ant-select-selection-search {
          display: flex;
          align-items: center;
          height: 100%;
        }

        .ant-select-selection-placeholder {
          margin-bottom: 0 !important;
          padding-left: 0 !important;
          line-height: 38px !important;
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 10px;
        }
      }
    }

    .sendBtn {
      right: 4px;
      bottom: 6px;
    }

    .pauseBtn {
      right: 4px;
      bottom: 6px;
    }
  }

  &.centeredInput {

    position: relative;
    top: 26%;
    z-index: 10;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    width: 100%;
    max-width: 700px;
    flex-shrink: 0;

    .tools {
      display: none; // 居中时隐藏工具栏
    }

    .composer {
      width: 100%;
    }
  }
}

.searchOption {
  padding: 6px 20px;
  color: #212121;
  font-size: 16px;
}

.mobile {
  .searchOption {
    min-height: 26px;
    padding: 2px 12px;
    font-size: 14px;
  }
}

.model {
  margin-top: 2px;
  color: var(--text-color-fourth);
  font-size: 13px;
  line-height: 12px;
}

.autoCompleteDropdown {
  left: 20px !important;
  width: fit-content !important;
  min-width: 100px !important;
  border-radius: 6px;

  &.modelOptions {
    width: 150px !important;

    .searchOption {
      padding: 0 10px;
      color: var(--text-color-secondary);
      font-size: 14px;
    }

    :global {
      .ant-select-item {
        height: 30px !important;
        line-height: 30px !important;
      }
    }
  }
}

.semanticType {
  margin-right: 10px;
}

.quoteText {
  color: var(--chat-blue);
}

.toolIconImg {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.agentTitle {
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16px;
  padding: 8px 0;
  position: relative;
}

.waveAnimation {
  animation: wave 2s linear infinite;
  border: 2px solid #1890ff;
  border-radius: 4px;
  box-shadow: 0 0 8px 2px rgba(24, 144, 255, 0.6);
}

@keyframes wave {
  0% {
    box-shadow: 0 0 8px 2px rgba(24, 144, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 12px 4px rgba(24, 144, 255, 1);
  }
  100% {
    box-shadow: 0 0 8px 2px rgba(24, 144, 255, 0.6);
  }
}
