import { updateMessageContainerScroll, isMobile, uuid, setToken } from '../utils/utils';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import {
  ForwardRefRenderFunction,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import MessageContainer from './MessageContainer';
import { GenieUI } from 'genie-ui';
// 在生产环境中导入构建后的CSS文件
if (process.env.NODE_ENV === 'production') {
  require('genie-ui/dist/index.es.css');
}
import styles from './style.module.less';
import { ConversationDetailType, MessageItem, MessageTypeEnum, AgentType } from './type';
import { queryAgentList, getAllConversations } from './service';
import { useThrottleFn } from 'ahooks';
import Conversation from './Conversation';
import ChatFooter from './ChatFooter';
import classNames from 'classnames';
import { cloneDeep, isBoolean } from 'lodash';
import AgentList from './AgentList';
import MobileAgents from './MobileAgents';
import { HistoryMsgItemType, MsgDataType, SendMsgParamsType } from '../common/type';
import { getHistoryMsg } from '../service';
import ShowCase from '../ShowCase';
import { jsonParse } from '../utils/utils';
import { ConfigProvider, Drawer, Modal, Row, Col, Space, Switch, Tooltip, Spin } from 'antd';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { StreamingProvider } from '../context/StreamingContext';

dayjs.locale('zh-cn');

type Props = {
  token?: string;
  agentIds?: number[];
  initialAgentId?: number;
  chatVisible?: boolean;
  noInput?: boolean;
  isDeveloper?: boolean;
  integrateSystem?: string;
  isCopilot?: boolean;
  onCurrentAgentChange?: (agent?: AgentType) => void;
  onReportMsgEvent?: (msg: string, valid: boolean) => void;
};

const Chat: ForwardRefRenderFunction<any, Props> = (
  {
    token,
    agentIds,
    initialAgentId,
    chatVisible,
    noInput,
    isDeveloper,
    integrateSystem,
    isCopilot,
    onCurrentAgentChange,
    onReportMsgEvent,
  },
  ref
) => {
  const [messageList, setMessageList] = useState<MessageItem[]>([]);
  const [inputMsg, setInputMsg] = useState('');
  const [pageNo, setPageNo] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [historyInited, setHistoryInited] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [currentConversation, setCurrentConversation] = useState<
    ConversationDetailType | undefined
  >(isMobile ? { chatId: 0, chatName: '问答' } : undefined);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [agentList, setAgentList] = useState<AgentType[]>([]);
  const [currentAgent, setCurrentAgent] = useState<AgentType>();
  const [mobileAgentsVisible, setMobileAgentsVisible] = useState(false);
  const [agentListVisible, setAgentListVisible] = useState(true);
  const [conversations, setConversations] = useState<ConversationDetailType[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCaseVisible, setShowCaseVisible] = useState(false);

  const [isSimpleMode, setIsSimpleMode] = useState<boolean>(false);
  const [isDebugMode, setIsDebugMode] = useState<boolean>(true);

  const conversationRef = useRef<any>();
  const agentListRef = useRef<any>();
  const chatFooterRef = useRef<any>();

  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  const [isLUIAgent, setIsLUIAgent] = useState<boolean>(false);  
  const [inputCentered, setInputCentered] = useState<boolean>(false);  
  const [hasUserInput, setHasUserInput] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    sendCopilotMsg,
  }));

  const sendCopilotMsg = (params: SendMsgParamsType) => {
    setAgentListVisible(false);
    const { agentId, msg, modelId } = params;
    if (currentAgent?.id !== agentId) {
      setMessageList([]);
      const agent = agentList.find(item => item.id === agentId) || ({} as AgentType);
      updateCurrentAgent({ ...agent, initialSendMsgParams: params });
    } else {
      onSendMsg(msg, messageList, modelId, params);
    }
  };

  const updateAgentConfigMode = (agent: AgentType) => {
    const toolConfig = jsonParse(agent?.toolConfig, {});
    const { simpleMode, debugMode } = toolConfig;
    if (isBoolean(simpleMode)) {
      setIsSimpleMode(simpleMode);
    } else {
      setIsSimpleMode(false);
    }
    if (isBoolean(debugMode)) {
      setIsDebugMode(debugMode);
    } else {
      setIsDebugMode(true);
    }
  };

  // 统一管理conversations数据，避免重复调用getAllConversations接口
  const updateConversations = async (agentId?: number) => {
    const { data } = await getAllConversations(agentId || currentAgent!.id);
    const conversationList = data || [];
    setConversations(conversationList.slice(0, 200));
    return conversationList;
  };

  const updateCurrentAgent = (agent?: AgentType) => {
    setCurrentAgent(agent);
    onCurrentAgentChange?.(agent);

    // 检测是否为LUI智能助理poc  
    const isLUI = agent?.name.includes('LUI');  
    setIsLUIAgent(isLUI);  
      
    localStorage.setItem('AGENT_ID', `${agent?.id}`);
    if (agent) {
      updateAgentConfigMode(agent);
    }
    if (!isCopilot) {
      window.history.replaceState({}, '', `${window.location.pathname}?agentId=${agent?.id}`);
    }

    if (isLUI) {  
      // 如果是LUI智能体，设置输入框居中，隐藏消息  
      setInputCentered(true);  
      setHasUserInput(false);  
      setMessageList([]); // 清空消息列表，不显示开场白  
    } else {  
      setInputCentered(false);  
      setHasUserInput(false);  
    }  
      
    if (agent.initialSendMsgParams) {  
      const { msg, modelId, filters } = agent.initialSendMsgParams;  
      onSendMsg(msg, [], modelId, { ...agent.initialSendMsgParams, filters });  
    }
  };

  const initAgentList = async () => {
    try {
      const res = await queryAgentList();
      const agentListValue = (res.data || []).filter(
        item => item.status === 1 && (agentIds === undefined || agentIds.includes(item.id))
      );
      setAgentList(agentListValue);
      if (agentListValue.length > 0) {
        const agentId = initialAgentId || localStorage.getItem('AGENT_ID');
        if (agentId) {
          const agent = agentListValue.find(item => item.id === +agentId);
          updateCurrentAgent(agent || agentListValue[0]);
        } else {
          updateCurrentAgent(agentListValue[0]);
        }
      }
    } catch (error) {
      console.error('Failed to load agent list:', error);
    } finally {
      // 无论成功还是失败，都取消loading
      setLoading(false);
    }
  };

  useEffect(() => {
    initAgentList();
  }, []);

  useEffect(() => {
    if (token) {
      setToken(token);
    }
  }, [token]);

  // 当currentAgent变化时，统一更新conversations数据
  useEffect(() => {
    if (currentAgent) {
      if(currentAgent.name.includes('LUI')){
        setAgentListVisible(false);
      }
      const initConversations = async () => {
        const conversationList = await updateConversations(currentAgent.id);
        // 如果有对话，选择第一个；如果没有，创建新对话
        if (conversationList.length > 0) {
          onSelectConversation(conversationList[0]);
        } else {
          // 这里需要调用创建新对话的逻辑
          agentListRef.current?.onAddConversation();
        }
      };

      if (currentAgent.initialSendMsgParams) {
        agentListRef.current?.onAddConversation(currentAgent.initialSendMsgParams);
      } else {
        initConversations();
      }
    }
  }, [currentAgent]);

  useEffect(() => {
    if (chatVisible) {
      inputFocus();
      updateMessageContainerScroll();
    }
  }, [chatVisible]);

  useEffect(() => {
    if (!currentConversation) {
      return;
    }
    const { initialMsgParams, isAdd } = currentConversation;
    if (isAdd) {
      inputFocus();
      if (initialMsgParams) {
        onSendMsg(initialMsgParams.msg, [], initialMsgParams.modelId, initialMsgParams);
        // 新对话发送消息后关闭loading
        setHistoryLoading(false);
        return;
      }
      sendHelloRsp();
      // 新对话显示欢迎消息后关闭loading
      setHistoryLoading(false);
      return;
    }
    updateHistoryMsg(1);
    setPageNo(1);
  }, [currentConversation]);

  const handleAutoScroll = () => {
    if (!messageContainerRef.current) return;
    const { scrollTop, scrollHeight, clientHeight } = messageContainerRef.current;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 50;
    setAutoScrollEnabled(isAtBottom);
  };

  // 增强的滚动更新函数
  const enhancedUpdateScroll = () => {
    updateMessageContainerScroll(); // 保持原有功能
    if (autoScrollEnabled && messageContainerRef.current) {
      const { scrollHeight, clientHeight } = messageContainerRef.current;
      if (scrollHeight > clientHeight) {
        messageContainerRef.current.scrollTo({
          top: scrollHeight,
          behavior: 'smooth'
        });
      }
    }
  }

  useEffect(() => {
    if (historyInited) {
      const messageContainerEle = document.getElementById('messageContainer');
      if (messageContainerEle) {
        // 使用重命名后的函数
        messageContainerEle.addEventListener('scroll', handleScrollForHistory);
        
        // 添加新的自动滚动监听
        messageContainerEle.addEventListener('scroll', handleAutoScroll);
      }
      return () => {
        if (messageContainerEle) {
          messageContainerEle.removeEventListener('scroll', handleScrollForHistory);
          messageContainerEle.removeEventListener('scroll', handleAutoScroll);
        }
      };
    }
    // if (historyInited) {
    //   const messageContainerEle = document.getElementById('messageContainer');
    //   messageContainerEle?.addEventListener('scroll', handleScrollForHistory );
    // }
    // return () => {
    //   const messageContainerEle = document.getElementById('messageContainer');
    //   messageContainerEle?.removeEventListener('scroll', handleScrollForHistory );
    // };
  }, [historyInited]);

  const sendHelloRsp = (agent?: AgentType) => {
    if (noInput) {
      return;
    }
    setMessageList([
      {
        id: uuid(),
        type: MessageTypeEnum.AGENT_LIST,
        msg: agent?.name || currentAgent?.name || agentList?.[0]?.name,
      },
    ]);
  };

  const convertHistoryMsg = (list: HistoryMsgItemType[]) => {
    return list.map((item: HistoryMsgItemType) => ({
      id: item.questionId,
      questionId: item.questionId,
      type: MessageTypeEnum.QUESTION,
      msg: item.queryText,
      parseInfos: item.parseInfos,
      parseTimeCost: item.parseTimeCost,
      msgData: { ...(item.queryResult || {}), similarQueries: item.similarQueries },
      score: item.score,
      agentId: currentAgent?.id,
    }));
  };

  const updateHistoryMsg = async (page: number) => {
    try {
      const res = await getHistoryMsg(page, currentConversation!.chatId, 3);
      const { hasNextPage, list } = res?.data || { hasNextPage: false, list: [] };
      const msgList = [...convertHistoryMsg(list), ...(page === 1 ? [] : messageList)];
      setMessageList(msgList);
      setHasNextPage(hasNextPage);
      if (page === 1) {
        if (list.length === 0) {
          sendHelloRsp();
        }
        updateMessageContainerScroll();
        setHistoryInited(true);
        inputFocus();
        // 只在第一页加载完成后关闭loading
        setHistoryLoading(false);
      } else {
        const msgEle = document.getElementById(`${messageList[0]?.id}`);
        msgEle?.scrollIntoView();
      }
    } catch (error) {
      console.error('Failed to load history messages:', error);
      // 如果是第一页加载失败，也要关闭loading
      if (page === 1) {
        setHistoryLoading(false);
      }
    }
  };

  const { run: handleScrollForHistory  } = useThrottleFn(
    e => {
      if (e.target.scrollTop === 0 && hasNextPage) {
        updateHistoryMsg(pageNo + 1);
        setPageNo(pageNo + 1);
      }
    },
    {
      leading: true,
      trailing: true,
      wait: 200,
    }
  );

  const inputFocus = () => {
    if (!isMobile) {
      chatFooterRef.current?.inputFocus();
    }
  };

  const inputBlur = () => {
    chatFooterRef.current?.inputBlur();
  };

  const onSendMsg = async (
    msg?: string,
    list?: MessageItem[],
    modelId?: number,
    sendMsgParams?: SendMsgParamsType,
    files?: any[]
  ) => {
    const currentMsg = msg || inputMsg;
    if (currentMsg.trim() === '') {
      setInputMsg('');
      return;
    }

    if (isLUIAgent && inputCentered && !hasUserInput) {  
      setInputCentered(false);  
      setHasUserInput(true);  
    } 


    const msgAgent = agentList.find(item => currentMsg.indexOf(item.name) === 1);
    const certainAgent = currentMsg[0] === '/' && msgAgent;
    const agentIdValue = certainAgent ? msgAgent.id : undefined;
    const agent = agentList.find(item => item.id === sendMsgParams?.agentId);

    if (agent || certainAgent) {
      updateCurrentAgent(agent || msgAgent);
    }
    const msgs = [
      ...(list || messageList),
      {
        id: uuid(),
        msg: currentMsg,
        msgValue: certainAgent
          ? currentMsg.replace(`/${certainAgent.name}`, '').trim()
          : currentMsg,
        modelId: modelId === -1 ? undefined : modelId,
        agentId: agent?.id || agentIdValue || currentAgent?.id,
        type: MessageTypeEnum.QUESTION,
        filters: sendMsgParams?.filters,
        files: files,
      },
    ];
    setMessageList(msgs);
    updateMessageContainerScroll();
    setInputMsg('');
  };

  const onInputMsgChange = (value: string) => {
    const inputMsgValue = value || '';
    setInputMsg(inputMsgValue);
  };

  const saveConversationToLocal = (conversation: ConversationDetailType) => {
    if (conversation) {
      if (conversation.chatId !== -1) {
        localStorage.setItem('CONVERSATION_ID', `${conversation.chatId}`);
      }
    } else {
      localStorage.removeItem('CONVERSATION_ID');
    }
  };

  const onSelectConversation = (
    conversation: ConversationDetailType,
    sendMsgParams?: SendMsgParamsType,
    isAdd?: boolean
  ) => {
    // 切换到不同对话时显示loading（包括新对话）
    if (currentConversation?.chatId !== conversation.chatId) {
      setHistoryLoading(true);
    }
    setCurrentConversation({
      ...conversation,
      initialMsgParams: sendMsgParams,
      isAdd,
    });
    saveConversationToLocal(conversation);
  };

  const onMsgDataLoaded = (
    data: MsgDataType,
    questionId: string | number,
    question: string,
    valid: boolean,
    isRefresh?: boolean
  ) => {
    onReportMsgEvent?.(question, valid);
    // 统一更新conversations数据，避免重复调用
    if (!isMobile) {
      updateConversations(currentAgent?.id);
    }
    if (!data) {
      return;
    }
    const msgs = cloneDeep(messageList);
    const msg = msgs.find(item => item.id === questionId);
    if (msg) {
      msg.msgData = data;
      setMessageList(msgs);
    }
    if (!isRefresh) {
      updateMessageContainerScroll(`${questionId}`);
    }
  };

  const onToggleHistoryVisible = () => {
    setHistoryVisible(!historyVisible);
  };

  const onAddConversation = () => {
    agentListRef.current?.onAddConversation();
    // conversationRef.current?.onAddConversation();
    inputFocus();
  };

  const onSelectAgent = (agent: AgentType) => {
    if (agent.id === currentAgent?.id) {
      return;
    }
    if (messageList.length === 1 && messageList[0].type === MessageTypeEnum.AGENT_LIST) {
      setMessageList([]);
    }
    updateCurrentAgent(agent);
    updateMessageContainerScroll();
  };

  const sendMsg = (msg: string, modelId?: number, files?: any[]) => {
    onSendMsg(msg, messageList, modelId, undefined, files);
    if (isMobile) {
      inputBlur();
    }
  };

  const onCloseConversation = () => {
    setHistoryVisible(false);
  };

  const chatClass = classNames(styles.chat, {
    [styles.mobile]: isMobile,
    [styles.historyVisible]: historyVisible,
  });

  return (
    <ConfigProvider locale={locale}>
      <StreamingProvider>
        <div className={chatClass}>
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100vh',
              width: '100%'
            }}>
              <Spin size="large">
                <div style={{ minHeight: '200px' }} />
              </Spin>
            </div>
          ) : (
            <div className={styles.chatSection}>
            {!isMobile && (
              <div className={styles.agentListContainer}>
                {/* {agentList.length > 1 && agentListVisible && ( */}
                {agentList.length > 0 && (
                  <div>
                    <AgentList
                      agentList={agentList}
                      currentAgent={currentAgent}
                      onSelectAgent={onSelectAgent}
                      currentConversation={currentConversation}
                      onSelectConversation={onSelectConversation}
                      visible={agentListVisible}
                      conversations={conversations}
                      updateConversations={updateConversations}
                      ref={agentListRef}
                    />
                    <div 
                      className={styles.agentListToggle}
                      onClick={() => {
                        if (isMobile) {
                          setMobileAgentsVisible(true);
                        } else {
                          setAgentListVisible(!agentListVisible);
                        }
                      }}
                    >
                      {agentListVisible ? (
                        <Tooltip title="收起" placement="right"><LeftOutlined /></Tooltip>) : 
                        (<Tooltip title="展开" placement="right"><RightOutlined /></Tooltip>)
                      }
                    </div>
                  </div>
                )}
              </div>
            )}
            <div className={styles.chatApp}>
              {currentConversation && (
                <div className={styles.chatBody}>
                  <div className={styles.chatContent}>
                    {/* {currentAgent && !isMobile && !noInput && (
                      <div className={styles.chatHeader}>
                        <Row style={{ width: '100%' }}>
                          <Col flex="1 1 200px">
                            <Space>
                              <div className={styles.chatHeaderTitle}>{currentAgent.name}</div>
                              <div className={styles.chatHeaderTip}>{currentAgent.description}</div>
                              <Tooltip title="精简模式下，问答结果将以文本形式输出">
                                <Switch
                                  key={currentAgent.id}
                                  style={{ position: 'relative', top: -1 }}
                                  size="small"
                                  value={isSimpleMode}
                                  checkedChildren="精简模式"
                                  unCheckedChildren="精简模式"
                                  onChange={checked => {
                                    setIsSimpleMode(checked);
                                  }}
                                />
                              </Tooltip>
                            </Space>
                          </Col>
                          <Col flex="0 1 118px"></Col>
                        </Row>
                      </div>
                    )} */}
                    {currentAgent?.enableMultiAgent === 1 ? (
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column',overflow: 'auto' }}>
                        <GenieUI
                          style={{ flex: 1 }}
                          showLogo={false}
                          // placeholder="请输入您的问题..."
                          serviceConfig={{
                            baseURL: '',
                            apiPrefix: '/web',
                            timeout: 10000,
                            headers: {
                              'Content-Type': 'application/json',
                            },
                          }}
                        />
                      </div>
                    ) : (
                        (!isLUIAgent || hasUserInput) ? 
                          <MessageContainer
                            ref={messageContainerRef}
                            onUpdateMessageScroll={enhancedUpdateScroll}
                            id="messageContainer"
                            isSimpleMode={isSimpleMode}
                            isDebugMode={isDebugMode}
                            messageList={messageList}
                            chatId={currentConversation?.chatId}
                            historyVisible={historyVisible}
                            historyLoading={historyLoading}
                            currentAgent={currentAgent}
                            chatVisible={chatVisible}
                            isDeveloper={isDeveloper}
                            integrateSystem={integrateSystem}
                            onMsgDataLoaded={onMsgDataLoaded}
                            onSendMsg={onSendMsg}
                          />
                        : null
                    )}
                    {!noInput && currentAgent?.enableMultiAgent !== 1 && (
                      <ChatFooter
                        inputMsg={inputMsg}
                        chatId={currentConversation?.chatId}
                        agentList={agentList}
                        currentAgent={currentAgent}
                        onToggleHistoryVisible={onToggleHistoryVisible}
                        onInputMsgChange={onInputMsgChange}
                        onSendMsg={sendMsg}
                        onAddConversation={onAddConversation}
                        onSelectAgent={onSelectAgent}
                        onOpenAgents={() => {
                          if (isMobile) {
                            setMobileAgentsVisible(true);
                          } else {
                            setAgentListVisible(!agentListVisible);
                          }
                        }}
                        onOpenShowcase={() => {
                          setShowCaseVisible(!showCaseVisible);
                        }}
                        ref={chatFooterRef}
                         // 传递新的props  
                        isLUIAgent={isLUIAgent}  
                        inputCentered={inputCentered}  
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
            <Conversation
              currentAgent={currentAgent}
              currentConversation={currentConversation}
              historyVisible={historyVisible}
              onSelectConversation={onSelectConversation}
              onCloseConversation={onCloseConversation}
              conversations={conversations}
              updateConversations={updateConversations}
              ref={conversationRef}
            />
            {currentAgent &&
              (isMobile ? (
                <Drawer
                  title="showcase"
                  placement="bottom"
                  height="95%"
                  open={showCaseVisible}
                  className={styles.showCaseDrawer}
                  destroyOnHidden
                  onClose={() => {
                    setShowCaseVisible(false);
                  }}
                >
                  <ShowCase agentId={currentAgent.id} onSendMsg={onSendMsg} />
                </Drawer>
              ) : (
                <Modal
                  title="showcase"
                  width="98%"
                  open={showCaseVisible}
                  centered
                  footer={null}
                  wrapClassName={styles.showCaseModal}
                  destroyOnHidden
                  onCancel={() => {
                    setShowCaseVisible(false);
                  }}
                >
                  <ShowCase
                    height="calc(100vh - 140px)"
                    agentId={currentAgent.id}
                    onSendMsg={onSendMsg}
                  />
                </Modal>
              ))}
          </div>
          )}
          <MobileAgents
            open={mobileAgentsVisible}
            agentList={agentList}
            currentAgent={currentAgent}
            onSelectAgent={onSelectAgent}
            onClose={() => {
              setMobileAgentsVisible(false);
            }}
          />
        </div>
      </StreamingProvider>
    </ConfigProvider>
  );
};

export default forwardRef(Chat);
