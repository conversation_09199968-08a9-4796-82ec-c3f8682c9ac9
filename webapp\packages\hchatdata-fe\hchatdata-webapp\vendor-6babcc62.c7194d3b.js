(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[97],{9504:function(Je,w,G){"use strict";var T=G(56974);T.ZP.version="4.8.23",w.ZP=T.ZP;var C="4.8.23"},3231:function(Je,w,G){"use strict";G.r(w),G.d(w,{Graph:function(){return _},GraphWithEvent:function(){return de},algorithm:function(){return T},comparision:function(){return x},essence:function(){return C},generate:function(){return M}});var T={};G.r(T),G.d(T,{components:function(){return pe},dfs:function(){return De},dijkstra:function(){return Xt},dijkstraAll:function(){return Z},findCycles:function(){return Oe},floydWarshall:function(){return cr},isAcyclic:function(){return Et},postorder:function(){return qt},preorder:function(){return $t},prim:function(){return se},tarjan:function(){return ie},topsort:function(){return st}});var C={};G.r(C),G.d(C,{hasSelfLoop:function(){return nr},isGraph:function(){return Wt},isNullGraph:function(){return tr},isSimpleGraph:function(){return Ot}});var x={};G.r(x),G.d(x,{containAllSameEdges:function(){return ye},containAllSameNodes:function(){return le},containSameEdges:function(){return fr},containSameNodes:function(){return er},getSameEdges:function(){return H},getSameNodes:function(){return W},isGraphComplement:function(){return ar},isGraphContainsAnother:function(){return Fe},isGraphOptionSame:function(){return X},isGraphSame:function(){return xe}});var M={};G.r(M),G.d(M,{getGraphComplement:function(){return rr}});var E;(function(ee){ee.DEFAULT_EDGE_NAME="\0",ee.GRAPH_NODE="\0",ee.EDGE_KEY_DELIM=""})(E||(E={}));function p(ee,L){var j=ee.get(L)||0;ee.set(L,j+1)}function u(ee,L){var j=ee.get(L);j!==void 0&&(j=j-1,j>0?ee.set(L,j):ee.delete(L))}function t(ee,L,j,te){var Q=String(L),oe=String(j);if(!ee&&Q>oe){var Ee=Q;Q=oe,oe=Ee}return Q+E.EDGE_KEY_DELIM+oe+E.EDGE_KEY_DELIM+(te===void 0?E.DEFAULT_EDGE_NAME:te)}function n(ee,L,j,te){var Q=String(L),oe=String(j),Ee={v:L,w:j};if(!ee&&Q>oe){var Re=Ee.v;Ee.v=Ee.w,Ee.w=Re}return te!==void 0&&(Ee.name=te),Ee}function r(ee,L){return t(ee,L.v,L.w,L.name)}function e(ee){return typeof ee=="function"}var o=function(L){return L.nodes().map(function(j){var te=L.node(j),Q=L.parent(j),oe={id:j,value:te,parent:Q};return oe.value===void 0&&delete oe.value,oe.parent===void 0&&delete oe.parent,oe})},a=function(L){return L.edges().map(function(j){var te=L.edge(j),Q={v:j.v,w:j.w,value:te,name:j.name};return Q.name===void 0&&delete Q.name,Q.value===void 0&&delete Q.value,Q})},i=function(L){var j={options:{directed:L.isDirected(),multigraph:L.isMultigraph(),compound:L.isCompound()},nodes:o(L),edges:a(L),value:L.graph()};return j.value===void 0&&delete j.value,j},f=function(L){var j=new _(L.options);return L.value!==void 0&&j.setGraph(L.value),L.nodes.forEach(function(te){j.setNode(te.id,te.value),te.parent&&j.setParent(te.id,te.parent)}),L.edges.forEach(function(te){j.setEdge(te.v,te.w,te.value,te.name)}),j};function s(ee,L){var j=Object.keys(ee);if(Object.getOwnPropertySymbols){var te=Object.getOwnPropertySymbols(ee);L&&(te=te.filter(function(Q){return Object.getOwnPropertyDescriptor(ee,Q).enumerable})),j.push.apply(j,te)}return j}function l(ee){for(var L=1;L<arguments.length;L++){var j=arguments[L]!=null?arguments[L]:{};L%2?s(Object(j),!0).forEach(function(te){m(ee,te,j[te])}):Object.getOwnPropertyDescriptors?Object.defineProperties(ee,Object.getOwnPropertyDescriptors(j)):s(Object(j)).forEach(function(te){Object.defineProperty(ee,te,Object.getOwnPropertyDescriptor(j,te))})}return ee}function m(ee,L,j){return L in ee?Object.defineProperty(ee,L,{value:j,enumerable:!0,configurable:!0,writable:!0}):ee[L]=j,ee}function h(ee,L){if(!(ee instanceof L))throw new TypeError("Cannot call a class as a function")}function d(ee,L){for(var j=0;j<L.length;j++){var te=L[j];te.enumerable=te.enumerable||!1,te.configurable=!0,"value"in te&&(te.writable=!0),Object.defineProperty(ee,te.key,te)}}function v(ee,L,j){return L&&d(ee.prototype,L),j&&d(ee,j),Object.defineProperty(ee,"prototype",{writable:!1}),ee}var y={compound:!1,multigraph:!1,directed:!0},_=function(){function ee(){var L=this,j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};h(this,ee),this.directed=!0,this.multigraph=!1,this.compound=!1,this.GRAPH_NODE=E.GRAPH_NODE,this.label=void 0,this.nodeCountNum=0,this.edgeCountNum=0,this.defaultNodeLabelFn=function(){},this.defaultEdgeLabelFn=function(){},this.parentMap=void 0,this.childrenMap=void 0,this.nodesLabelMap=new Map,this.inEdgesMap=new Map,this.outEdgesMap=new Map,this.predecessorsMap=new Map,this.successorsMap=new Map,this.edgesMap=new Map,this.edgesLabelsMap=new Map,this.isDirected=function(){return L.directed},this.isMultigraph=function(){return L.multigraph},this.isCompound=function(){return L.compound},this.setGraph=function(Q){return L.label=Q,L},this.graph=function(){return L.label},this.setDefaultNodeLabel=function(Q){return e(Q)?L.defaultNodeLabelFn=Q:L.defaultNodeLabelFn=function(){return Q},L},this.nodeCount=function(){return L.nodeCountNum},this.node=function(Q){return L.nodesLabelMap.get(Q)},this.nodes=function(){return Array.from(L.nodesLabelMap.keys())},this.sources=function(){return L.nodes().filter(function(Q){var oe;return!(!((oe=L.inEdgesMap.get(Q))===null||oe===void 0)&&oe.size)})},this.sinks=function(){return L.nodes().filter(function(Q){var oe;return!(!((oe=L.outEdgesMap.get(Q))===null||oe===void 0)&&oe.size)})},this.setNodes=function(Q,oe){return Q.map(function(Ee){return L.setNode(Ee,oe)}),L},this.hasNode=function(Q){return L.nodesLabelMap.has(Q)},this.checkCompound=function(){if(!L.isCompound())throw new Error("Cannot construct parent-children relations in a non-compound graph")},this.parent=function(Q){if(L.isCompound()){var oe,Ee=(oe=L.parentMap)===null||oe===void 0?void 0:oe.get(Q);if(Ee!==L.GRAPH_NODE)return Ee}},this.removeFromParentsChildList=function(Q){var oe=L.parentMap.get(Q);L.childrenMap.get(oe).delete(Q)},this.setParent=function(Q,oe){var Ee,Re;L.checkCompound();for(var Xe=oe===void 0?L.GRAPH_NODE:oe,ft=L.parent(Xe);ft;){if(Q===ft)throw new Error("Setting "+oe+" as parent of "+Q+" would create a cycle");ft=L.parent(ft)}oe&&L.setNode(oe),L.setNode(Q),L.removeFromParentsChildList(Q),(Ee=L.parentMap)===null||Ee===void 0||Ee.set(Q,Xe);var dt=L.childrenMap.get(Xe);return dt.set(Q,!0),(Re=L.childrenMap)===null||Re===void 0||Re.set(Xe,dt),L},this.children=function(Q){var oe=Q===void 0?L.GRAPH_NODE:Q;if(L.isCompound()){var Ee,Re=(Ee=L.childrenMap)===null||Ee===void 0?void 0:Ee.get(oe);return Re?Array.from(Re.keys()):void 0}if(oe===L.GRAPH_NODE)return L.nodes();if(Q&&L.hasNode(Q))return[]},this.predecessors=function(Q){var oe=L.predecessorsMap.get(Q);return oe?Array.from(oe.keys()):void 0},this.successors=function(Q){var oe=L.successorsMap.get(Q);return oe?Array.from(oe.keys()):void 0},this.neighbors=function(Q){var oe;if(L.hasNode(Q))return Array.from(new Set((oe=L.predecessors(Q))===null||oe===void 0?void 0:oe.concat(L.successors(Q))))},this.isLeaf=function(Q){var oe;if(L.isDirected()){var Ee;return!(!((Ee=L.successors(Q))===null||Ee===void 0)&&Ee.length)}return!(!((oe=L.neighbors(Q))===null||oe===void 0)&&oe.length)},this.filterNodes=function(Q){var oe=L.directed,Ee=L.multigraph,Re=L.compound,Xe=new ee({directed:oe,multigraph:Ee,compound:Re});if(Xe.setGraph(L.graph()),L.nodes().forEach(function(dt){Q(dt)&&Xe.setNode(dt,L.node(dt))}),L.edges().forEach(function(dt){Xe.hasNode(dt.v)&&Xe.hasNode(dt.w)&&Xe.setEdgeObj(dt,L.edge(dt))}),Re){var ft=function(wt){for(var It=L.parent(wt);It!==void 0&&!Xe.hasNode(It);)It=L.parent(It);return It};Xe.nodes().forEach(function(dt){Xe.setParent(dt,ft(dt))})}return Xe},this.setDefaultEdgeLabel=function(Q){return e(Q)?L.defaultEdgeLabelFn=Q:L.defaultEdgeLabelFn=function(){return Q},L},this.edgeCount=function(){return L.edgeCountNum},this.setEdgeObj=function(Q,oe){return L.setEdge(Q.v,Q.w,oe,Q.name)},this.setPath=function(Q,oe){return Q.reduce(function(Ee,Re){return L.setEdge(Ee,Re,oe),Re}),L},this.edgeFromArgs=function(Q,oe,Ee){return L.edge({v:Q,w:oe,name:Ee})},this.edge=function(Q){return L.edgesLabelsMap.get(r(L.isDirected(),Q))},this.hasEdge=function(Q,oe,Ee){return L.edgesLabelsMap.has(r(L.isDirected(),{v:Q,w:oe,name:Ee}))},this.removeEdgeObj=function(Q){var oe=Q.v,Ee=Q.w,Re=Q.name;return L.removeEdge(oe,Ee,Re)},this.edges=function(){return Array.from(L.edgesMap.values())},this.inEdges=function(Q,oe){var Ee=L.inEdgesMap.get(Q);if(Ee)return Array.from(Ee.values()).filter(function(Re){return!oe||Re.v===oe})},this.outEdges=function(Q,oe){var Ee=L.outEdgesMap.get(Q);if(Ee)return Array.from(Ee.values()).filter(function(Re){return!oe||Re.w===oe})},this.nodeEdges=function(Q,oe){var Ee;if(L.hasNode(Q))return(Ee=L.inEdges(Q,oe))===null||Ee===void 0?void 0:Ee.concat(L.outEdges(Q,oe))},this.toJSON=function(){return i(L)},this.nodeInDegree=function(Q){var oe=L.inEdgesMap.get(Q);return oe?oe.size:0},this.nodeOutDegree=function(Q){var oe=L.outEdgesMap.get(Q);return oe?oe.size:0},this.nodeDegree=function(Q){return L.nodeInDegree(Q)+L.nodeOutDegree(Q)},this.source=function(Q){return Q.v},this.target=function(Q){return Q.w};var te=l(l({},y),j);this.compound=te.compound,this.directed=te.directed,this.multigraph=te.multigraph,this.compound&&(this.parentMap=new Map,this.childrenMap=new Map)}return v(ee,[{key:"setNode",value:function(j,te){var Q=this.nodesLabelMap,oe=this.defaultNodeLabelFn,Ee=this.isCompound,Re=this.parentMap,Xe=this.childrenMap,ft=this.inEdgesMap,dt=this.outEdgesMap,wt=this.predecessorsMap,It=this.successorsMap;if(Q.has(j))return te!==void 0&&Q.set(j,te),this;if(Q.set(j,te||oe(j)),Ee()){var At;Re==null||Re.set(j,this.GRAPH_NODE),Xe==null||Xe.set(j,new Map),Xe!=null&&Xe.has(this.GRAPH_NODE)||Xe==null||Xe.set(this.GRAPH_NODE,new Map),Xe==null||(At=Xe.get(this.GRAPH_NODE))===null||At===void 0||At.set(j,!0)}return[ft,dt,wt,It].forEach(function(Ut){return Ut.set(j,new Map)}),this.nodeCountNum+=1,this}},{key:"removeNode",value:function(j){var te=this;if(this.hasNode(j)){var Q=function(Qt){te.removeEdge(Qt.v,Qt.w,Qt.name)},oe=this.inEdgesMap,Ee=this.outEdgesMap,Re=this.predecessorsMap,Xe=this.successorsMap,ft=this.nodesLabelMap;if(this.isCompound()){var dt,wt,It;this.removeFromParentsChildList(j),(dt=this.parentMap)===null||dt===void 0||dt.delete(j),(wt=this.children(j))===null||wt===void 0||wt.forEach(function(Bt){return te.setParent(Bt)}),(It=this.childrenMap)===null||It===void 0||It.delete(j)}var At=oe.get(j),Ut=Ee.get(j);Array.from(At.values()).forEach(function(Bt){return Q(Bt)}),Array.from(Ut.values()).forEach(function(Bt){return Q(Bt)}),ft.delete(j),oe.delete(j),Ee.delete(j),Re.delete(j),Xe.delete(j),this.nodeCountNum-=1}return this}},{key:"setEdge",value:function(j,te,Q,oe){var Ee,Re,Xe=n(this.isDirected(),j,te,oe),ft=r(this.isDirected(),Xe),dt=Xe.v,wt=Xe.w;if(this.edgesLabelsMap.has(ft))return this.edgesLabelsMap.set(ft,Q),this;if(oe!==void 0&&!this.isMultigraph())throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(dt),this.setNode(wt),this.edgesLabelsMap.set(ft,Q||this.defaultEdgeLabelFn(dt,wt,oe)),Object.freeze(Xe),this.edgesMap.set(ft,Xe);var It=this.predecessorsMap.get(wt),At=this.successorsMap.get(dt);return p(It,dt),p(At,wt),(Ee=this.inEdgesMap.get(wt))===null||Ee===void 0||Ee.set(ft,Xe),(Re=this.outEdgesMap.get(dt))===null||Re===void 0||Re.set(ft,Xe),this.edgeCountNum+=1,this}},{key:"removeEdge",value:function(j,te,Q){var oe=t(this.isDirected(),j,te,Q),Ee=this.edgesMap.get(oe);if(Ee){var Re=n(this.isDirected(),j,te,Q),Xe=Re.v,ft=Re.w;this.edgesLabelsMap.delete(oe),this.edgesMap.delete(oe);var dt=this.predecessorsMap.get(ft),wt=this.successorsMap.get(Xe);u(dt,Xe),u(wt,ft),this.inEdgesMap.get(ft).delete(oe),this.outEdgesMap.get(Xe).delete(oe),this.edgeCountNum-=1}return this}}]),ee}();_.fromJSON=f;function D(ee){"@babel/helpers - typeof";return D=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(L){return typeof L}:function(L){return L&&typeof Symbol=="function"&&L.constructor===Symbol&&L!==Symbol.prototype?"symbol":typeof L},D(ee)}function b(ee,L){if(!(ee instanceof L))throw new TypeError("Cannot call a class as a function")}function S(ee,L){for(var j=0;j<L.length;j++){var te=L[j];te.enumerable=te.enumerable||!1,te.configurable=!0,"value"in te&&(te.writable=!0),Object.defineProperty(ee,te.key,te)}}function k(ee,L,j){return L&&S(ee.prototype,L),j&&S(ee,j),Object.defineProperty(ee,"prototype",{writable:!1}),ee}function A(){return typeof Reflect!="undefined"&&Reflect.get?A=Reflect.get:A=function(L,j,te){var Q=K(L,j);if(Q){var oe=Object.getOwnPropertyDescriptor(Q,j);return oe.get?oe.get.call(arguments.length<3?L:te):oe.value}},A.apply(this,arguments)}function K(ee,L){for(;!Object.prototype.hasOwnProperty.call(ee,L)&&(ee=ae(ee),ee!==null););return ee}function R(ee,L){if(typeof L!="function"&&L!==null)throw new TypeError("Super expression must either be null or a function");ee.prototype=Object.create(L&&L.prototype,{constructor:{value:ee,writable:!0,configurable:!0}}),Object.defineProperty(ee,"prototype",{writable:!1}),L&&P(ee,L)}function P(ee,L){return P=Object.setPrototypeOf||function(te,Q){return te.__proto__=Q,te},P(ee,L)}function N(ee){var L=ue();return function(){var te=ae(ee),Q;if(L){var oe=ae(this).constructor;Q=Reflect.construct(te,arguments,oe)}else Q=te.apply(this,arguments);return U(this,Q)}}function U(ee,L){if(L&&(D(L)==="object"||typeof L=="function"))return L;if(L!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return B(ee)}function B(ee){if(ee===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return ee}function ue(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(ee){return!1}}function ae(ee){return ae=Object.setPrototypeOf?Object.getPrototypeOf:function(j){return j.__proto__||Object.getPrototypeOf(j)},ae(ee)}var de=function(ee){R(j,ee);var L=N(j);function j(){var te;b(this,j);for(var Q=arguments.length,oe=new Array(Q),Ee=0;Ee<Q;Ee++)oe[Ee]=arguments[Ee];return te=L.call.apply(L,[this].concat(oe)),te.eventPool={},te}return k(j,[{key:"appendEvent",value:function(Q,oe){this.eventPool[Q]||(this.eventPool[Q]=[]),this.eventPool[Q].push(oe)}},{key:"removeEvent",value:function(Q,oe){if(this.eventPool[Q]){var Ee=this.eventPool[Q].indexOf(oe);Ee>-1&&this.eventPool[Q].splice(Ee,1)}}},{key:"emitEvent",value:function(Q){for(var oe=arguments.length,Ee=new Array(oe>1?oe-1:0),Re=1;Re<oe;Re++)Ee[Re-1]=arguments[Re];this.eventPool[Q]&&this.eventPool[Q].forEach(function(Xe){Xe.apply(void 0,Ee)})}},{key:"setNode",value:function(Q,oe){return A(ae(j.prototype),"setNode",this).call(this,Q,oe),this.emitEvent("nodeAdd",Q,oe),this}},{key:"removeNode",value:function(Q){return A(ae(j.prototype),"removeNode",this).call(this,Q),this.emitEvent("nodeRemove",Q),this}},{key:"setEdge",value:function(Q,oe,Ee,Re){return A(ae(j.prototype),"setEdge",this).call(this,Q,oe,Ee,Re),this.emitEvent("edgeAdd",Q,oe,Ee,Re),this}},{key:"removeEdge",value:function(Q,oe,Ee){return A(ae(j.prototype),"removeEdge",this).call(this,Q,oe,Ee),this.emitEvent("edgeRemove",Q,oe,Ee),this}}]),j}(_);function ve(ee,L){for(var j=0;j<L.length;j++){var te=L[j];te.enumerable=te.enumerable||!1,te.configurable=!0,"value"in te&&(te.writable=!0),Object.defineProperty(ee,te.key,te)}}function we(ee,L,j){return L&&ve(ee.prototype,L),j&&ve(ee,j),Object.defineProperty(ee,"prototype",{writable:!1}),ee}function Se(ee,L){if(!(ee instanceof L))throw new TypeError("Cannot call a class as a function")}var Le=we(function ee(){var L=this;Se(this,ee),this.arr=[],this.keyIndice=new Map,this.size=function(){return L.arr.length},this.keys=function(){return L.arr.map(function(j){return j.key})},this.has=function(j){return L.keyIndice.has(j)},this.priority=function(j){var te=L.keyIndice.get(j);if(te!==void 0)return L.arr[te].priority},this.swap=function(j,te){var Q=L.arr,oe=L.keyIndice,Ee=[Q[j],Q[te]],Re=Ee[0],Xe=Ee[1];Q[j]=Xe,Q[te]=Re,oe.set(Re.key,te),oe.set(Xe.key,j)},this.innerDecrease=function(j){for(var te=L.arr,Q=te[j].priority,oe,Ee=j;Ee!==0;){var Re;if(oe=Ee>>1,((Re=te[oe])===null||Re===void 0?void 0:Re.priority)<Q)break;L.swap(Ee,oe),Ee=oe}},this.heapify=function(j){var te=L.arr,Q=j<<1,oe=Q+1,Ee=j;Q<te.length&&(Ee=te[Q].priority<te[Ee].priority?Q:Ee,oe<te.length&&(Ee=te[oe].priority<te[Ee].priority?oe:Ee),Ee!==j&&(L.swap(j,Ee),L.heapify(Ee)))},this.min=function(){if(L.size()===0)throw new Error("Queue underflow");return L.arr[0].key},this.add=function(j,te){var Q=L.keyIndice,oe=L.arr;if(!Q.has(j)){var Ee=oe.length;return Q.set(j,Ee),oe.push({key:j,priority:te}),L.innerDecrease(Ee),!0}return!1},this.removeMin=function(){L.swap(0,L.arr.length-1);var j=L.arr.pop();return L.keyIndice.delete(j.key),L.heapify(0),j.key},this.decrease=function(j,te){if(!L.has(j))throw new Error("There's no key named ".concat(j));var Q=L.keyIndice.get(j);if(te>L.arr[Q].priority)throw new Error("New priority is greater than current priority.Key: ".concat(j," Old: + ").concat(L.arr[Q].priority," New: ").concat(te));L.arr[Q].priority=te,L.innerDecrease(Q)}}),q=function(L,j){var te=new _,Q=new Map,oe=new Le,Ee;function Re(dt){var wt=dt.v===Ee?dt.w:dt.v,It=oe.priority(wt);if(It!==void 0){var At=j(dt);At<It&&(Q.set(wt,Ee),oe.decrease(wt,At))}}if(L.nodeCount()===0)return te;L.nodes().forEach(function(dt){oe.add(dt,Number.POSITIVE_INFINITY),te.setNode(dt)}),oe.decrease(L.nodes()[0],0);for(var Xe=!1;oe.size()>0;){var ft;if(Ee=oe.removeMin(),Q.has(Ee))te.setEdge(Ee,Q.get(Ee));else{if(Xe)throw new Error("Input graph is not connected: "+L.graph());Xe=!0}(ft=L.nodeEdges(Ee))===null||ft===void 0||ft.forEach(Re)}return te},se=q,ne=function(L){var j=new Set,te=[],Q=L.nodes();return Q.forEach(function(oe){for(var Ee=[],Re=[oe];Re.length>0;){var Xe=Re.pop();if(!j.has(Xe)){var ft,dt;j.add(Xe),Ee.push(Xe),(ft=L.successors(Xe))===null||ft===void 0||ft.forEach(function(wt){return Re.push(wt)}),(dt=L.predecessors(Xe))===null||dt===void 0||dt.forEach(function(wt){return Re.push(wt)})}}Ee.length&&te.push(Ee)}),te},pe=ne,Me=function ee(L,j,te,Q,oe,Ee){Q.includes(j)||(Q.push(j),te||Ee.push(j),oe(j).forEach(function(Re){return ee(L,Re,te,Q,oe,Ee)}),te&&Ee.push(j))},he=function(L,j,te){var Q=Array.isArray(j)?j:[j],oe=function(ft){return L.isDirected()?L.successors(ft):L.neighbors(ft)},Ee=[],Re=[];return Q.forEach(function(Xe){if(L.hasNode(Xe))Me(L,Xe,te==="post",Re,oe,Ee);else throw new Error("Graph does not have node: "+Xe)}),Ee},De=he;function Pe(ee,L){return pt(ee)||tt(ee,L)||Ze(ee,L)||He()}function He(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ze(ee,L){if(ee){if(typeof ee=="string")return Ue(ee,L);var j=Object.prototype.toString.call(ee).slice(8,-1);if(j==="Object"&&ee.constructor&&(j=ee.constructor.name),j==="Map"||j==="Set")return Array.from(ee);if(j==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(j))return Ue(ee,L)}}function Ue(ee,L){(L==null||L>ee.length)&&(L=ee.length);for(var j=0,te=new Array(L);j<L;j++)te[j]=ee[j];return te}function tt(ee,L){var j=ee==null?null:typeof Symbol!="undefined"&&ee[Symbol.iterator]||ee["@@iterator"];if(j!=null){var te=[],Q=!0,oe=!1,Ee,Re;try{for(j=j.call(ee);!(Q=(Ee=j.next()).done)&&(te.push(Ee.value),!(L&&te.length===L));Q=!0);}catch(Xe){oe=!0,Re=Xe}finally{try{!Q&&j.return!=null&&j.return()}finally{if(oe)throw Re}}return te}}function pt(ee){if(Array.isArray(ee))return ee}var Mt=function(){return 1},jt=function(L,j,te,Q){return Ct(L,j,te||Mt,Q||function(oe){return L.outEdges(oe)})},Ct=function(L,j,te,Q){var oe=new Map,Ee=new Le,Re,Xe,ft=function(It){var At=It.v!==Re?It.v:It.w,Ut=oe.get(At),Bt=te(It),Qt=Xe.distance+Bt;if(Bt<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+It+" Weight: "+Bt);Qt<Ut.distance&&(Ut.distance=Qt,Ut.predecessor=Re,Ee.decrease(At,Qt))};for(L.nodes().forEach(function(wt){var It=wt===j?0:Number.POSITIVE_INFINITY;oe.set(wt,{distance:It}),Ee.add(wt,It)});Ee.size()>0&&(Re=Ee.removeMin(),Xe=oe.get(Re),!(Xe&&Xe.distance===Number.POSITIVE_INFINITY));)Q(Re).forEach(ft);var dt={};return Array.from(oe.entries()).forEach(function(wt){var It=Pe(wt,2),At=It[0],Ut=It[1];return dt[String(At)]=Ut,dt}),dt},Xt=jt,Jt=function(L,j,te){var Q={};return L.nodes().forEach(function(oe){return Q[String(oe)]=Xt(L,oe,j,te),Q}),Q},Z=Jt,J=function(L){var j=0,te=[],Q=new Map,oe=[];function Ee(Re){var Xe,ft={onStack:!0,lowlink:j,index:j};if(Q.set(Re,ft),j+=1,te.push(Re),(Xe=L.successors(Re))===null||Xe===void 0||Xe.forEach(function(At){var Ut;if(Q.has(At)){if(!((Ut=Q.get(At))===null||Ut===void 0)&&Ut.onStack){var Qt=Q.get(At);ft.lowlink=Math.min(ft.lowlink,Qt.index)}}else{Ee(At);var Bt=Q.get(At);ft.lowlink=Math.min(ft.lowlink,Bt.lowlink)}}),ft.lowlink===ft.index){var dt=[],wt;do{wt=te.pop();var It=Q.get(wt);It.onStack=!1,dt.push(wt)}while(Re!==wt);oe.push(dt)}}return L.nodes().forEach(function(Re){Q.has(Re)||Ee(Re)}),oe},ie=J,me=function(L){return ie(L).filter(function(j){return j.length>1||j.length===1&&L.hasEdge(j[0],j[0])})},Oe=me;function Ce(ee){"@babel/helpers - typeof";return Ce=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(L){return typeof L}:function(L){return L&&typeof Symbol=="function"&&L.constructor===Symbol&&L!==Symbol.prototype?"symbol":typeof L},Ce(ee)}function Ne(ee,L){for(var j=0;j<L.length;j++){var te=L[j];te.enumerable=te.enumerable||!1,te.configurable=!0,"value"in te&&(te.writable=!0),Object.defineProperty(ee,te.key,te)}}function Y(ee,L,j){return L&&Ne(ee.prototype,L),j&&Ne(ee,j),Object.defineProperty(ee,"prototype",{writable:!1}),ee}function $(ee,L){if(!(ee instanceof L))throw new TypeError("Cannot call a class as a function")}function Ie(ee,L){if(typeof L!="function"&&L!==null)throw new TypeError("Super expression must either be null or a function");ee.prototype=Object.create(L&&L.prototype,{constructor:{value:ee,writable:!0,configurable:!0}}),Object.defineProperty(ee,"prototype",{writable:!1}),L&&je(ee,L)}function _e(ee){var L=Ve();return function(){var te=at(ee),Q;if(L){var oe=at(this).constructor;Q=Reflect.construct(te,arguments,oe)}else Q=te.apply(this,arguments);return Ae(this,Q)}}function Ae(ee,L){if(L&&(Ce(L)==="object"||typeof L=="function"))return L;if(L!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Be(ee)}function Be(ee){if(ee===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return ee}function ze(ee){var L=typeof Map=="function"?new Map:void 0;return ze=function(te){if(te===null||!qe(te))return te;if(typeof te!="function")throw new TypeError("Super expression must either be null or a function");if(typeof L!="undefined"){if(L.has(te))return L.get(te);L.set(te,Q)}function Q(){return Ye(te,arguments,at(this).constructor)}return Q.prototype=Object.create(te.prototype,{constructor:{value:Q,enumerable:!1,writable:!0,configurable:!0}}),je(Q,te)},ze(ee)}function Ye(ee,L,j){return Ve()?Ye=Reflect.construct:Ye=function(Q,oe,Ee){var Re=[null];Re.push.apply(Re,oe);var Xe=Function.bind.apply(Q,Re),ft=new Xe;return Ee&&je(ft,Ee.prototype),ft},Ye.apply(null,arguments)}function Ve(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(ee){return!1}}function qe(ee){return Function.toString.call(ee).indexOf("[native code]")!==-1}function je(ee,L){return je=Object.setPrototypeOf||function(te,Q){return te.__proto__=Q,te},je(ee,L)}function at(ee){return at=Object.setPrototypeOf?Object.getPrototypeOf:function(j){return j.__proto__||Object.getPrototypeOf(j)},at(ee)}var vt=function(ee){Ie(j,ee);var L=_e(j);function j(){return $(this,j),L.apply(this,arguments)}return Y(j)}(ze(Error));function ct(ee){var L=new Set,j=new Set,te=[];function Q(oe){if(j.has(oe))throw new vt;if(!L.has(oe)){var Ee;j.add(oe),L.add(oe),(Ee=ee.predecessors(oe))===null||Ee===void 0||Ee.forEach(Q),j.delete(oe),te.push(oe)}}if(ee.sinks().forEach(Q),L.size!==ee.nodeCount())throw new vt;return te}var st=ct,gt=function(L){try{st(L)}catch(j){if(j instanceof vt)return!1;throw j}return!0},Et=gt,Vt=function(L,j){return De(L,j,"post")},qt=Vt,Kt=function(L,j){return De(L,j,"pre")},$t=Kt,or=function(){return 1};function Zt(ee,L,j){return Rt(ee,L||or,j||function(te){return ee.outEdges(te)})}function Rt(ee,L,j){var te={},Q=ee.nodes();return Q.forEach(function(oe){var Ee=String(oe);te[Ee]={},te[Ee][Ee]={distance:0},Q.forEach(function(Re){oe!==Re&&(te[Ee][String(Re)]={distance:Number.POSITIVE_INFINITY})}),j(oe).forEach(function(Re){var Xe=Re.v===oe?Re.w:Re.v,ft=L(Re);te[Ee][String(Xe)]={distance:ft,predecessor:oe}})}),Q.forEach(function(oe){var Ee=String(oe),Re=te[Ee];Q.forEach(function(Xe){var ft=String(Xe),dt=te[ft];Q.forEach(function(wt){var It=String(wt),At=dt[Ee],Ut=Re[It],Bt=dt[It],Qt=At.distance+Ut.distance;Qt<Bt.distance&&(Bt.distance=Qt,Bt.predecessor=Ut.predecessor)})})}),te}var cr=Zt,er=function(L,j){for(var te=L.nodes(),Q=0;Q<te.length;Q++){var oe=te[Q];if(j.hasNode(oe))return!0}return!1},fr=function(L,j){for(var te=L.edges(),Q=0;Q<te.length;Q++){var oe=te[Q];if(j.hasEdge(oe.v,oe.w,oe.name))return!0}return!1},W=function(L,j){var te=L.nodes(),Q=te.filter(function(oe){return j.hasNode(oe)});return Q},H=function(L,j){var te=L.edges(),Q=te.filter(function(oe){return j.hasEdge(oe.v,oe.w,oe.name)});return Q},X=function(L,j){return L.isCompound()===j.isCompound()&&L.isDirected()===j.isDirected()&&L.isMultigraph()===j.isMultigraph()},le=function(L,j){var te=W(L,j);return te.length===L.nodes().length},ye=function(L,j){var te=H(L,j);return te.length===L.edges().length},xe=function(L,j){return X(L,j)&&L.nodeCount()===j.nodeCount()&&le(L,j)&&L.edgeCount()===j.edgeCount()&&ye(L,j)},Fe=function(L,j){return le(L,j)&&ye(L,j)};function We(ee,L){return Gt(ee)||kt(ee,L)||Qe(ee,L)||it()}function it(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Qe(ee,L){if(ee){if(typeof ee=="string")return rt(ee,L);var j=Object.prototype.toString.call(ee).slice(8,-1);if(j==="Object"&&ee.constructor&&(j=ee.constructor.name),j==="Map"||j==="Set")return Array.from(ee);if(j==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(j))return rt(ee,L)}}function rt(ee,L){(L==null||L>ee.length)&&(L=ee.length);for(var j=0,te=new Array(L);j<L;j++)te[j]=ee[j];return te}function kt(ee,L){var j=ee==null?null:typeof Symbol!="undefined"&&ee[Symbol.iterator]||ee["@@iterator"];if(j!=null){var te=[],Q=!0,oe=!1,Ee,Re;try{for(j=j.call(ee);!(Q=(Ee=j.next()).done)&&(te.push(Ee.value),!(L&&te.length===L));Q=!0);}catch(Xe){oe=!0,Re=Xe}finally{try{!Q&&j.return!=null&&j.return()}finally{if(oe)throw Re}}return te}}function Gt(ee){if(Array.isArray(ee))return ee}function Wt(ee){return ee instanceof _}function Ot(ee){if(ee.isMultigraph())return!1;for(var L=ee.edges(),j=new Map,te=0;te<L.length;te++){var Q=L[te];if(Q.v===Q.w)return!1;var oe=[Q.v,Q.w].sort(),Ee=We(oe,2),Re=Ee[0],Xe=Ee[1],ft="".concat(Re,"-").concat(Xe);if(j.has(ft))return!1;j.set(ft,!0)}return!0}function tr(ee){return ee.nodes().length===0}function nr(ee){for(var L=ee.edges(),j=0;j<L.length;j++){var te=L[j];if(te.v===te.w)return!0}return!1}var ar=function(L,j){if(!Ot(L)||!Ot(j)||!le(L,j)||fr(L,j))return!1;var te=L.nodeCount();return L.edgeCount()+j.edgeCount()===te*(te-1)/2},rr=function(L){if(!Ot(L))return null;for(var j=L.nodeCount(),te=new _({compound:L.isCompound(),directed:L.isDirected(),multigraph:L.isMultigraph()}),Q=L.nodes(),oe=0;oe<j;oe++){var Ee=Q[oe];te.setNode(Ee,L.node(Ee));for(var Re=oe+1;Re<j;Re++){var Xe=Q[Re];L.hasEdge(Ee,Xe)||te.setEdge(Ee,Xe)}}return te}},97291:function(Je){(function(G,T){Je.exports=T()})(typeof self!="undefined"?self:this,function(){return function(w){var G={};function T(C){if(G[C])return G[C].exports;var x=G[C]={i:C,l:!1,exports:{}};return w[C].call(x.exports,x,x.exports,T),x.l=!0,x.exports}return T.m=w,T.c=G,T.d=function(C,x,M){T.o(C,x)||Object.defineProperty(C,x,{configurable:!1,enumerable:!0,get:M})},T.n=function(C){var x=C&&C.__esModule?function(){return C.default}:function(){return C};return T.d(x,"a",x),x},T.o=function(C,x){return Object.prototype.hasOwnProperty.call(C,x)},T.p="",T(T.s=5)}([function(w,G){function T(C,x,M,E){return E===void 0&&(E="height"),M==="center"?(C[E]+x[E])/2:C.height}w.exports={assign:Object.assign,getHeight:T}},function(w,G,T){var C=T(3),x=function(){function M(p,u){u===void 0&&(u={});var t=this;t.options=u,t.rootNode=C(p,u)}var E=M.prototype;return E.execute=function(){throw new Error("please override this method")},M}();w.exports=x},function(w,G,T){var C=T(4),x=["LR","RL","TB","BT","H","V"],M=["LR","RL","H"],E=function(n){return M.indexOf(n)>-1},p=x[0];w.exports=function(t,n,r){var e=n.direction||p;if(n.isHorizontal=E(e),e&&x.indexOf(e)===-1)throw new TypeError("Invalid direction: "+e);if(e===x[0])r(t,n);else if(e===x[1])r(t,n),t.right2left();else if(e===x[2])r(t,n);else if(e===x[3])r(t,n),t.bottom2top();else if(e===x[4]||e===x[5]){var o=C(t,n),a=o.left,i=o.right;r(a,n),r(i,n),n.isHorizontal?a.right2left():a.bottom2top(),i.translate(a.x-i.x,a.y-i.y),t.x=a.x,t.y=i.y;var f=t.getBoundingBox();n.isHorizontal?f.top<0&&t.translate(0,-f.top):f.left<0&&t.translate(-f.left,0)}var s=n.fixedRoot;return s===void 0&&(s=!0),s&&t.translate(-(t.x+t.width/2+t.hgap),-(t.y+t.height/2+t.vgap)),u(t,n),t};function u(t,n){if(n.radial){var r=n.isHorizontal?["x","y"]:["y","x"],e=r[0],o=r[1],a={x:1/0,y:1/0},i={x:-1/0,y:-1/0},f=0;t.DFTraverse(function(m){f++;var h=m.x,d=m.y;a.x=Math.min(a.x,h),a.y=Math.min(a.y,d),i.x=Math.max(i.x,h),i.y=Math.max(i.y,d)});var s=i[o]-a[o];if(s===0)return;var l=Math.PI*2/f;t.DFTraverse(function(m){var h=(m[o]-a[o])/s*(Math.PI*2-l)+l,d=m[e]-t[e];m.x=Math.cos(h)*d,m.y=Math.sin(h)*d})}}},function(w,G,T){var C=T(0),x=18,M=x*2,E=x,p={getId:function(r){return r.id||r.name},getPreH:function(r){return r.preH||0},getPreV:function(r){return r.preV||0},getHGap:function(r){return r.hgap||E},getVGap:function(r){return r.vgap||E},getChildren:function(r){return r.children},getHeight:function(r){return r.height||M},getWidth:function(r){var e=r.label||" ";return r.width||e.split("").length*x}};function u(n,r){var e=this;if(e.vgap=e.hgap=0,n instanceof u)return n;e.data=n;var o=r.getHGap(n),a=r.getVGap(n);return e.preH=r.getPreH(n),e.preV=r.getPreV(n),e.width=r.getWidth(n),e.height=r.getHeight(n),e.width+=e.preH,e.height+=e.preV,e.id=r.getId(n),e.x=e.y=0,e.depth=0,e.children||(e.children=[]),e.addGap(o,a),e}C.assign(u.prototype,{isRoot:function(){return this.depth===0},isLeaf:function(){return this.children.length===0},addGap:function(r,e){var o=this;o.hgap+=r,o.vgap+=e,o.width+=2*r,o.height+=2*e},eachNode:function(r){for(var e=this,o=[e],a;a=o.shift();)r(a),o=a.children.concat(o)},DFTraverse:function(r){this.eachNode(r)},BFTraverse:function(r){for(var e=this,o=[e],a;a=o.shift();)r(a),o=o.concat(a.children)},getBoundingBox:function(){var r={left:Number.MAX_VALUE,top:Number.MAX_VALUE,width:0,height:0};return this.eachNode(function(e){r.left=Math.min(r.left,e.x),r.top=Math.min(r.top,e.y),r.width=Math.max(r.width,e.x+e.width),r.height=Math.max(r.height,e.y+e.height)}),r},translate:function(r,e){r===void 0&&(r=0),e===void 0&&(e=0),this.eachNode(function(o){o.x+=r,o.y+=e,o.x+=o.preH,o.y+=o.preV})},right2left:function(){var r=this,e=r.getBoundingBox();r.eachNode(function(o){o.x=o.x-(o.x-e.left)*2-o.width}),r.translate(e.width,0)},bottom2top:function(){var r=this,e=r.getBoundingBox();r.eachNode(function(o){o.y=o.y-(o.y-e.top)*2-o.height}),r.translate(0,e.height)}});function t(n,r,e){r===void 0&&(r={}),r=C.assign({},p,r);var o=new u(n,r),a=[o],i;if(!e&&!n.collapsed){for(;i=a.shift();)if(!i.data.collapsed){var f=r.getChildren(i.data),s=f?f.length:0;if(i.children=new Array(s),f&&s)for(var l=0;l<s;l++){var m=new u(f[l],r);i.children[l]=m,a.push(m),m.parent=i,m.depth=i.depth+1}}}return o}w.exports=t},function(w,G,T){var C=T(3);w.exports=function(x,M){for(var E=C(x.data,M,!0),p=C(x.data,M,!0),u=x.children.length,t=Math.round(u/2),n=M.getSide||function(a,i){return i<t?"right":"left"},r=0;r<u;r++){var e=x.children[r],o=n(e,r);o==="right"?p.children.push(e):E.children.push(e)}return E.eachNode(function(a){a.isRoot()||(a.side="left")}),p.eachNode(function(a){a.isRoot()||(a.side="right")}),{left:E,right:p}}},function(w,G,T){var C={compactBox:T(6),dendrogram:T(8),indented:T(10),mindmap:T(12)};w.exports=C},function(w,G,T){function C(e,o){e.prototype=Object.create(o.prototype),e.prototype.constructor=e,x(e,o)}function x(e,o){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,i){return a.__proto__=i,a},x(e,o)}var M=T(1),E=T(7),p=T(2),u=T(0),t=function(e){function o(){return e.apply(this,arguments)||this}C(o,e);var a=o.prototype;return a.execute=function(){var f=this;return p(f.rootNode,f.options,E)},o}(M),n={};function r(e,o){return o=u.assign({},n,o),new t(e,o).execute()}w.exports=r},function(w,G){function T(u,t,n,r){r===void 0&&(r=[]);var e=this;e.w=u||0,e.h=t||0,e.y=n||0,e.x=0,e.c=r||[],e.cs=r.length,e.prelim=0,e.mod=0,e.shift=0,e.change=0,e.tl=null,e.tr=null,e.el=null,e.er=null,e.msel=0,e.mser=0}T.fromNode=function(u,t){if(!u)return null;var n=[];return u.children.forEach(function(r){n.push(T.fromNode(r,t))}),t?new T(u.height,u.width,u.x,n):new T(u.width,u.height,u.y,n)};function C(u,t,n){n?u.y+=t:u.x+=t,u.children.forEach(function(r){C(r,t,n)})}function x(u,t){var n=t?u.y:u.x;return u.children.forEach(function(r){n=Math.min(x(r,t),n)}),n}function M(u,t){var n=x(u,t);C(u,-n,t)}function E(u,t,n){n?t.y=u.x:t.x=u.x,u.c.forEach(function(r,e){E(r,t.children[e],n)})}function p(u,t,n){n===void 0&&(n=0),t?(u.x=n,n+=u.width):(u.y=n,n+=u.height),u.children.forEach(function(r){p(r,t,n)})}w.exports=function(u,t){t===void 0&&(t={});var n=t.isHorizontal;function r(b){if(b.cs===0){e(b);return}r(b.c[0]);for(var S=_(s(b.c[0].el),0,null),k=1;k<b.cs;++k){r(b.c[k]);var A=s(b.c[k].er);o(b,k,S),S=_(A,k,S)}h(b),e(b)}function e(b){b.cs===0?(b.el=b,b.er=b,b.msel=b.mser=0):(b.el=b.c[0].el,b.msel=b.c[0].msel,b.er=b.c[b.cs-1].er,b.mser=b.c[b.cs-1].mser)}function o(b,S,k){for(var A=b.c[S-1],K=A.mod,R=b.c[S],P=R.mod;A!==null&&R!==null;){s(A)>k.low&&(k=k.nxt);var N=K+A.prelim+A.w-(P+R.prelim);N>0&&(P+=N,a(b,S,k.index,N));var U=s(A),B=s(R);U<=B&&(A=f(A),A!==null&&(K+=A.mod)),U>=B&&(R=i(R),R!==null&&(P+=R.mod))}!A&&R?l(b,S,R,P):A&&!R&&m(b,S,A,K)}function a(b,S,k,A){b.c[S].mod+=A,b.c[S].msel+=A,b.c[S].mser+=A,v(b,S,k,A)}function i(b){return b.cs===0?b.tl:b.c[0]}function f(b){return b.cs===0?b.tr:b.c[b.cs-1]}function s(b){return b.y+b.h}function l(b,S,k,A){var K=b.c[0].el;K.tl=k;var R=A-k.mod-b.c[0].msel;K.mod+=R,K.prelim-=R,b.c[0].el=b.c[S].el,b.c[0].msel=b.c[S].msel}function m(b,S,k,A){var K=b.c[S].er;K.tr=k;var R=A-k.mod-b.c[S].mser;K.mod+=R,K.prelim-=R,b.c[S].er=b.c[S-1].er,b.c[S].mser=b.c[S-1].mser}function h(b){b.prelim=(b.c[0].prelim+b.c[0].mod+b.c[b.cs-1].mod+b.c[b.cs-1].prelim+b.c[b.cs-1].w)/2-b.w/2}function d(b,S){S+=b.mod,b.x=b.prelim+S,y(b);for(var k=0;k<b.cs;k++)d(b.c[k],S)}function v(b,S,k,A){if(k!==S-1){var K=S-k;b.c[k+1].shift+=A/K,b.c[S].shift-=A/K,b.c[S].change-=A-A/K}}function y(b){for(var S=0,k=0,A=0;A<b.cs;A++)S+=b.c[A].shift,k+=S+b.c[A].change,b.c[A].mod+=k}function _(b,S,k){for(;k!==null&&b>=k.low;)k=k.nxt;return{low:b,index:S,nxt:k}}p(u,n);var D=T.fromNode(u,n);return r(D),d(D,0),E(D,u,n),M(u,n),u}},function(w,G,T){function C(e,o){e.prototype=Object.create(o.prototype),e.prototype.constructor=e,x(e,o)}function x(e,o){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,i){return a.__proto__=i,a},x(e,o)}var M=T(1),E=T(9),p=T(2),u=T(0),t=function(e){function o(){return e.apply(this,arguments)||this}C(o,e);var a=o.prototype;return a.execute=function(){var f=this;return f.rootNode.width=0,p(f.rootNode,f.options,E)},o}(M),n={};function r(e,o){return o=u.assign({},n,o),new t(e,o).execute()}w.exports=r},function(w,G,T){var C=T(0);function x(p,u){p===void 0&&(p=0),u===void 0&&(u=[]);var t=this;t.x=t.y=0,t.leftChild=t.rightChild=null,t.height=0,t.children=u}var M={isHorizontal:!0,nodeSep:20,nodeSize:20,rankSep:200,subTreeSep:10};function E(p,u,t){t?(u.x=p.x,u.y=p.y):(u.x=p.y,u.y=p.x),p.children.forEach(function(n,r){E(n,u.children[r],t)})}w.exports=function(p,u){u===void 0&&(u={}),u=C.assign({},M,u);var t=0;function n(i){if(!i)return null;i.width=0,i.depth&&i.depth>t&&(t=i.depth);var f=i.children,s=f.length,l=new x(i.height,[]);return f.forEach(function(m,h){var d=n(m);l.children.push(d),h===0&&(l.leftChild=d),h===s-1&&(l.rightChild=d)}),l.originNode=i,l.isLeaf=i.isLeaf(),l}function r(i){if(i.isLeaf||i.children.length===0)i.drawingDepth=t;else{var f=i.children.map(function(l){return r(l)}),s=Math.min.apply(null,f);i.drawingDepth=s-1}return i.drawingDepth}var e;function o(i){i.x=i.drawingDepth*u.rankSep,i.isLeaf?(i.y=0,e&&(i.y=e.y+e.height+u.nodeSep,i.originNode.parent!==e.originNode.parent&&(i.y+=u.subTreeSep)),e=i):(i.children.forEach(function(f){o(f)}),i.y=(i.leftChild.y+i.rightChild.y)/2)}var a=n(p);return r(a),o(a),E(a,p,u.isHorizontal),p}},function(w,G,T){function C(a,i){a.prototype=Object.create(i.prototype),a.prototype.constructor=a,x(a,i)}function x(a,i){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(f,s){return f.__proto__=s,f},x(a,i)}var M=T(1),E=T(11),p=T(4),u=T(0),t=["LR","RL","H"],n=t[0],r=function(a){function i(){return a.apply(this,arguments)||this}C(i,a);var f=i.prototype;return f.execute=function(){var l=this,m=l.options,h=l.rootNode;m.isHorizontal=!0;var d=m.indent,v=d===void 0?20:d,y=m.dropCap,_=y===void 0?!0:y,D=m.direction,b=D===void 0?n:D,S=m.align;if(b&&t.indexOf(b)===-1)throw new TypeError("Invalid direction: "+b);if(b===t[0])E(h,v,_,S);else if(b===t[1])E(h,v,_,S),h.right2left();else if(b===t[2]){var k=p(h,m),A=k.left,K=k.right;E(A,v,_,S),A.right2left(),E(K,v,_,S);var R=A.getBoundingBox();K.translate(R.width,0),h.x=K.x-h.width/2}return h},i}(M),e={};function o(a,i){return i=u.assign({},e,i),new r(a,i).execute()}w.exports=o},function(w,G,T){var C=T(0);function x(M,E,p,u,t){var n=(typeof p=="function"?p(M):p)*M.depth;if(!u)try{if(M.id===M.parent.children[0].id){M.x+=n,M.y=E?E.y:0;return}}catch(o){}if(M.x+=n,E){if(M.y=E.y+C.getHeight(E,M,t),E.parent&&M.parent.id!==E.parent.id){var r=E.parent,e=r.y+C.getHeight(r,M,t);M.y=e>M.y?e:M.y}}else M.y=0}w.exports=function(M,E,p,u){var t=null;M.eachNode(function(n){x(n,t,E,p,u),t=n})}},function(w,G,T){function C(e,o){e.prototype=Object.create(o.prototype),e.prototype.constructor=e,x(e,o)}function x(e,o){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,i){return a.__proto__=i,a},x(e,o)}var M=T(1),E=T(13),p=T(2),u=T(0),t=function(e){function o(){return e.apply(this,arguments)||this}C(o,e);var a=o.prototype;return a.execute=function(){var f=this;return p(f.rootNode,f.options,E)},o}(M),n={};function r(e,o){return o=u.assign({},n,o),new t(e,o).execute()}w.exports=r},function(w,G,T){var C=T(0);function x(p,u){var t=0;return p.children.length?p.children.forEach(function(n){t+=x(n,u)}):t=p.height,p._subTreeSep=u.getSubTreeSep(p.data),p.totalHeight=Math.max(p.height,t)+2*p._subTreeSep,p.totalHeight}function M(p){var u=p.children,t=u.length;if(t){u.forEach(function(i){M(i)});var n=u[0],r=u[t-1],e=r.y-n.y+r.height,o=0;if(u.forEach(function(i){o+=i.totalHeight}),e>p.height)p.y=n.y+e/2-p.height/2;else if(u.length!==1||p.height>o){var a=p.y+(p.height-e)/2-n.y;u.forEach(function(i){i.translate(0,a)})}else p.y=(n.y+n.height/2+r.y+r.height/2)/2-p.height/2}}var E={getSubTreeSep:function(){return 0}};w.exports=function(p,u){u===void 0&&(u={}),u=C.assign({},E,u),p.parent={x:0,width:0,height:0,y:0},p.BFTraverse(function(t){t.x=t.parent.x+t.parent.width}),p.parent=null,x(p,u),p.startY=0,p.y=p.totalHeight/2-p.height/2,p.eachNode(function(t){var n=t.children,r=n.length;if(r){var e=n[0];if(e.startY=t.startY+t._subTreeSep,r===1)e.y=t.y+t.height/2-e.height/2;else{e.y=e.startY+e.totalHeight/2-e.height/2;for(var o=1;o<r;o++){var a=n[o];a.startY=n[o-1].startY+n[o-1].totalHeight,a.y=a.startY+a.totalHeight/2-a.height/2}}}}),M(p)}}])})},43169:function(Je,w,G){"use strict";var T=this&&this.__createBinding||(Object.create?function(M,E,p,u){u===void 0&&(u=p);var t=Object.getOwnPropertyDescriptor(E,p);(!t||("get"in t?!E.__esModule:t.writable||t.configurable))&&(t={enumerable:!0,get:function(){return E[p]}}),Object.defineProperty(M,u,t)}:function(M,E,p,u){u===void 0&&(u=p),M[u]=E[p]}),C=this&&this.__exportStar||function(M,E){for(var p in M)p!=="default"&&!Object.prototype.hasOwnProperty.call(E,p)&&T(E,M,p)};Object.defineProperty(w,"__esModule",{value:!0}),w.getLayoutByName=w.unRegisterLayout=w.registerLayout=void 0;var x=G(27653);Object.defineProperty(w,"registerLayout",{enumerable:!0,get:function(){return x.registerLayout}}),Object.defineProperty(w,"unRegisterLayout",{enumerable:!0,get:function(){return x.unRegisterLayout}}),Object.defineProperty(w,"getLayoutByName",{enumerable:!0,get:function(){return x.getLayoutByName}}),C(G(66929),w)},2021:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.Base=void 0;var G=function(){function T(){this.nodes=[],this.edges=[],this.combos=[],this.comboEdges=[],this.hiddenNodes=[],this.hiddenEdges=[],this.hiddenCombos=[],this.vedges=[],this.positions=[],this.destroyed=!1,this.onLayoutEnd=function(){}}return T.prototype.layout=function(C){return this.init(C),this.execute(!0)},T.prototype.init=function(C){this.nodes=C.nodes||[],this.edges=C.edges||[],this.combos=C.combos||[],this.comboEdges=C.comboEdges||[],this.hiddenNodes=C.hiddenNodes||[],this.hiddenEdges=C.hiddenEdges||[],this.hiddenCombos=C.hiddenCombos||[],this.vedges=C.vedges||[]},T.prototype.execute=function(C){},T.prototype.executeWithWorker=function(){},T.prototype.getDefaultCfg=function(){return{}},T.prototype.updateCfg=function(C){C&&Object.assign(this,C)},T.prototype.getType=function(){return"base"},T.prototype.destroy=function(){this.nodes=null,this.edges=null,this.combos=null,this.positions=null,this.destroyed=!0},T}();w.Base=G},93978:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])},t(n,r)};return function(n,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");t(n,r);function e(){this.constructor=n}n.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.CircularLayout=void 0;var C=G(2021),x=G(45612);function M(t,n,r,e){t.forEach(function(o,a){t[a].children=[],t[a].parent=[]}),e?n.forEach(function(o){var a=(0,x.getEdgeTerminal)(o,"source"),i=(0,x.getEdgeTerminal)(o,"target"),f=0;a&&(f=r[a]);var s=0;i&&(s=r[i]);var l=t[f].children,m=t[s].parent;l.push(t[s].id),m.push(t[f].id)}):n.forEach(function(o){var a=(0,x.getEdgeTerminal)(o,"source"),i=(0,x.getEdgeTerminal)(o,"target"),f=0;a&&(f=r[a]);var s=0;i&&(s=r[i]);var l=t[f].children,m=t[s].children;l.push(t[s].id),m.push(t[f].id)})}function E(t,n,r){for(var e=r.length,o=0;o<e;o++){var a=(0,x.getEdgeTerminal)(r[o],"source"),i=(0,x.getEdgeTerminal)(r[o],"target");if(t.id===a&&n.id===i||n.id===a&&t.id===i)return!0}return!1}function p(t,n){var r=t.degree,e=n.degree;return r<e?-1:r>e?1:0}var u=function(t){T(n,t);function n(r){var e=t.call(this)||this;return e.radius=null,e.nodeSize=void 0,e.startRadius=null,e.endRadius=null,e.startAngle=0,e.endAngle=2*Math.PI,e.clockwise=!0,e.divisions=1,e.ordering=null,e.angleRatio=1,e.nodes=[],e.edges=[],e.nodeMap={},e.degrees=[],e.width=300,e.height=300,e.updateCfg(r),e}return n.prototype.getDefaultCfg=function(){return{radius:null,startRadius:null,endRadius:null,startAngle:0,endAngle:2*Math.PI,clockwise:!0,divisions:1,ordering:null,angleRatio:1}},n.prototype.execute=function(){var r,e=this,o=e.nodes,a=e.edges,i=o.length;if(i===0){e.onLayoutEnd&&e.onLayoutEnd();return}!e.width&&typeof window!="undefined"&&(e.width=window.innerWidth),!e.height&&typeof window!="undefined"&&(e.height=window.innerHeight),e.center||(e.center=[e.width/2,e.height/2]);var f=e.center;if(i===1){o[0].x=f[0],o[0].y=f[1],e.onLayoutEnd&&e.onLayoutEnd();return}var s=e.radius,l=e.startRadius,m=e.endRadius,h=e.divisions,d=e.startAngle,v=e.endAngle,y=e.angleRatio,_=e.ordering,D=e.clockwise,b=e.nodeSpacing,S=e.nodeSize,k=(v-d)/i,A={};o.forEach(function(Se,Le){A[Se.id]=Le}),e.nodeMap=A;var K=(0,x.getDegree)(o.length,A,a);if(e.degrees=K,b){var R=(0,x.getFuncByUnknownType)(10,b),P=(0,x.getFuncByUnknownType)(10,S),N=-1/0;o.forEach(function(Se){var Le=P(Se);N<Le&&(N=Le)});var U=0;o.forEach(function(Se,Le){Le===0?U+=N||10:U+=(R(Se)||0)+(N||10)}),s=U/(2*Math.PI)}else!s&&!l&&!m?s=e.height>e.width?e.width/2:e.height/2:!l&&m?l=m:l&&!m&&(m=l);var B=k*y,ue=[];_==="topology"?ue=e.topologyOrdering():_==="topology-directed"?ue=e.topologyOrdering(!0):_==="degree"?ue=e.degreeOrdering():ue=o;for(var ae=Math.ceil(i/h),de=0;de<i;++de){var ve=s;!ve&&l!==null&&m!==null&&(ve=l+de*(m-l)/(i-1)),ve||(ve=10+de*100/(i-1));var we=d+de%ae*B+2*Math.PI/h*Math.floor(de/ae);D||(we=v-de%ae*B-2*Math.PI/h*Math.floor(de/ae)),ue[de].x=f[0]+Math.cos(we)*ve,ue[de].y=f[1]+Math.sin(we)*ve,ue[de].weight=K[de].all}return(r=e.onLayoutEnd)===null||r===void 0||r.call(e),{nodes:ue,edges:this.edges}},n.prototype.topologyOrdering=function(r){r===void 0&&(r=!1);var e=this,o=e.degrees,a=e.edges,i=e.nodes,f=(0,x.clone)(i),s=e.nodeMap,l=[f[0]],m=[i[0]],h=[],d=i.length;h[0]=!0,M(f,a,s,r);var v=0;return f.forEach(function(y,_){if(_!==0)if((_===d-1||o[_].all!==o[_+1].all||E(l[v],y,a))&&!h[_])l.push(y),m.push(i[s[y.id]]),h[_]=!0,v++;else{for(var D=l[v].children,b=!1,S=0;S<D.length;S++){var k=s[D[S]];if(o[k].all===o[_].all&&!h[k]){l.push(f[k]),m.push(i[s[f[k].id]]),h[k]=!0,b=!0;break}}for(var A=0;!b&&(h[A]||(l.push(f[A]),m.push(i[s[f[A].id]]),h[A]=!0,b=!0),A++,A!==d););}}),m},n.prototype.degreeOrdering=function(){var r=this,e=r.nodes,o=[],a=r.degrees;return e.forEach(function(i,f){i.degree=a[f].all,o.push(i)}),o.sort(p),o},n.prototype.getType=function(){return"circular"},n}(C.Base);w.CircularLayout=u},3467:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])},t(n,r)};return function(n,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");t(n,r);function e(){this.constructor=n}n.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}}(),C=this&&this.__assign||function(){return C=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},C.apply(this,arguments)};Object.defineProperty(w,"__esModule",{value:!0}),w.ComboCombinedLayout=void 0;var x=G(22688),M=G(2021),E=G(45612),p=G(66929),u=function(t){T(n,t);function n(r){var e=t.call(this)||this;return e.center=[0,0],e.nodes=[],e.edges=[],e.combos=[],e.comboEdges=[],e.comboPadding=10,e.comboTrees=[],e.updateCfg(r),e}return n.prototype.getDefaultCfg=function(){return{}},n.prototype.execute=function(){var r=this,e=r.nodes,o=r.center;if(!e||e.length===0){r.onLayoutEnd&&r.onLayoutEnd();return}if(e.length===1){e[0].x=o[0],e[0].y=o[1],r.onLayoutEnd&&r.onLayoutEnd();return}r.initVals(),r.run(),r.onLayoutEnd&&r.onLayoutEnd()},n.prototype.run=function(){var r,e=this,o=e.nodes,a=e.edges,i=e.combos,f=e.comboEdges,s=e.center,l={};o.forEach(function(N){l[N.id]=N});var m={};i.forEach(function(N){m[N.id]=N});var h=e.getInnerGraphs(l),d=[],v=[],y={},_=!0;this.comboTrees.forEach(function(N){var U=h[N.id];if(U){var B=C(C({},N),{x:U.x||m[N.id].x,y:U.y||m[N.id].y,fx:U.fx||m[N.id].fx,fy:U.fy||m[N.id].fy,mass:U.mass||m[N.id].mass,size:U.size});v.push(B),!isNaN(B.x)&&B.x!==0&&!isNaN(B.y)&&B.y!==0?_=!1:(B.x=Math.random()*100,B.y=Math.random()*100),d.push(N.id),(0,E.traverseTreeUp)(N,function(ue){return ue.id!==N.id&&(y[ue.id]=N.id),!0})}}),o.forEach(function(N){if(!(N.comboId&&m[N.comboId])){var U=C({},N);v.push(U),!isNaN(U.x)&&U.x!==0&&!isNaN(U.y)&&U.y!==0?_=!1:(U.x=Math.random()*100,U.y=Math.random()*100),d.push(N.id)}});var D=[];if(a.concat(f).forEach(function(N){var U=y[N.source]||N.source,B=y[N.target]||N.target;U!==B&&d.includes(U)&&d.includes(B)&&D.push({source:U,target:B})}),v!=null&&v.length){if(v.length===1)v[0].x=s[0],v[0].y=s[1];else{var b={nodes:v,edges:D},S=this.outerLayout||new p.GForceLayout({gravity:1,factor:4,linkDistance:function(N,U,B){var ue,ae,de=((((ue=U.size)===null||ue===void 0?void 0:ue[0])||30)+(((ae=B.size)===null||ae===void 0?void 0:ae[0])||30))/2;return Math.min(de*1.5,700)}}),k=(r=S.getType)===null||r===void 0?void 0:r.call(S);if(S.updateCfg({center:s,kg:5,preventOverlap:!0,animate:!1}),_&&x.FORCE_LAYOUT_TYPE_MAP[k]){var A=v.length<100?new p.MDSLayout:new p.GridLayout;A.layout(b)}S.layout(b)}v.forEach(function(N){var U=h[N.id];if(!U){var B=l[N.id];B&&(B.x=N.x,B.y=N.y);return}U.visited=!0,U.x=N.x,U.y=N.y,U.nodes.forEach(function(ue){ue.x+=N.x,ue.y+=N.y})})}for(var K=Object.keys(h),R=function(N){var U=K[N],B=h[U];if(!B)return"continue";B.nodes.forEach(function(ue){B.visited||(ue.x+=B.x||0,ue.y+=B.y||0),l[ue.id]&&(l[ue.id].x=ue.x,l[ue.id].y=ue.y)}),m[U]&&(m[U].x=B.x,m[U].y=B.y)},P=K.length-1;P>=0;P--)R(P);return{nodes:o,edges:a,combos:i,comboEdges:f}},n.prototype.getInnerGraphs=function(r){var e=this,o=e.comboTrees,a=e.nodeSize,i=e.edges,f=e.comboPadding,s=e.spacing,l={},m=this.innerLayout||new p.ConcentricLayout({type:"concentric",sortBy:"id"});return m.center=[0,0],m.preventOverlap=!0,m.nodeSpacing=s,(o||[]).forEach(function(h){(0,E.traverseTreeUp)(h,function(d){var v,y=(f==null?void 0:f(d))||10;if((0,E.isArray)(y)&&(y=Math.max.apply(Math,y)),!((v=d.children)===null||v===void 0)&&v.length){var D=d.children.map(function(ae){if(ae.itemType==="combo")return l[ae.id];var de=r[ae.id]||{};return C(C({},de),ae)}),b=D.map(function(ae){return ae.id}),S={nodes:D,edges:i.filter(function(ae){return b.includes(ae.source)&&b.includes(ae.target)})},k=1/0;D.forEach(function(ae){var de;ae.size||(ae.size=((de=l[ae.id])===null||de===void 0?void 0:de.size)||(a==null?void 0:a(ae))||[30,30]),(0,E.isNumber)(ae.size)&&(ae.size=[ae.size,ae.size]),k>ae.size[0]&&(k=ae.size[0]),k>ae.size[1]&&(k=ae.size[1])}),m.layout(S);var A=(0,E.getLayoutBBox)(D),K=A.minX,R=A.minY,P=A.maxX,N=A.maxY,U={x:(P+K)/2,y:(N+R)/2};S.nodes.forEach(function(ae){ae.x-=U.x,ae.y-=U.y});var B=Math.max(P-K,k)+y*2,ue=Math.max(N-R,k)+y*2;l[d.id]={id:d.id,nodes:D,size:[B,ue]}}else if(d.itemType==="combo"){var _=y?[y*2,y*2]:[30,30];l[d.id]={id:d.id,nodes:[],size:_}}return!0})}),l},n.prototype.initVals=function(){var r=this,e=r.nodeSize,o=r.spacing,a,i;if((0,E.isNumber)(o)?i=function(){return o}:(0,E.isFunction)(o)?i=o:i=function(){return 0},this.spacing=i,!e)a=function(d){var v=i(d);if(d.size){if((0,E.isArray)(d.size)){var y=d.size[0]>d.size[1]?d.size[0]:d.size[1];return(y+v)/2}if((0,E.isObject)(d.size)){var y=d.size.width>d.size.height?d.size.width:d.size.height;return(y+v)/2}return(d.size+v)/2}return 10+v/2};else if((0,E.isFunction)(e))a=function(d){var v=e(d),y=i(d);if((0,E.isArray)(d.size)){var _=d.size[0]>d.size[1]?d.size[0]:d.size[1];return(_+y)/2}return((v||10)+y)/2};else if((0,E.isArray)(e)){var f=e[0]>e[1]?e[0]:e[1],s=f/2;a=function(d){return s+i(d)/2}}else{var l=e/2;a=function(d){return l+i(d)/2}}this.nodeSize=a;var m=r.comboPadding,h;(0,E.isNumber)(m)?h=function(){return m}:(0,E.isArray)(m)?h=function(){return Math.max.apply(null,m)}:(0,E.isFunction)(m)?h=m:h=function(){return 0},this.comboPadding=h},n.prototype.getType=function(){return"comboCombined"},n}(M.Base);w.ComboCombinedLayout=u},51773:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var E=function(p,u){return E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])},E(p,u)};return function(p,u){if(typeof u!="function"&&u!==null)throw new TypeError("Class extends value "+String(u)+" is not a constructor or null");E(p,u);function t(){this.constructor=p}p.prototype=u===null?Object.create(u):(t.prototype=u.prototype,new t)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.ComboForceLayout=void 0;var C=G(2021),x=G(45612),M=function(E){T(p,E);function p(u){var t=E.call(this)||this;return t.center=[0,0],t.maxIteration=100,t.gravity=10,t.comboGravity=10,t.linkDistance=10,t.alpha=1,t.alphaMin=.001,t.alphaDecay=1-Math.pow(t.alphaMin,1/300),t.alphaTarget=0,t.velocityDecay=.6,t.edgeStrength=.6,t.nodeStrength=30,t.preventOverlap=!1,t.preventNodeOverlap=!1,t.preventComboOverlap=!1,t.collideStrength=void 0,t.nodeCollideStrength=.5,t.comboCollideStrength=.5,t.comboSpacing=20,t.comboPadding=10,t.optimizeRangeFactor=1,t.onTick=function(){},t.onLayoutEnd=function(){},t.depthAttractiveForceScale=1,t.depthRepulsiveForceScale=2,t.nodes=[],t.edges=[],t.combos=[],t.comboTrees=[],t.width=300,t.height=300,t.bias=[],t.nodeMap={},t.oriComboMap={},t.indexMap={},t.comboMap={},t.previousLayouted=!1,t.updateCfg(u),t}return p.prototype.getDefaultCfg=function(){return{maxIteration:100,center:[0,0],gravity:10,speed:1,comboGravity:30,preventOverlap:!1,preventComboOverlap:!0,preventNodeOverlap:!0,nodeSpacing:void 0,collideStrength:void 0,nodeCollideStrength:.5,comboCollideStrength:.5,comboSpacing:20,comboPadding:10,edgeStrength:.6,nodeStrength:30,linkDistance:10}},p.prototype.execute=function(){var u=this,t=u.nodes,n=u.center;if(u.comboTree={id:"comboTreeRoot",depth:-1,children:u.comboTrees},!t||t.length===0){u.onLayoutEnd&&u.onLayoutEnd();return}if(t.length===1){t[0].x=n[0],t[0].y=n[1],u.onLayoutEnd&&u.onLayoutEnd();return}u.initVals(),u.run(),u.onLayoutEnd&&u.onLayoutEnd()},p.prototype.run=function(){var u=this,t=u.nodes,n=u.previousLayouted?u.maxIteration/5:u.maxIteration;!u.width&&typeof window!="undefined"&&(u.width=window.innerWidth),!u.height&&typeof window!="undefined"&&(u.height=window.innerHeight);var r=u.center,e=u.velocityDecay,o=u.comboMap;u.previousLayouted||u.initPos(o);for(var a=function(l){var m=[];t.forEach(function(h,d){m[d]={x:0,y:0}}),u.applyCalculate(m),u.applyComboCenterForce(m),t.forEach(function(h,d){!(0,x.isNumber)(h.x)||!(0,x.isNumber)(h.y)||(h.x+=m[d].x*e,h.y+=m[d].y*e)}),u.alpha+=(u.alphaTarget-u.alpha)*u.alphaDecay,u.onTick()},i=0;i<n;i++)a(i);var f=[0,0];t.forEach(function(l){!(0,x.isNumber)(l.x)||!(0,x.isNumber)(l.y)||(f[0]+=l.x,f[1]+=l.y)}),f[0]/=t.length,f[1]/=t.length;var s=[r[0]-f[0],r[1]-f[1]];t.forEach(function(l,m){!(0,x.isNumber)(l.x)||!(0,x.isNumber)(l.y)||(l.x+=s[0],l.y+=s[1])}),u.combos.forEach(function(l){var m=o[l.id];m&&m.empty&&(l.x=m.cx||l.x,l.y=m.cy||l.y)}),u.previousLayouted=!0},p.prototype.initVals=function(){var u=this,t=u.edges,n=u.nodes,r=u.combos,e={},o={},a={};n.forEach(function(ve,we){o[ve.id]=ve,a[ve.id]=we}),u.nodeMap=o,u.indexMap=a;var i={};r.forEach(function(ve){i[ve.id]=ve}),u.oriComboMap=i,u.comboMap=u.getComboMap();var f=u.preventOverlap;u.preventComboOverlap=u.preventComboOverlap||f,u.preventNodeOverlap=u.preventNodeOverlap||f;var s=u.collideStrength;s&&(u.comboCollideStrength=s,u.nodeCollideStrength=s),u.comboCollideStrength=u.comboCollideStrength?u.comboCollideStrength:0,u.nodeCollideStrength=u.nodeCollideStrength?u.nodeCollideStrength:0;for(var l=0;l<t.length;++l){var m=(0,x.getEdgeTerminal)(t[l],"source"),h=(0,x.getEdgeTerminal)(t[l],"target");e[m]?e[m]++:e[m]=1,e[h]?e[h]++:e[h]=1}for(var d=[],l=0;l<t.length;++l){var m=(0,x.getEdgeTerminal)(t[l],"source"),h=(0,x.getEdgeTerminal)(t[l],"target");d[l]=e[m]/(e[m]+e[h])}this.bias=d;var v=u.nodeSize,y=u.nodeSpacing,_,D;if((0,x.isNumber)(y)?D=function(){return y}:(0,x.isFunction)(y)?D=y:D=function(){return 0},this.nodeSpacing=D,!v)_=function(ve){if(ve.size){if((0,x.isArray)(ve.size)){var we=ve.size[0]>ve.size[1]?ve.size[0]:ve.size[1];return we/2}if((0,x.isObject)(ve.size)){var we=ve.size.width>ve.size.height?ve.size.width:ve.size.height;return we/2}return ve.size/2}return 10};else if((0,x.isFunction)(v))_=function(ve){return v(ve)};else if((0,x.isArray)(v)){var b=v[0]>v[1]?v[0]:v[1],S=b/2;_=function(ve){return S}}else{var k=v/2;_=function(ve){return k}}this.nodeSize=_;var A=u.comboSpacing,K;(0,x.isNumber)(A)?K=function(){return A}:(0,x.isFunction)(A)?K=A:K=function(){return 0},this.comboSpacing=K;var R=u.comboPadding,P;(0,x.isNumber)(R)?P=function(){return R}:(0,x.isArray)(R)?P=function(){return Math.max.apply(null,R)}:(0,x.isFunction)(R)?P=R:P=function(){return 0},this.comboPadding=P;var N=this.linkDistance,U;N||(N=10),(0,x.isNumber)(N)?U=function(ve){return N}:U=N,this.linkDistance=U;var B=this.edgeStrength,ue;B||(B=1),(0,x.isNumber)(B)?ue=function(ve){return B}:ue=B,this.edgeStrength=ue;var ae=this.nodeStrength,de;ae||(ae=30),(0,x.isNumber)(ae)?de=function(ve){return ae}:de=ae,this.nodeStrength=de},p.prototype.initPos=function(u){var t=this,n=t.nodes;n.forEach(function(r,e){var o=r.comboId,a=u[o];o&&a?(r.x=a.cx+100/(e+1),r.y=a.cy+100/(e+1)):(r.x=100/(e+1),r.y=100/(e+1))})},p.prototype.getComboMap=function(){var u=this,t=u.nodeMap,n=u.comboTrees,r=u.oriComboMap,e={};return(n||[]).forEach(function(o){var a=[];(0,x.traverseTreeUp)(o,function(i){if(i.itemType==="node"||!r[i.id])return!0;if(e[i.id]===void 0){var f={id:i.id,name:i.id,cx:0,cy:0,count:0,depth:u.oriComboMap[i.id].depth||0,children:[]};e[i.id]=f}var s=i.children;s&&s.forEach(function(h){if(!e[h.id]&&!t[h.id])return!0;a.push(h)});var l=e[i.id];if(l.cx=0,l.cy=0,a.length===0){l.empty=!0;var m=r[i.id];l.cx=m.x,l.cy=m.y}return a.forEach(function(h){if(l.count++,h.itemType!=="node"){var d=e[h.id];(0,x.isNumber)(d.cx)&&(l.cx+=d.cx),(0,x.isNumber)(d.cy)&&(l.cy+=d.cy);return}var v=t[h.id];v&&((0,x.isNumber)(v.x)&&(l.cx+=v.x),(0,x.isNumber)(v.y)&&(l.cy+=v.y))}),l.cx/=l.count||1,l.cy/=l.count||1,l.children=a,!0})}),e},p.prototype.applyComboCenterForce=function(u){var t=this,n=t.gravity,r=t.comboGravity||n,e=this.alpha,o=t.comboTrees,a=t.indexMap,i=t.nodeMap,f=t.comboMap;(o||[]).forEach(function(s){(0,x.traverseTreeUp)(s,function(l){if(l.itemType==="node")return!0;var m=f[l.id];if(!m)return!0;var h=f[l.id],d=(h.depth+1)/10*.5,v=h.cx,y=h.cy;return h.cx=0,h.cy=0,h.children.forEach(function(_){if(_.itemType!=="node"){var D=f[_.id];D&&(0,x.isNumber)(D.cx)&&(h.cx+=D.cx),D&&(0,x.isNumber)(D.cy)&&(h.cy+=D.cy);return}var b=i[_.id],S=b.x-v||.005,k=b.y-y||.005,A=Math.sqrt(S*S+k*k),K=a[b.id],R=r*e/A*d;u[K].x-=S*R,u[K].y-=k*R,(0,x.isNumber)(b.x)&&(h.cx+=b.x),(0,x.isNumber)(b.y)&&(h.cy+=b.y)}),h.cx/=h.count||1,h.cy/=h.count||1,!0})})},p.prototype.applyCalculate=function(u){var t=this,n=t.comboMap,r=t.nodes,e={};r.forEach(function(a,i){r.forEach(function(f,s){if(!(i<s)){var l=a.x-f.x||.005,m=a.y-f.y||.005,h=l*l+m*m,d=Math.sqrt(h);h<1&&(h=d),e["".concat(a.id,"-").concat(f.id)]={vx:l,vy:m,vl2:h,vl:d},e["".concat(f.id,"-").concat(a.id)]={vl2:h,vl:d,vx:-l,vy:-m}}})}),t.updateComboSizes(n),t.calRepulsive(u,e),t.calAttractive(u,e);var o=t.preventComboOverlap;o&&t.comboNonOverlapping(u,n)},p.prototype.updateComboSizes=function(u){var t=this,n=t.comboTrees,r=t.nodeMap,e=t.nodeSize,o=t.comboSpacing,a=t.comboPadding;(n||[]).forEach(function(i){var f=[];(0,x.traverseTreeUp)(i,function(s){if(s.itemType==="node")return!0;var l=u[s.id];if(!l)return!1;var m=s.children;m&&m.forEach(function(v){!u[v.id]&&!r[v.id]||f.push(v)}),l.minX=1/0,l.minY=1/0,l.maxX=-1/0,l.maxY=-1/0,f.forEach(function(v){if(v.itemType!=="node")return!0;var y=r[v.id];if(!y)return!0;var _=e(y),D=y.x-_,b=y.y-_,S=y.x+_,k=y.y+_;l.minX>D&&(l.minX=D),l.minY>b&&(l.minY=b),l.maxX<S&&(l.maxX=S),l.maxY<k&&(l.maxY=k)});var h=t.oriComboMap[s.id].size||10;(0,x.isArray)(h)&&(h=h[0]);var d=Math.max(l.maxX-l.minX,l.maxY-l.minY,h);return l.r=d/2+o(l)/2+a(l),!0})})},p.prototype.comboNonOverlapping=function(u,t){var n=this,r=n.comboTree,e=n.comboCollideStrength,o=n.indexMap,a=n.nodeMap;(0,x.traverseTreeUp)(r,function(i){if(!t[i.id]&&!a[i.id]&&i.id!=="comboTreeRoot")return!1;var f=i.children;return f&&f.length>1&&f.forEach(function(s,l){if(s.itemType==="node")return!1;var m=t[s.id];m&&f.forEach(function(h,d){if(l<=d||h.itemType==="node")return!1;var v=t[h.id];if(!v)return!1;var y=m.cx-v.cx||.005,_=m.cy-v.cy||.005,D=y*y+_*_,b=m.r||1,S=v.r||1,k=b+S,A=S*S,K=b*b;if(D<k*k){var R=s.children;if(!R||R.length===0)return!1;var P=h.children;if(!P||P.length===0)return!1;var N=Math.sqrt(D),U=(k-N)/N*e,B=y*U,ue=_*U,ae=A/(K+A),de=1-ae;R.forEach(function(ve){if(ve.itemType!=="node")return!1;if(a[ve.id]){var we=o[ve.id];P.forEach(function(Se){if(Se.itemType!=="node"||!a[Se.id])return!1;var Le=o[Se.id];u[we].x+=B*ae,u[we].y+=ue*ae,u[Le].x-=B*de,u[Le].y-=ue*de})}})}})}),!0})},p.prototype.calRepulsive=function(u,t){var n=this,r=n.nodes,e=n.width*n.optimizeRangeFactor,o=n.nodeStrength,a=n.alpha,i=n.nodeCollideStrength,f=n.preventNodeOverlap,s=n.nodeSize,l=n.nodeSpacing,m=n.depthRepulsiveForceScale,h=n.center;r.forEach(function(d,v){if(!(!d.x||!d.y)){if(h){var y=n.gravity,_=d.x-h[0]||.005,D=d.y-h[1]||.005,b=Math.sqrt(_*_+D*D);u[v].x-=_*y*a/b,u[v].y-=D*y*a/b}r.forEach(function(S,k){if(v!==k&&!(!S.x||!S.y)){var A=t["".concat(d.id,"-").concat(S.id)],K=A.vl2,R=A.vl;if(!(R>e)){var P=t["".concat(d.id,"-").concat(S.id)],N=P.vx,U=P.vy,B=Math.log(Math.abs(S.depth-d.depth)/10)+1||1;B=B<1?1:B,S.comboId!==d.comboId&&(B+=1);var ue=B?Math.pow(m,B):1,ae=o(S)*a/K*ue;if(u[v].x+=N*ae,u[v].y+=U*ae,v<k&&f){var de=s(d)+l(d)||1,ve=s(S)+l(S)||1,we=de+ve;if(K<we*we){var Se=(we-R)/R*i,Le=ve*ve,q=Le/(de*de+Le),se=N*Se,ne=U*Se;u[v].x+=se*q,u[v].y+=ne*q,q=1-q,u[k].x-=se*q,u[k].y-=ne*q}}}}})}})},p.prototype.calAttractive=function(u,t){var n=this,r=n.edges,e=n.linkDistance,o=n.alpha,a=n.edgeStrength,i=n.bias,f=n.depthAttractiveForceScale;r.forEach(function(s,l){var m=(0,x.getEdgeTerminal)(s,"source"),h=(0,x.getEdgeTerminal)(s,"target");if(!(!m||!h||m===h)){var d=n.indexMap[m],v=n.indexMap[h],y=n.nodeMap[m],_=n.nodeMap[h];if(!(!y||!_)){var D=y.depth===_.depth?0:Math.log(Math.abs(y.depth-_.depth)/10);y.comboId===_.comboId&&(D=D/2);var b=D?Math.pow(f,D):1;if(y.comboId!==_.comboId&&b===1?b=f/2:y.comboId===_.comboId&&(b=2),!(!(0,x.isNumber)(_.x)||!(0,x.isNumber)(y.x)||!(0,x.isNumber)(_.y)||!(0,x.isNumber)(y.y))){var S=t["".concat(h,"-").concat(m)],k=S.vl,A=S.vx,K=S.vy,R=(k-e(s))/k*o*a(s)*b,P=A*R,N=K*R,U=i[l];u[v].x-=P*U,u[v].y-=N*U,u[d].x+=P*(1-U),u[d].y+=N*(1-U)}}}})},p.prototype.getType=function(){return"comboForce"},p}(C.Base);w.ComboForceLayout=M},52342:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var E=function(p,u){return E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])},E(p,u)};return function(p,u){if(typeof u!="function"&&u!==null)throw new TypeError("Class extends value "+String(u)+" is not a constructor or null");E(p,u);function t(){this.constructor=p}p.prototype=u===null?Object.create(u):(t.prototype=u.prototype,new t)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.ConcentricLayout=void 0;var C=G(45612),x=G(2021),M=function(E){T(p,E);function p(u){var t=E.call(this)||this;return t.nodeSize=30,t.minNodeSpacing=10,t.nodeSpacing=10,t.preventOverlap=!1,t.equidistant=!1,t.startAngle=3/2*Math.PI,t.clockwise=!0,t.sortBy="degree",t.nodes=[],t.edges=[],t.width=300,t.height=300,t.onLayoutEnd=function(){},t.updateCfg(u),t}return p.prototype.getDefaultCfg=function(){return{nodeSize:30,minNodeSpacing:10,nodeSpacing:10,preventOverlap:!1,sweep:void 0,equidistant:!1,startAngle:3/2*Math.PI,clockwise:!0,maxLevelDiff:void 0,sortBy:"degree"}},p.prototype.execute=function(){var u,t,n=this,r=n.nodes,e=n.edges,o=r.length;if(o===0){(u=n.onLayoutEnd)===null||u===void 0||u.call(n);return}!n.width&&typeof window!="undefined"&&(n.width=window.innerWidth),!n.height&&typeof window!="undefined"&&(n.height=window.innerHeight),n.center||(n.center=[n.width/2,n.height/2]);var a=n.center;if(o===1){r[0].x=a[0],r[0].y=a[1],(t=n.onLayoutEnd)===null||t===void 0||t.call(n);return}var i=n.nodeSize,f=n.nodeSpacing,s=[],l,m=0;(0,C.isArray)(i)?l=Math.max(i[0],i[1]):l=i,(0,C.isArray)(f)?m=Math.max(f[0],f[1]):(0,C.isNumber)(f)&&(m=f),r.forEach(function(B){s.push(B);var ue=l;(0,C.isArray)(B.size)?ue=Math.max(B.size[0],B.size[1]):(0,C.isNumber)(B.size)?ue=B.size:(0,C.isObject)(B.size)&&(ue=Math.max(B.size.width,B.size.height)),l=Math.max(l,ue),(0,C.isFunction)(f)&&(m=Math.max(f(B),m))}),n.clockwise=n.counterclockwise!==void 0?!n.counterclockwise:n.clockwise;var h={},d={};if(s.forEach(function(B,ue){h[B.id]=B,d[B.id]=ue}),(n.sortBy==="degree"||!(0,C.isString)(n.sortBy)||s[0][n.sortBy]===void 0)&&(n.sortBy="degree",!(0,C.isNumber)(r[0].degree))){var v=(0,C.getDegree)(r.length,d,e);s.forEach(function(B,ue){B.degree=v[ue].all})}s.sort(function(B,ue){return ue[n.sortBy]-B[n.sortBy]}),n.maxValueNode=s[0],n.maxLevelDiff=n.maxLevelDiff||n.maxValueNode[n.sortBy]/4;var y=[[]],_=y[0];s.forEach(function(B){if(_.length>0){var ue=Math.abs(_[0][n.sortBy]-B[n.sortBy]);n.maxLevelDiff&&ue>=n.maxLevelDiff&&(_=[],y.push(_))}_.push(B)});var D=l+(m||n.minNodeSpacing);if(!n.preventOverlap){var b=y.length>0&&y[0].length>1,S=Math.min(n.width,n.height)/2-D,k=S/(y.length+(b?1:0));D=Math.min(D,k)}var A=0;if(y.forEach(function(B){var ue=n.sweep;ue===void 0&&(ue=2*Math.PI-2*Math.PI/B.length);var ae=B.dTheta=ue/Math.max(1,B.length-1);if(B.length>1&&n.preventOverlap){var de=Math.cos(ae)-Math.cos(0),ve=Math.sin(ae)-Math.sin(0),we=Math.sqrt(D*D/(de*de+ve*ve));A=Math.max(we,A)}B.r=A,A+=D}),n.equidistant){for(var K=0,R=0,P=0;P<y.length;P++){var N=y[P],U=N.r-R;K=Math.max(K,U)}R=0,y.forEach(function(B,ue){ue===0&&(R=B.r),B.r=R,R+=K})}return y.forEach(function(B){var ue=B.dTheta,ae=B.r;B.forEach(function(de,ve){var we=n.startAngle+(n.clockwise?1:-1)*ue*ve;de.x=a[0]+ae*Math.cos(we),de.y=a[1]+ae*Math.sin(we)})}),n.onLayoutEnd&&n.onLayoutEnd(),{nodes:r,edges:e}},p.prototype.getType=function(){return"concentric"},p}(x.Base);w.ConcentricLayout=M},22688:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.FORCE_LAYOUT_TYPE_MAP=w.LAYOUT_MESSAGE=void 0,w.LAYOUT_MESSAGE={RUN:"LAYOUT_RUN",END:"LAYOUT_END",ERROR:"LAYOUT_ERROR",TICK:"LAYOUT_TICK",GPURUN:"GPU_LAYOUT_RUN",GPUEND:"GPU_LAYOUT_END"},w.FORCE_LAYOUT_TYPE_MAP={gForce:!0,force2:!0,fruchterman:!0,forceAtlas2:!0,force:!0,"graphin-force":!0}},78389:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var n=function(r,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,a){o.__proto__=a}||function(o,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(o[i]=a[i])},n(r,e)};return function(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(r,e);function o(){this.constructor=r}r.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}}(),C=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(w,"__esModule",{value:!0}),w.DagreLayout=void 0;var x=C(G(21753)),M=G(45612),E=G(2021),p=G(49565),u=function(n){T(r,n);function r(e){var o=n.call(this)||this;return o.rankdir="TB",o.nodesep=50,o.ranksep=50,o.controlPoints=!1,o.sortByCombo=!1,o.edgeLabelSpace=!0,o.radial=!1,o.nodes=[],o.edges=[],o.onLayoutEnd=function(){},o.layoutNode=function(a){var i=o,f=i.nodes,s=f.find(function(m){return m.id===a});if(s){var l=s.layout!==!1;return l}return!0},o.updateCfg(e),o}return r.prototype.getDefaultCfg=function(){return{rankdir:"TB",align:void 0,nodeSize:void 0,nodesepFunc:void 0,ranksepFunc:void 0,nodesep:50,ranksep:50,controlPoints:!1,radial:!1,focusNode:null}},r.prototype.execute=function(){var e=this,o,a,i,f,s=this,l=s.nodes,m=s.nodeSize,h=s.rankdir,d=s.combos,v=s.begin,y=s.radial,_=s.comboEdges,D=_===void 0?[]:_,b=s.vedges,S=b===void 0?[]:b;if(l){var k=s.edges||[],A=new p.Graph({multigraph:!0,compound:!0});s.nodeMap={};var K={};l.forEach(function(Y){s.nodeMap[Y.id]=Y,Y.comboId&&(K[Y.comboId]=K[Y.comboId]||[],K[Y.comboId].push(Y.id))});var R=[],P={};!((o=s.nodeOrder)===null||o===void 0)&&o.length?(s.nodeOrder.forEach(function(Y){P[Y]=!0,R.push(s.nodeMap[Y])}),l.forEach(function(Y){P[Y.id]||R.push(Y)})):R=l;var N;m?(0,M.isArray)(m)?N=function(){return m}:N=function(){return[m,m]}:N=function(Y){return Y.size?(0,M.isArray)(Y.size)?Y.size:(0,M.isObject)(Y.size)?[Y.size.width||40,Y.size.height||40]:[Y.size,Y.size]:[40,40]};var U=(0,M.getFunc)(s.ranksep,50,s.ranksepFunc),B=(0,M.getFunc)(s.nodesep,50,s.nodesepFunc),ue=B,ae=U;(h==="LR"||h==="RL")&&(ue=U,ae=B),A.setDefaultEdgeLabel(function(){return{}}),A.setGraph(s);var de={};this.sortByCombo&&d&&d.forEach(function(Y){if(de[Y.id]=Y,Y.collapsed){var $=N(Y),Ie=ae(Y),_e=ue(Y),Ae=$[0]+2*_e,Be=$[1]+2*Ie;A.setNode(Y.id,{width:Ae,height:Be})}Y.parentId&&(de[Y.parentId]||A.setNode(Y.parentId,{}),A.setParent(Y.id,Y.parentId))}),R.filter(function(Y){return Y.layout!==!1}).forEach(function(Y){var $=N(Y),Ie=ae(Y),_e=ue(Y),Ae=$[0]+2*_e,Be=$[1]+2*Ie,ze=Y.layer;(0,M.isNumber)(ze)?A.setNode(Y.id,{width:Ae,height:Be,layer:ze}):A.setNode(Y.id,{width:Ae,height:Be}),e.sortByCombo&&Y.comboId&&(de[Y.comboId]||(de[Y.comboId]={id:Y.comboId},A.setNode(Y.comboId,{})),A.setParent(Y.id,Y.comboId))}),k.forEach(function(Y){var $=(0,M.getEdgeTerminal)(Y,"source"),Ie=(0,M.getEdgeTerminal)(Y,"target");e.layoutNode($)&&e.layoutNode(Ie)&&A.setEdge($,Ie,{weight:Y.weight||1})}),(a=D==null?void 0:D.concat(S||[]))===null||a===void 0||a.forEach(function(Y){var $,Ie,_e=Y.source,Ae=Y.target,Be=!(($=de[_e])===null||$===void 0)&&$.collapsed?[_e]:K[_e]||[_e],ze=!((Ie=de[Ae])===null||Ie===void 0)&&Ie.collapsed?[Ae]:K[Ae]||[Ae];Be.forEach(function(Ye){ze.forEach(function(Ve){A.setEdge(Ye,Ve,{weight:Y.weight||1})})})});var ve=void 0;!((i=s.preset)===null||i===void 0)&&i.nodes&&(ve=new p.Graph({multigraph:!0,compound:!0}),s.preset.nodes.forEach(function(Y){ve==null||ve.setNode(Y.id,Y)})),x.default.layout(A,{prevGraph:ve,edgeLabelSpace:s.edgeLabelSpace,keepNodeOrder:!!s.nodeOrder,nodeOrder:s.nodeOrder});var we=[0,0];if(v){var Se=1/0,Le=1/0;A.nodes().forEach(function(Y){var $=A.node(Y);Se>$.x&&(Se=$.x),Le>$.y&&(Le=$.y)}),A.edges().forEach(function(Y){var $,Ie=A.edge(Y);($=Ie.points)===null||$===void 0||$.forEach(function(_e){Se>_e.x&&(Se=_e.x),Le>_e.y&&(Le=_e.y)})}),we[0]=v[0]-Se,we[1]=v[1]-Le}var q=h==="LR"||h==="RL";if(y){var se=this,ne=se.focusNode,pe=se.ranksep,Me=se.getRadialPos,he=(0,M.isString)(ne)?ne:ne==null?void 0:ne.id,De=he?(f=A.node(he))===null||f===void 0?void 0:f._rank:0,Pe=[],He=q?"y":"x",Ze=q?"height":"width",Ue=1/0,tt=-1/0;A.nodes().forEach(function(Y){var $=A.node(Y);if(s.nodeMap[Y]){var Ie=B(s.nodeMap[Y]);if(De===0)Pe[$._rank]||(Pe[$._rank]={nodes:[],totalWidth:0,maxSize:-1/0}),Pe[$._rank].nodes.push(Y),Pe[$._rank].totalWidth+=Ie*2+$[Ze],Pe[$._rank].maxSize<Math.max($.width,$.height)&&(Pe[$._rank].maxSize=Math.max($.width,$.height));else{var _e=$._rank-De;if(_e===0)Pe[_e]||(Pe[_e]={nodes:[],totalWidth:0,maxSize:-1/0}),Pe[_e].nodes.push(Y),Pe[_e].totalWidth+=Ie*2+$[Ze],Pe[_e].maxSize<Math.max($.width,$.height)&&(Pe[_e].maxSize=Math.max($.width,$.height));else{var Ae=Math.abs(_e);Pe[Ae]||(Pe[Ae]={left:[],right:[],totalWidth:0,maxSize:-1/0}),Pe[Ae].totalWidth+=Ie*2+$[Ze],Pe[Ae].maxSize<Math.max($.width,$.height)&&(Pe[Ae].maxSize=Math.max($.width,$.height)),_e<0?Pe[Ae].left.push(Y):Pe[Ae].right.push(Y)}}var Be=$[He]-$[Ze]/2-Ie,ze=$[He]+$[Ze]/2+Ie;Be<Ue&&(Ue=Be),ze>tt&&(tt=ze)}});var pt=pe||50,Mt={},jt=(tt-Ue)/.9,Ct=[(Ue+tt-jt)*.5,(Ue+tt+jt)*.5],Xt=function(Y,$,Ie,_e){Ie===void 0&&(Ie=-1/0),_e===void 0&&(_e=[0,1]);var Ae=Ie;return Y.forEach(function(Be){var ze=A.node(Be);Mt[Be]=$;var Ye=Me(ze[He],Ct,jt,$,_e),Ve=Ye.x,qe=Ye.y;if(s.nodeMap[Be]){s.nodeMap[Be].x=Ve+we[0],s.nodeMap[Be].y=qe+we[1],s.nodeMap[Be]._order=ze._order;var je=U(s.nodeMap[Be]);Ae<je&&(Ae=je)}}),Ae},Jt=!0,Z=0;Pe.forEach(function(Y){var $,Ie,_e,Ae,Be,ze,Ye;if(!(!(!(($=Y==null?void 0:Y.nodes)===null||$===void 0)&&$.length)&&!(!((Ie=Y==null?void 0:Y.left)===null||Ie===void 0)&&Ie.length)&&!(!((_e=Y==null?void 0:Y.right)===null||_e===void 0)&&_e.length))){if(Jt&&Y.nodes.length===1){var Ve=Y.nodes[0];if(!s.nodeMap[Ve])return;s.nodeMap[Ve].x=we[0],s.nodeMap[Ve].y=we[1],Mt[Y.nodes[0]]=0,pt=U(s.nodeMap[Ve]),Jt=!1;return}pt=Math.max(pt,Y.totalWidth/(2*Math.PI));var qe=-1/0;if(De===0||!((Ae=Y.nodes)===null||Ae===void 0)&&Ae.length)qe=Xt(Y.nodes,pt,qe,[0,1]);else{var je=((Be=Y.left)===null||Be===void 0?void 0:Be.length)/(((ze=Y.left)===null||ze===void 0?void 0:ze.length)+((Ye=Y.right)===null||Ye===void 0?void 0:Ye.length));qe=Xt(Y.left,pt,qe,[0,je]),qe=Xt(Y.right,pt,qe,[je+.05,1])}pt+=qe,Jt=!1,Z-Y.maxSize}}),A.edges().forEach(function(Y){var $,Ie,_e,Ae=A.edge(Y),Be=k.findIndex(function(ct){var st=(0,M.getEdgeTerminal)(ct,"source"),gt=(0,M.getEdgeTerminal)(ct,"target");return st===Y.v&&gt===Y.w});if(!(Be<=-1)&&s.edgeLabelSpace&&s.controlPoints&&k[Be].type!=="loop"){var ze=He==="x"?"y":"x",Ye=($=Ae==null?void 0:Ae.points)===null||$===void 0?void 0:$.slice(1,Ae.points.length-1),Ve=[],qe=(Ie=A.node(Y.v))===null||Ie===void 0?void 0:Ie[ze],je=qe-((_e=A.node(Y.w))===null||_e===void 0?void 0:_e[ze]),at=Mt[Y.v],vt=at-Mt[Y.w];Ye==null||Ye.forEach(function(ct){var st=(ct[ze]-qe)/je*vt+at,gt=Me(ct[He],Ct,jt,st);Ve.push({x:gt.x+we[0],y:gt.y+we[1]})}),k[Be].controlPoints=Ve}})}else{var J=new Set,ie=h==="BT"||h==="RL",me=ie?function(Y,$){return $-Y}:function(Y,$){return Y-$};A.nodes().forEach(function(Y){var $=A.node(Y);if($){var Ie=e.nodeMap[Y];Ie||(Ie=d==null?void 0:d.find(function(_e){return _e.id===Y})),Ie&&(Ie.x=$.x+we[0],Ie.y=$.y+we[1],Ie._order=$._order,J.add(q?Ie.x:Ie.y))}});var Oe=Array.from(J).sort(me),Ce=q?function(Y,$){return Y.x!==$.x}:function(Y,$){return Y.y!==$.y},Ne=q?function(Y,$,Ie){var _e=Math.max($.y,Ie.y),Ae=Math.min($.y,Ie.y);return Y.filter(function(Be){return Be.y<=_e&&Be.y>=Ae})}:function(Y,$,Ie){var _e=Math.max($.x,Ie.x),Ae=Math.min($.x,Ie.x);return Y.filter(function(Be){return Be.x<=_e&&Be.x>=Ae})};A.edges().forEach(function(Y){var $,Ie=A.edge(Y),_e=k.findIndex(function(ze){var Ye=(0,M.getEdgeTerminal)(ze,"source"),Ve=(0,M.getEdgeTerminal)(ze,"target");return Ye===Y.v&&Ve===Y.w});if(!(_e<=-1)&&s.edgeLabelSpace&&s.controlPoints&&k[_e].type!=="loop"){($=Ie==null?void 0:Ie.points)===null||$===void 0||$.forEach(function(ze){ze.x+=we[0],ze.y+=we[1]});var Ae=s.nodeMap[Y.v],Be=s.nodeMap[Y.w];k[_e].controlPoints=t(Ie==null?void 0:Ie.points,Ae,Be,Oe,q,Ce,Ne)}})}return s.onLayoutEnd&&s.onLayoutEnd(),{nodes:l,edges:k}}},r.prototype.getRadialPos=function(e,o,a,i,f){f===void 0&&(f=[0,1]);var s=(e-o[0])/a;s=s*(f[1]-f[0])+f[0];var l=s*2*Math.PI;return{x:Math.cos(l)*i,y:Math.sin(l)*i}},r.prototype.getType=function(){return"dagre"},r}(E.Base);w.DagreLayout=u;var t=function(n,r,e,o,a,i,f){var s=(n==null?void 0:n.slice(1,n.length-1))||[];if(r&&e){var l=r.x,m=r.y,h=e.x,d=e.y;if(a&&(l=r.y,m=r.x,h=e.y,d=e.x),d!==m&&l!==h){var v=o.indexOf(m),y=o[v+1];if(y){var _=s[0],D=a?{x:(m+y)/2,y:(_==null?void 0:_.y)||h}:{x:(_==null?void 0:_.x)||h,y:(m+y)/2};(!_||i(_,D))&&s.unshift(D)}var b=o.indexOf(d),S=Math.abs(b-v);if(S===1)s=f(s,r,e),s.length||s.push(a?{x:(m+d)/2,y:l}:{x:l,y:(m+d)/2});else if(S>1){var k=o[b-1];if(k){var A=s[s.length-1],K=a?{x:(d+k)/2,y:(A==null?void 0:A.y)||h}:{x:(A==null?void 0:A.x)||l,y:(d+k)/2};(!A||i(A,K))&&s.push(K)}}}}return s}},63473:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])},t(n,r)};return function(n,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");t(n,r);function e(){this.constructor=n}n.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}}(),C=this&&this.__assign||function(){return C=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},C.apply(this,arguments)},x=this&&this.__spreadArray||function(t,n,r){if(r||arguments.length===2)for(var e=0,o=n.length,a;e<o;e++)(a||!(e in n))&&(a||(a=Array.prototype.slice.call(n,0,e)),a[e]=n[e]);return t.concat(a||Array.prototype.slice.call(n))};Object.defineProperty(w,"__esModule",{value:!0}),w.DagreCompoundLayout=void 0;var M=G(2021),E=G(85534),p=G(45612),u=function(t){T(n,t);function n(r){var e=t.call(this)||this;return e.rankdir="TB",e.nodesep=50,e.edgesep=5,e.ranksep=50,e.controlPoints=!0,e.anchorPoint=!0,e.nodes=[],e.edges=[],e.combos=[],e.onLayoutEnd=function(){},e.updateCfg(r),e}return n.prototype.getDefaultCfg=function(){return{rankdir:"TB",align:void 0,begin:void 0,nodeSize:void 0,nodesep:50,ranksep:50,controlPoints:!0,anchorPoint:!0}},n.prototype.init=function(r){var e=r.hiddenNodes||[],o=r.hiddenEdges||[],a=r.hiddenCombos||[];this.nodes=this.getDataByOrder((r.nodes||[]).concat(e)),this.edges=this.getDataByOrder((r.edges||[]).concat(o)),this.combos=(r.combos||[]).concat(a.map(function(i){return C(C({},i),{collapsed:!0})}))},n.prototype.execute=function(){var r=this,e=r.nodes,o=r.edges;if(e){var a=r.getLayoutConfig(),i=a.graphDef,f=a.graphOption,s=a.graphSettings,l=(0,E.buildGraph)(i,f,s),m=(0,E.flatGraph)(l,!0);return this.updatePosition(m),r.onLayoutEnd&&r.onLayoutEnd(),{nodes:e,edges:o}}},n.prototype.getNodePath=function(r){var e=this,o=e.nodes,a=e.combos,i=o.find(function(s){return s.id===r}),f=function(s,l){l===void 0&&(l=[]);var m=a.find(function(h){return h.id===s});return m?(l.unshift(s),m.parentId?f(m.parentId,l):l):l};return i&&i.comboId?f(i.comboId,[r]):[r]},n.prototype.getLayoutConfig=function(){var r,e,o,a=this,i=a.nodes,f=a.edges,s=a.combos,l=a.nodeSize,m=a.rankdir,h=a.align,d=a.edgesep,v=a.nodesep,y=a.ranksep,_=a.settings,D=(s||[]).reduce(function(Se,Le){var q=i.filter(function(ne){return ne.comboId===Le.id}).map(function(ne){return ne.id}),se=(s||[]).filter(function(ne){return ne.parentId===Le.id}).map(function(ne){return ne.id});return(q.length||se.length)&&(Se[Le.id]=x(x([],q,!0),se,!0)),Se},{}),b;l?(0,p.isArray)(l)?b=function(){return l}:b=function(){return[l,l]}:b=function(Se){return Se&&Se.size?(0,p.isArray)(Se.size)?Se.size:(0,p.isObject)(Se.size)?[Se.size.width||40,Se.size.height||40]:[Se.size,Se.size]:[40,40]};var S=function(Se){return Se&&Se.size?(0,p.isArray)(Se.size)?Se.size:[Se.size,Se.size]:[80,40]},k=S(s==null?void 0:s[0]),A=k[0],K=k[1],R=(e=(r=a.graphSettings)===null||r===void 0?void 0:r.subScene)===null||e===void 0?void 0:e.meta,P=((o=s.find(function(Se){return!Se.collapsed}))===null||o===void 0?void 0:o.padding)||[20,20,20,20],N=P[0],U=P[1],B=P[2],ue=P[3],ae={compound:D,nodes:x([],(i||[]).map(function(Se){var Le=b(Se),q=Le[0],se=Le[1];return C(C({},Se),{width:q,height:se})}),!0),edges:x([],(f||[]).map(function(Se){return C(C({},Se),{v:Se.source,w:Se.target})}),!0)},de={expanded:(s||[]).filter(function(Se){return!Se.collapsed}).map(function(Se){return Se.id})},ve={graph:{meta:{align:h,rankDir:m,nodeSep:v,edgeSep:d,rankSep:y}},subScene:{meta:{paddingTop:N||(R==null?void 0:R.paddingTop)||20,paddingRight:U||(R==null?void 0:R.paddingRight)||20,paddingBottom:B||(R==null?void 0:R.paddingBottom)||20,paddingLeft:ue||(R==null?void 0:R.paddingLeft)||20,labelHeight:0}},nodeSize:{meta:{width:A,height:K}}},we=(0,E.mergeConfig)(_,C({},(0,E.mergeConfig)(ve,E.LAYOUT_CONFIG)));return a.graphSettings=we,{graphDef:ae,graphOption:de,graphSettings:we}},n.prototype.updatePosition=function(r){var e=r.nodes,o=r.edges;this.updateNodePosition(e,o),this.updateEdgePosition(e,o)},n.prototype.getBegin=function(r,e){var o=this,a=o.begin,i=[0,0];if(a){var f=1/0,s=1/0;r.forEach(function(l){f>l.x&&(f=l.x),s>l.y&&(s=l.y)}),e.forEach(function(l){l.points.forEach(function(m){f>m.x&&(f=m.x),s>m.y&&(s=m.y)})}),i[0]=a[0]-f,i[1]=a[1]-s}return i},n.prototype.updateNodePosition=function(r,e){var o=this,a=o.combos,i=o.nodes,f=o.edges,s=o.anchorPoint,l=o.graphSettings,m=this.getBegin(r,e);r.forEach(function(h){var d,v=h.x,y=h.y,_=h.id,D=h.type,b=h.coreBox;if(D===E.HierarchyNodeType.META&&_!==E.ROOT_NAME){var S=a.findIndex(function(P){return P.id===_}),k=(d=l==null?void 0:l.subScene)===null||d===void 0?void 0:d.meta;a[S].offsetX=v+m[0],a[S].offsetY=y+m[1],a[S].fixSize=[b.width,b.height],a[S].fixCollapseSize=[b.width,b.height],h.expanded?a[S].padding=[k==null?void 0:k.paddingTop,k==null?void 0:k.paddingRight,k==null?void 0:k.paddingBottom,k==null?void 0:k.paddingLeft]:a[S].padding=[0,0,0,0]}else if(D===E.HierarchyNodeType.OP){var S=i.findIndex(function(N){return N.id===_});if(i[S].x=v+m[0],i[S].y=y+m[1],s){var A=[],K=e.filter(function(N){return N.v===_}),R=e.filter(function(N){return N.w===_});K.length>0&&K.forEach(function(N){var U=N.points[0],B=(U.x-v)/h.width+.5,ue=(U.y-y)/h.height+.5;A.push([B,ue]),N.baseEdgeList.forEach(function(ae){var de=f.find(function(ve){return ve.source===ae.v&&ve.target===ae.w});de&&(de.sourceAnchor=A.length-1)})}),R.length>0&&R.forEach(function(N){var U=N.points[N.points.length-1],B=(U.x-v)/h.width+.5,ue=(U.y-y)/h.height+.5;A.push([B,ue]),N.baseEdgeList.forEach(function(ae){var de=f.find(function(ve){return ve.source===ae.v&&ve.target===ae.w});de&&(de.targetAnchor=A.length-1)})}),i[S].anchorPoints=A.length>0?A:i[S].anchorPoints||[]}}})},n.prototype.updateEdgePosition=function(r,e){var o=this,a=o.combos,i=o.edges,f=o.controlPoints,s=this.getBegin(r,e);f&&(a.forEach(function(l){l.inEdges=[],l.outEdges=[]}),i.forEach(function(l){var m,h,d,v,y=r.find(function(P){return P.id===l.source}),_=r.find(function(P){return P.id===l.target}),D=[],b=[];if(y&&_)b=(0,E.getEdges)(y==null?void 0:y.id,_==null?void 0:_.id,r);else if(!y||!_){var S=o.getNodePath(l.source),k=o.getNodePath(l.target),A=S.reverse().slice(y?0:1).find(function(P){return r.find(function(N){return N.id===P})}),K=k.reverse().slice(_?0:1).find(function(P){return r.find(function(N){return N.id===P})});y=r.find(function(P){return P.id===A}),_=r.find(function(P){return P.id===K}),b=(0,E.getEdges)(y==null?void 0:y.id,_==null?void 0:_.id,r,{v:l.source,w:l.target})}if(D=b.reduce(function(P,N){return x(x([],P,!0),N.points.map(function(U){return C(C({},U),{x:U.x+s[0],y:U.y+s[1]})}),!0)},[]),D=D.slice(1,-1),l.controlPoints=D,(_==null?void 0:_.type)===E.NodeType.META){var R=a.findIndex(function(P){return P.id===(_==null?void 0:_.id)});if(!a[R]||!((m=a[R].inEdges)===null||m===void 0)&&m.some(function(P){return P.source===y.id&&P.target===_.id}))return;(h=a[R].inEdges)===null||h===void 0||h.push({source:y.id,target:_.id,controlPoints:D})}if((y==null?void 0:y.type)===E.NodeType.META){var R=a.findIndex(function(N){return N.id===(y==null?void 0:y.id)});if(!a[R]||!((d=a[R].outEdges)===null||d===void 0)&&d.some(function(N){return N.source===y.id&&N.target===_.id}))return;(v=a[R].outEdges)===null||v===void 0||v.push({source:y.id,target:_.id,controlPoints:D})}}))},n.prototype.getType=function(){return"dagreCompound"},n.prototype.getDataByOrder=function(r){return r.every(function(e){return e.layoutOrder!==void 0})||r.forEach(function(e,o){e.layoutOrder=o}),r.sort(function(e,o){return e.layoutOrder-o.layoutOrder})},n}(M.Base);w.DagreCompoundLayout=u},49565:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var M=function(E,p){return M=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(u,t){u.__proto__=t}||function(u,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(u[n]=t[n])},M(E,p)};return function(E,p){if(typeof p!="function"&&p!==null)throw new TypeError("Class extends value "+String(p)+" is not a constructor or null");M(E,p);function u(){this.constructor=E}E.prototype=p===null?Object.create(p):(u.prototype=p.prototype,new u)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.Graph=void 0;var C=G(3231),x=function(M){T(E,M);function E(){return M!==null&&M.apply(this,arguments)||this}return E}(C.Graph);w.Graph=x},21753:function(Je,w,G){"use strict";var T=this&&this.__importDefault||function(M){return M&&M.__esModule?M:{default:M}};Object.defineProperty(w,"__esModule",{value:!0});var C=T(G(37001)),x=G(51279);w.default={layout:C.default,util:{time:x.time,notime:x.notime}}},64514:function(Je,w,G){"use strict";var T=this&&this.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(w,"__esModule",{value:!0});var C=T(G(22659)),x=function(p){var u=function(n){return function(r){var e;return((e=n.edge(r))===null||e===void 0?void 0:e.weight)||1}},t=p.graph().acyclicer==="greedy"?(0,C.default)(p,u(p)):M(p);t==null||t.forEach(function(n){var r=p.edge(n);p.removeEdgeObj(n),r.forwardName=n.name,r.reversed=!0,p.setEdge(n.w,n.v,r,"rev-".concat(Math.random()))})},M=function(p){var u=[],t={},n={},r=function(e){var o;n[e]||(n[e]=!0,t[e]=!0,(o=p.outEdges(e))===null||o===void 0||o.forEach(function(a){t[a.w]?u.push(a):r(a.w)}),delete t[e])};return p.nodes().forEach(r),u},E=function(p){p.edges().forEach(function(u){var t=p.edge(u);if(t.reversed){p.removeEdgeObj(u);var n=t.forwardName;delete t.reversed,delete t.forwardName,p.setEdge(u.w,u.v,t,n)}})};w.default={run:x,undo:E}},46365:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(51279),C=function(M){var E,p=function(u){var t=M.children(u),n=M.node(u);if(t!=null&&t.length&&t.forEach(function(o){return p(o)}),n.hasOwnProperty("minRank")){n.borderLeft=[],n.borderRight=[];for(var r=n.minRank,e=n.maxRank+1;r<e;r+=1)x(M,"borderLeft","_bl",u,n,r),x(M,"borderRight","_br",u,n,r)}};(E=M.children())===null||E===void 0||E.forEach(function(u){return p(u)})},x=function(M,E,p,u,t,n){var r={rank:n,borderType:E,width:0,height:0},e=t[E][n-1],o=(0,T.addDummyNode)(M,"border",r,p);t[E][n]=o,M.setParent(o,u),e&&M.setEdge(e,o,{weight:1})};w.default=C},8467:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(t){var n,r=(n=t.graph().rankdir)===null||n===void 0?void 0:n.toLowerCase();(r==="lr"||r==="rl")&&C(t)},T=function(t){var n,r=(n=t.graph().rankdir)===null||n===void 0?void 0:n.toLowerCase();(r==="bt"||r==="rl")&&M(t),(r==="lr"||r==="rl")&&(p(t),C(t))},C=function(t){t.nodes().forEach(function(n){x(t.node(n))}),t.edges().forEach(function(n){x(t.edge(n))})},x=function(t){var n=t.width;t.width=t.height,t.height=n},M=function(t){t.nodes().forEach(function(n){E(t.node(n))}),t.edges().forEach(function(n){var r,e=t.edge(n);(r=e.points)===null||r===void 0||r.forEach(function(o){return E(o)}),e.hasOwnProperty("y")&&E(e)})},E=function(t){t!=null&&t.y&&(t.y=-t.y)},p=function(t){t.nodes().forEach(function(n){u(t.node(n))}),t.edges().forEach(function(n){var r,e=t.edge(n);(r=e.points)===null||r===void 0||r.forEach(function(o){return u(o)}),e.hasOwnProperty("x")&&u(e)})},u=function(t){var n=t.x;t.x=t.y,t.y=n};w.default={adjust:G,undo:T}},42297:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(x,M){if(x!=="next"&&x!=="prev")return M},T=function(x){x.prev.next=x.next,x.next.prev=x.prev,delete x.next,delete x.prev},C=function(){function x(){var M={};M.prev=M,M.next=M.prev,this.shortcut=M}return x.prototype.dequeue=function(){var M=this.shortcut,E=M.prev;if(E&&E!==M)return T(E),E},x.prototype.enqueue=function(M){var E=this.shortcut;M.prev&&M.next&&T(M),M.next=E.next,E.next.prev=M,E.next=M,M.prev=E},x.prototype.toString=function(){for(var M=[],E=this.shortcut,p=E.prev;p!==E;)M.push(JSON.stringify(p,G)),p=p==null?void 0:p.prev;return"[".concat(M.join(", "),"]")},x}();w.default=C},22659:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var a=function(i,f){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,l){s.__proto__=l}||function(s,l){for(var m in l)Object.prototype.hasOwnProperty.call(l,m)&&(s[m]=l[m])},a(i,f)};return function(i,f){if(typeof f!="function"&&f!==null)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");a(i,f);function s(){this.constructor=i}i.prototype=f===null?Object.create(f):(s.prototype=f.prototype,new s)}}(),C=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(w,"__esModule",{value:!0});var x=C(G(42297)),M=G(3231),E=function(a){T(i,a);function i(){return a!==null&&a.apply(this,arguments)||this}return i}(x.default),p=function(a){T(i,a);function i(){return a!==null&&a.apply(this,arguments)||this}return i}(M.Graph),u=function(){return 1},t=function(a,i){var f;if(a.nodeCount()<=1)return[];var s=e(a,i||u),l=n(s.graph,s.buckets,s.zeroIdx);return(f=l.map(function(m){return a.outEdges(m.v,m.w)}))===null||f===void 0?void 0:f.flat()},n=function(a,i,f){for(var s=[],l=i[i.length-1],m=i[0],h;a.nodeCount();){for(;h=m.dequeue();)r(a,i,f,h);for(;h=l.dequeue();)r(a,i,f,h);if(a.nodeCount()){for(var d=i.length-2;d>0;--d)if(h=i[d].dequeue(),h){s=s.concat(r(a,i,f,h,!0));break}}}return s},r=function(a,i,f,s,l){var m,h,d=[];return(m=a.inEdges(s.v))===null||m===void 0||m.forEach(function(v){var y=a.edge(v),_=a.node(v.v);l&&d.push({v:v.v,w:v.w,in:0,out:0}),_.out===void 0&&(_.out=0),_.out-=y,o(i,f,_)}),(h=a.outEdges(s.v))===null||h===void 0||h.forEach(function(v){var y=a.edge(v),_=v.w,D=a.node(_);D.in===void 0&&(D.in=0),D.in-=y,o(i,f,D)}),a.removeNode(s.v),l?d:void 0},e=function(a,i){var f=new p,s=0,l=0;a.nodes().forEach(function(y){f.setNode(y,{v:y,in:0,out:0})}),a.edges().forEach(function(y){var _=f.edge(y)||0,D=(i==null?void 0:i(y))||1,b=_+D;f.setEdge(y.v,y.w,b),l=Math.max(l,f.node(y.v).out+=D),s=Math.max(s,f.node(y.w).in+=D)});for(var m=[],h=l+s+3,d=0;d<h;d++)m.push(new E);var v=s+1;return f.nodes().forEach(function(y){o(m,v,f.node(y))}),{buckets:m,zeroIdx:v,graph:f}},o=function(a,i,f){f.out?f.in?a[f.out-f.in+i].enqueue(f):a[a.length-1].enqueue(f):a[0].enqueue(f)};w.default=t},37001:function(Je,w,G){"use strict";var T=this&&this.__assign||function(){return T=Object.assign||function(q){for(var se,ne=1,pe=arguments.length;ne<pe;ne++){se=arguments[ne];for(var Me in se)Object.prototype.hasOwnProperty.call(se,Me)&&(q[Me]=se[Me])}return q},T.apply(this,arguments)},C=this&&this.__importDefault||function(q){return q&&q.__esModule?q:{default:q}};Object.defineProperty(w,"__esModule",{value:!0});var x=C(G(64514)),M=C(G(27809)),E=C(G(26758)),p=G(51279),u=C(G(74626)),t=C(G(95879)),n=C(G(46365)),r=C(G(8467)),e=C(G(77129)),o=C(G(90403)),a=C(G(59368)),i=G(49565),f=function(q,se){var ne=se&&se.debugTiming?p.time:p.notime;ne("layout",function(){se&&!se.keepNodeOrder&&se.prevGraph&&ne("  inheritOrder",function(){l(q,se.prevGraph)});var pe=ne("  buildLayoutGraph",function(){return k(q)});se&&se.edgeLabelSpace===!1||ne("  makeSpaceForEdgeLabels",function(){A(pe)});try{ne("  runLayout",function(){s(pe,ne,se)})}catch(Me){if(Me.message==="Not possible to find intersection inside of the rectangle"){console.error(`The following error may be caused by improper layer setting, please make sure your manual layer setting does not violate the graph's structure:
`,Me);return}throw Me}ne("  updateInputGraph",function(){m(q,pe)})})},s=function(q,se,ne){se("    removeSelfEdges",function(){de(q)}),se("    acyclic",function(){x.default.run(q)}),se("    nestingGraph.run",function(){t.default.run(q)}),se("    rank",function(){(0,E.default)((0,p.asNonCompoundGraph)(q))}),se("    injectEdgeLabelProxies",function(){K(q)}),se("    removeEmptyRanks",function(){(0,p.removeEmptyRanks)(q)}),se("    nestingGraph.cleanup",function(){t.default.cleanup(q)}),se("    normalizeRanks",function(){(0,p.normalizeRanks)(q)}),se("    assignRankMinMax",function(){R(q)}),se("    removeEdgeLabelProxies",function(){P(q)}),se("    normalize.run",function(){M.default.run(q)}),se("    parentDummyChains",function(){(0,u.default)(q)}),se("    addBorderSegments",function(){(0,n.default)(q)}),ne&&ne.keepNodeOrder&&se("    initDataOrder",function(){(0,a.default)(q,ne.nodeOrder)}),se("    order",function(){(0,e.default)(q,ne==null?void 0:ne.keepNodeOrder)}),se("    insertSelfEdges",function(){ve(q)}),se("    adjustCoordinateSystem",function(){r.default.adjust(q)}),se("    position",function(){(0,o.default)(q)}),se("    positionSelfEdges",function(){we(q)}),se("    removeBorderNodes",function(){ae(q)}),se("    normalize.undo",function(){M.default.undo(q)}),se("    fixupEdgeLabelCoords",function(){B(q)}),se("    undoCoordinateSystem",function(){r.default.undo(q)}),se("    translateGraph",function(){N(q)}),se("    assignNodeIntersects",function(){U(q)}),se("    reversePoints",function(){ue(q)}),se("    acyclic.undo",function(){x.default.undo(q)})},l=function(q,se){q.nodes().forEach(function(ne){var pe=q.node(ne),Me=se.node(ne);Me!==void 0?(pe.fixorder=Me._order,delete Me._order):delete pe.fixorder})},m=function(q,se){q.nodes().forEach(function(ne){var pe,Me=q.node(ne);if(Me){var he=se.node(ne);Me.x=he.x,Me.y=he.y,Me._order=he.order,Me._rank=he.rank,!((pe=se.children(ne))===null||pe===void 0)&&pe.length&&(Me.width=he.width,Me.height=he.height)}}),q.edges().forEach(function(ne){var pe=q.edge(ne),Me=se.edge(ne);pe.points=Me?Me.points:[],Me&&Me.hasOwnProperty("x")&&(pe.x=Me.x,pe.y=Me.y)}),q.graph().width=se.graph().width,q.graph().height=se.graph().height},h=["nodesep","edgesep","ranksep","marginx","marginy"],d={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},v=["acyclicer","ranker","rankdir","align"],y=["width","height","layer","fixorder"],_={width:0,height:0},D=["minlen","weight","width","height","labeloffset"],b={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},S=["labelpos"],k=function(q){var se=new i.Graph({multigraph:!0,compound:!0}),ne=Le(q.graph()),pe={};return v==null||v.forEach(function(Me){ne[Me]!==void 0&&(pe[Me]=ne[Me])}),se.setGraph(Object.assign({},d,Se(ne,h),pe)),q.nodes().forEach(function(Me){var he=Le(q.node(Me)),De=T(T({},_),he),Pe=Se(De,y);se.setNode(Me,Pe),se.setParent(Me,q.parent(Me))}),q.edges().forEach(function(Me){var he=Le(q.edge(Me)),De={};S==null||S.forEach(function(Pe){he[Pe]!==void 0&&(De[Pe]=he[Pe])}),se.setEdgeObj(Me,Object.assign({},b,Se(he,D),De))}),se},A=function(q){var se=q.graph();se.ranksep||(se.ranksep=0),se.ranksep/=2,q.nodes().forEach(function(ne){var pe=q.node(ne);isNaN(pe.layer)||pe.layer||(pe.layer=0)}),q.edges().forEach(function(ne){var pe,Me=q.edge(ne);Me.minlen*=2,((pe=Me.labelpos)===null||pe===void 0?void 0:pe.toLowerCase())!=="c"&&(se.rankdir==="TB"||se.rankdir==="BT"?Me.width+=Me.labeloffset:Me.height+=Me.labeloffset)})},K=function(q){q.edges().forEach(function(se){var ne=q.edge(se);if(ne.width&&ne.height){var pe=q.node(se.v),Me=q.node(se.w),he={e:se,rank:(Me.rank-pe.rank)/2+pe.rank};(0,p.addDummyNode)(q,"edge-proxy",he,"_ep")}})},R=function(q){var se=0;q.nodes().forEach(function(ne){var pe,Me,he=q.node(ne);he.borderTop&&(he.minRank=(pe=q.node(he.borderTop))===null||pe===void 0?void 0:pe.rank,he.maxRank=(Me=q.node(he.borderBottom))===null||Me===void 0?void 0:Me.rank,se=Math.max(se,he.maxRank||-1/0))}),q.graph().maxRank=se},P=function(q){q.nodes().forEach(function(se){var ne=q.node(se);ne.dummy==="edge-proxy"&&(q.edge(ne.e).labelRank=ne.rank,q.removeNode(se))})},N=function(q){var se,ne=0,pe,Me=0,he=q.graph(),De=he.marginx||0,Pe=he.marginy||0,He=function(Ze){if(Ze){var Ue=Ze.x,tt=Ze.y,pt=Ze.width,Mt=Ze.height;!isNaN(Ue)&&!isNaN(pt)&&(se===void 0&&(se=Ue-pt/2),se=Math.min(se,Ue-pt/2),ne=Math.max(ne,Ue+pt/2)),!isNaN(tt)&&!isNaN(Mt)&&(pe===void 0&&(pe=tt-Mt/2),pe=Math.min(pe,tt-Mt/2),Me=Math.max(Me,tt+Mt/2))}};q.nodes().forEach(function(Ze){He(q.node(Ze))}),q.edges().forEach(function(Ze){var Ue=q.edge(Ze);Ue!=null&&Ue.hasOwnProperty("x")&&He(Ue)}),se-=De,pe-=Pe,q.nodes().forEach(function(Ze){var Ue=q.node(Ze);Ue&&(Ue.x-=se,Ue.y-=pe)}),q.edges().forEach(function(Ze){var Ue,tt=q.edge(Ze);(Ue=tt.points)===null||Ue===void 0||Ue.forEach(function(pt){pt.x-=se,pt.y-=pe}),tt.hasOwnProperty("x")&&(tt.x-=se),tt.hasOwnProperty("y")&&(tt.y-=pe)}),he.width=ne-se+De,he.height=Me-pe+Pe},U=function(q){q.edges().forEach(function(se){var ne=q.edge(se),pe=q.node(se.v),Me=q.node(se.w),he,De;ne.points?(he=ne.points[0],De=ne.points[ne.points.length-1]):(ne.points=[],he=Me,De=pe),ne.points.unshift((0,p.intersectRect)(pe,he)),ne.points.push((0,p.intersectRect)(Me,De))})},B=function(q){q.edges().forEach(function(se){var ne=q.edge(se);if(ne!=null&&ne.hasOwnProperty("x"))switch((ne.labelpos==="l"||ne.labelpos==="r")&&(ne.width-=ne.labeloffset),ne.labelpos){case"l":ne.x-=ne.width/2+ne.labeloffset;break;case"r":ne.x+=ne.width/2+ne.labeloffset;break}})},ue=function(q){q.edges().forEach(function(se){var ne,pe=q.edge(se);pe.reversed&&((ne=pe.points)===null||ne===void 0||ne.reverse())})},ae=function(q){q.nodes().forEach(function(se){var ne,pe,Me;if(!((ne=q.children(se))===null||ne===void 0)&&ne.length){var he=q.node(se),De=q.node(he.borderTop),Pe=q.node(he.borderBottom),He=q.node(he.borderLeft[((pe=he.borderLeft)===null||pe===void 0?void 0:pe.length)-1]),Ze=q.node(he.borderRight[((Me=he.borderRight)===null||Me===void 0?void 0:Me.length)-1]);he.width=Math.abs((Ze==null?void 0:Ze.x)-(He==null?void 0:He.x))||10,he.height=Math.abs((Pe==null?void 0:Pe.y)-(De==null?void 0:De.y))||10,he.x=((He==null?void 0:He.x)||0)+he.width/2,he.y=((De==null?void 0:De.y)||0)+he.height/2}}),q.nodes().forEach(function(se){var ne;((ne=q.node(se))===null||ne===void 0?void 0:ne.dummy)==="border"&&q.removeNode(se)})},de=function(q){q.edges().forEach(function(se){if(se.v===se.w){var ne=q.node(se.v);ne.selfEdges||(ne.selfEdges=[]),ne.selfEdges.push({e:se,label:q.edge(se)}),q.removeEdgeObj(se)}})},ve=function(q){var se=(0,p.buildLayerMatrix)(q);se==null||se.forEach(function(ne){var pe=0;ne==null||ne.forEach(function(Me,he){var De,Pe=q.node(Me);Pe.order=he+pe,(De=Pe.selfEdges)===null||De===void 0||De.forEach(function(He){(0,p.addDummyNode)(q,"selfedge",{width:He.label.width,height:He.label.height,rank:Pe.rank,order:he+ ++pe,e:He.e,label:He.label},"_se")}),delete Pe.selfEdges})})},we=function(q){q.nodes().forEach(function(se){var ne=q.node(se);if(ne.dummy==="selfedge"){var pe=q.node(ne.e.v),Me=pe.x+pe.width/2,he=pe.y,De=ne.x-Me,Pe=pe.height/2;q.setEdgeObj(ne.e,ne.label),q.removeNode(se),ne.label.points=[{x:Me+2*De/3,y:he-Pe},{x:Me+5*De/6,y:he-Pe},{y:he,x:Me+De},{x:Me+5*De/6,y:he+Pe},{x:Me+2*De/3,y:he+Pe}],ne.label.x=ne.x,ne.label.y=ne.y}})},Se=function(q,se){var ne={};return se==null||se.forEach(function(pe){q[pe]!==void 0&&(ne[pe]=+q[pe])}),ne},Le=function(q){q===void 0&&(q={});var se={};return Object.keys(q).forEach(function(ne){se[ne.toLowerCase()]=q[ne]}),se};w.default=f},95879:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(51279),C=function(u){var t,n=(0,T.addDummyNode)(u,"root",{},"_root"),r=M(u),e=Math.max.apply(Math,Object.values(r));Math.abs(e)===1/0&&(e=1);var o=e-1,a=2*o+1;u.graph().nestingRoot=n,u.edges().forEach(function(f){u.edge(f).minlen*=a});var i=E(u)+1;(t=u.children())===null||t===void 0||t.forEach(function(f){x(u,n,a,i,o,r,f)}),u.graph().nodeRankFactor=a},x=function(u,t,n,r,e,o,a){var i=u.children(a);if(!(i!=null&&i.length)){a!==t&&u.setEdge(t,a,{weight:0,minlen:n});return}var f=(0,T.addBorderNode)(u,"_bt"),s=(0,T.addBorderNode)(u,"_bb"),l=u.node(a);u.setParent(f,a),l.borderTop=f,u.setParent(s,a),l.borderBottom=s,i==null||i.forEach(function(m){x(u,t,n,r,e,o,m);var h=u.node(m),d=h.borderTop?h.borderTop:m,v=h.borderBottom?h.borderBottom:m,y=h.borderTop?r:2*r,_=d!==v?1:e-o[a]+1;u.setEdge(f,d,{minlen:_,weight:y,nestingEdge:!0}),u.setEdge(v,s,{minlen:_,weight:y,nestingEdge:!0})}),u.parent(a)||u.setEdge(t,f,{weight:0,minlen:e+o[a]})},M=function(u){var t,n={},r=function(e,o){var a=u.children(e);a==null||a.forEach(function(i){return r(i,o+1)}),n[e]=o};return(t=u.children())===null||t===void 0||t.forEach(function(e){return r(e,1)}),n},E=function(u){var t=0;return u.edges().forEach(function(n){t+=u.edge(n).weight}),t},p=function(u){var t=u.graph();t.nestingRoot&&u.removeNode(t.nestingRoot),delete t.nestingRoot,u.edges().forEach(function(n){var r=u.edge(n);r.nestingEdge&&u.removeEdgeObj(n)})};w.default={run:C,cleanup:p}},27809:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(51279),C=function(E){E.graph().dummyChains=[],E.edges().forEach(function(p){return x(E,p)})},x=function(E,p){var u=p.v,t=E.node(u).rank,n=p.w,r=E.node(n).rank,e=p.name,o=E.edge(p),a=o.labelRank;if(r!==t+1){E.removeEdgeObj(p);var i=E.graph(),f,s,l;for(l=0,++t;t<r;++l,++t)o.points=[],s={edgeLabel:o,width:0,height:0,edgeObj:p,rank:t},f=(0,T.addDummyNode)(E,"edge",s,"_d"),t===a&&(s.width=o.width,s.height=o.height,s.dummy="edge-label",s.labelpos=o.labelpos),E.setEdge(u,f,{weight:o.weight},e),l===0&&(i.dummyChains||(i.dummyChains=[]),i.dummyChains.push(f)),u=f;E.setEdge(u,n,{weight:o.weight},e)}},M=function(E){var p;(p=E.graph().dummyChains)===null||p===void 0||p.forEach(function(u){var t=E.node(u),n=t.edgeLabel,r;t.edgeObj&&E.setEdgeObj(t.edgeObj,n);for(var e=u;t.dummy;)r=E.successors(e)[0],E.removeNode(e),n.points.push({x:t.x,y:t.y}),t.dummy==="edge-label"&&(n.x=t.x,n.y=t.y,n.width=t.width,n.height=t.height),e=r,t=E.node(e)})};w.default={run:C,undo:M}},93009:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(T,C,x){var M={},E;x==null||x.forEach(function(p){for(var u=T.parent(p),t,n;u;){if(t=T.parent(u),t?(n=M[t],M[t]=u):(n=E,E=u),n&&n!==u){C.setEdge(n,u);return}u=t}})};w.default=G},49654:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(T,C){return C.map(function(x){var M=T.inEdges(x);if(!(M!=null&&M.length))return{v:x};{var E={sum:0,weight:0};return M==null||M.forEach(function(p){var u=T.edge(p),t=T.node(p.v);E.sum+=u.weight*t.order,E.weight+=u.weight}),{v:x,barycenter:E.sum/E.weight,weight:E.weight}}})};w.default=G},83146:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(49565),C=function(M,E,p){var u=x(M),t=new T.Graph({compound:!0}).setGraph({root:u}).setDefaultNodeLabel(function(n){return M.node(n)});return M.nodes().forEach(function(n){var r,e=M.node(n),o=M.parent(n);(e.rank===E||e.minRank<=E&&E<=e.maxRank)&&(t.setNode(n),t.setParent(n,o||u),(r=M[p](n))===null||r===void 0||r.forEach(function(a){var i=a.v===n?a.w:a.v,f=t.edgeFromArgs(i,n),s=f!==void 0?f.weight:0;t.setEdge(i,n,{weight:M.edge(a).weight+s})}),e.hasOwnProperty("minRank")&&t.setNode(n,{borderLeft:e.borderLeft[E],borderRight:e.borderRight[E]}))}),t},x=function(M){for(var E;M.hasNode(E="_root".concat(Math.random())););return E};w.default=C},94542:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(51279),C=function(M,E,p){for(var u=(0,T.zipObject)(p,p.map(function(i,f){return f})),t=E.map(function(i){var f,s=(f=M.outEdges(i))===null||f===void 0?void 0:f.map(function(l){return{pos:u[l.w]||0,weight:M.edge(l).weight}});return s==null?void 0:s.sort(function(l,m){return l.pos-m.pos})}),n=t.flat().filter(function(i){return i!==void 0}),r=1;r<p.length;)r<<=1;var e=2*r-1;r-=1;var o=Array(e).fill(0,0,e),a=0;return n==null||n.forEach(function(i){if(i){var f=i.pos+r;o[f]+=i.weight;for(var s=0;f>0;)f%2&&(s+=o[f+1]),f=f-1>>1,o[f]+=i.weight;a+=i.weight*s}}),a},x=function(M,E){for(var p=0,u=1;u<(E==null?void 0:E.length);u+=1)p+=C(M,E[u-1],E[u]);return p};w.default=x},77129:function(Je,w,G){"use strict";var T=this&&this.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(w,"__esModule",{value:!0});var C=T(G(7584)),x=T(G(94542)),M=T(G(83146)),E=T(G(93009)),p=T(G(60788)),u=G(45612),t=G(49565),n=G(51279),r=function(i,f){for(var s=(0,n.maxRank)(i),l=[],m=[],h=1;h<s+1;h++)l.push(h);for(var h=s-1;h>-1;h--)m.push(h);var d=e(i,l,"inEdges"),v=e(i,m,"outEdges"),y=(0,C.default)(i);a(i,y);for(var _=Number.POSITIVE_INFINITY,D,h=0,b=0;b<4;++h,++b){o(h%2?d:v,h%4>=2,!1,f),y=(0,n.buildLayerMatrix)(i);var S=(0,x.default)(i,y);S<_&&(b=0,D=(0,u.clone)(y),_=S)}y=(0,C.default)(i),a(i,y);for(var h=0,b=0;b<4;++h,++b){o(h%2?d:v,h%4>=2,!0,f),y=(0,n.buildLayerMatrix)(i);var S=(0,x.default)(i,y);S<_&&(b=0,D=(0,u.clone)(y),_=S)}a(i,D)},e=function(i,f,s){return f.map(function(l){return(0,M.default)(i,l,s)})},o=function(i,f,s,l){var m=new t.Graph;i==null||i.forEach(function(h){for(var d,v=h.graph().root,y=(0,p.default)(h,v,m,f,s,l),_=0;_<((d=y.vs)===null||d===void 0?void 0:d.length);_++){var D=h.node(y.vs[_]);D&&(D.order=_)}(0,E.default)(h,m,y.vs)})},a=function(i,f){f==null||f.forEach(function(s){s==null||s.forEach(function(l,m){i.node(l).order=m})})};w.default=r},59368:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(T,C){for(var x=T.nodes().filter(function(t){var n;return!(!((n=T.children(t))===null||n===void 0)&&n.length)}),M=x.map(function(t){return T.node(t).rank}),E=Math.max.apply(Math,M),p=[],u=0;u<E+1;u++)p[u]=[];C==null||C.forEach(function(t){var n=T.node(t);!n||n!=null&&n.dummy||isNaN(n.rank)||(n.fixorder=p[n.rank].length,p[n.rank].push(t))})};w.default=G},7584:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(39694),C=function(x){for(var M={},E=x.nodes().filter(function(i){var f;return!(!((f=x.children(i))===null||f===void 0)&&f.length)}),p=E.map(function(i){return x.node(i).rank}),u=(0,T.max)(p),t=[],n=0;n<u+1;n++)t.push([]);var r=function(i){var f;if(!M.hasOwnProperty(i)){M[i]=!0;var s=x.node(i);isNaN(s.rank)||t[s.rank].push(i),(f=x.successors(i))===null||f===void 0||f.forEach(function(l){return r(l)})}},e=E.sort(function(i,f){return x.node(i).rank-x.node(f).rank}),o=e.filter(function(i){return x.node(i).fixorder!==void 0}),a=o.sort(function(i,f){return x.node(i).fixorder-x.node(f).fixorder});return a==null||a.forEach(function(i){isNaN(x.node(i).rank)||t[x.node(i).rank].push(i),M[i]=!0}),e==null||e.forEach(r),t};w.default=C},53631:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(x,M){var E,p,u,t={};x==null||x.forEach(function(r,e){t[r.v]={i:e,indegree:0,in:[],out:[],vs:[r.v]};var o=t[r.v];r.barycenter!==void 0&&(o.barycenter=r.barycenter,o.weight=r.weight)}),(E=M.edges())===null||E===void 0||E.forEach(function(r){var e=t[r.v],o=t[r.w];e!==void 0&&o!==void 0&&(o.indegree++,e.out.push(t[r.w]))});var n=(u=(p=Object.values(t)).filter)===null||u===void 0?void 0:u.call(p,function(r){return!r.indegree});return T(n)},T=function(x){for(var M,E,p=[],u=function(o){return function(a){a.merged||(a.barycenter===void 0||o.barycenter===void 0||a.barycenter>=o.barycenter)&&C(o,a)}},t=function(o){return function(a){a.in.push(o),--a.indegree===0&&x.push(a)}},n=function(){var o=x.pop();p.push(o),(M=o.in.reverse())===null||M===void 0||M.forEach(function(a){return u(o)(a)}),(E=o.out)===null||E===void 0||E.forEach(function(a){return t(o)(a)})};x!=null&&x.length;)n();var r=p.filter(function(o){return!o.merged}),e=["vs","i","barycenter","weight"];return r.map(function(o){var a={};return e==null||e.forEach(function(i){o[i]!==void 0&&(a[i]=o[i])}),a})},C=function(x,M){var E,p=0,u=0;x.weight&&(p+=x.barycenter*x.weight,u+=x.weight),M.weight&&(p+=M.barycenter*M.weight,u+=M.weight),x.vs=(E=M.vs)===null||E===void 0?void 0:E.concat(x.vs),x.barycenter=p/u,x.weight=u,x.i=Math.min(M.i,x.i),M.merged=!0};w.default=G},60788:function(Je,w,G){"use strict";var T=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(w,"__esModule",{value:!0});var C=T(G(49654)),x=T(G(53631)),M=T(G(88664)),E=function(t,n,r,e,o,a){var i,f,s,l,m=t.children(n),h=t.node(n),d=h?h.borderLeft:void 0,v=h?h.borderRight:void 0,y={};d&&(m=m==null?void 0:m.filter(function(A){return A!==d&&A!==v}));var _=(0,C.default)(t,m||[]);_==null||_.forEach(function(A){var K;if(!((K=t.children(A.v))===null||K===void 0)&&K.length){var R=E(t,A.v,r,e,a);y[A.v]=R,R.hasOwnProperty("barycenter")&&u(A,R)}});var D=(0,x.default)(_,r);p(D,y),(i=D.filter(function(A){return A.vs.length>0}))===null||i===void 0||i.forEach(function(A){var K=t.node(A.vs[0]);K&&(A.fixorder=K.fixorder,A.order=K.order)});var b=(0,M.default)(D,e,o,a);if(d&&(b.vs=[d,b.vs,v].flat(),!((f=t.predecessors(d))===null||f===void 0)&&f.length)){var S=t.node(((s=t.predecessors(d))===null||s===void 0?void 0:s[0])||""),k=t.node(((l=t.predecessors(v))===null||l===void 0?void 0:l[0])||"");b.hasOwnProperty("barycenter")||(b.barycenter=0,b.weight=0),b.barycenter=(b.barycenter*b.weight+S.order+k.order)/(b.weight+2),b.weight+=2}return b},p=function(t,n){t==null||t.forEach(function(r){var e,o=(e=r.vs)===null||e===void 0?void 0:e.map(function(a){return n[a]?n[a].vs:a});r.vs=o.flat()})},u=function(t,n){t.barycenter!==void 0?(t.barycenter=(t.barycenter*t.weight+n.barycenter*n.weight)/(t.weight+n.weight),t.weight+=n.weight):(t.barycenter=n.barycenter,t.weight=n.weight)};w.default=E},88664:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(51279),C=function(E,p,u,t){var n=(0,T.partition)(E,function(l){var m=l.hasOwnProperty("fixorder")&&!isNaN(l.fixorder);return t?!m&&l.hasOwnProperty("barycenter"):m||l.hasOwnProperty("barycenter")}),r=n.lhs,e=n.rhs.sort(function(l,m){return-l.i- -m.i}),o=[],a=0,i=0,f=0;r==null||r.sort(M(!!p,!!u)),f=x(o,e,f),r==null||r.forEach(function(l){var m;f+=(m=l.vs)===null||m===void 0?void 0:m.length,o.push(l.vs),a+=l.barycenter*l.weight,i+=l.weight,f=x(o,e,f)});var s={vs:o.flat()};return i&&(s.barycenter=a/i,s.weight=i),s},x=function(E,p,u){for(var t=u,n;p.length&&(n=p[p.length-1]).i<=t;)p.pop(),E==null||E.push(n.vs),t++;return t},M=function(E,p){return function(u,t){if(u.fixorder!==void 0&&t.fixorder!==void 0)return u.fixorder-t.fixorder;if(u.barycenter<t.barycenter)return-1;if(u.barycenter>t.barycenter)return 1;if(p&&u.order!==void 0&&t.order!==void 0){if(u.order<t.order)return-1;if(u.order>t.order)return 1}return E?t.i-u.i:u.i-t.i}};w.default=C},74626:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(x){var M,E={},p=0,u=function(t){var n,r=p;(n=x.children(t))===null||n===void 0||n.forEach(u),E[t]={low:r,lim:p++}};return(M=x.children())===null||M===void 0||M.forEach(u),E},T=function(x,M,E,p){var u=[],t=[],n=Math.min(M[E].low,M[p].low),r=Math.max(M[E].lim,M[p].lim),e,o;e=E;do e=x.parent(e),u.push(e);while(e&&(M[e].low>n||r>M[e].lim));for(o=e,e=p;e&&e!==o;)t.push(e),e=x.parent(e);return{lca:o,path:u.concat(t.reverse())}},C=function(x){var M,E=G(x);(M=x.graph().dummyChains)===null||M===void 0||M.forEach(function(p){var u,t,n=p,r=x.node(n),e=r.edgeObj;if(e)for(var o=T(x,E,e.v,e.w),a=o.path,i=o.lca,f=0,s=a[f],l=!0;n!==e.w;){if(r=x.node(n),l){for(;s!==i&&((u=x.node(s))===null||u===void 0?void 0:u.maxRank)<r.rank;)f++,s=a[f];s===i&&(l=!1)}if(!l){for(;f<a.length-1&&((t=x.node(a[f+1]))===null||t===void 0?void 0:t.minRank)<=r.rank;)f++;s=a[f]}x.setParent(n,s),n=x.successors(n)[0]}})};w.default=C},10830:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var d=function(v,y){return d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(_,D){_.__proto__=D}||function(_,D){for(var b in D)Object.prototype.hasOwnProperty.call(D,b)&&(_[b]=D[b])},d(v,y)};return function(v,y){if(typeof y!="function"&&y!==null)throw new TypeError("Class extends value "+String(y)+" is not a constructor or null");d(v,y);function _(){this.constructor=v}v.prototype=y===null?Object.create(y):(_.prototype=y.prototype,new _)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.width=w.sep=w.positionX=w.balance=w.alignCoordinates=w.findSmallestWidthAlignment=w.buildBlockGraph=w.horizontalCompaction=w.verticalAlignment=w.hasConflict=w.addConflict=w.findOtherInnerSegmentNode=w.findType2Conflicts=w.findType1Conflicts=void 0;var C=G(3231),x=G(39694),M=G(51279),E=function(d){T(v,d);function v(){return d!==null&&d.apply(this,arguments)||this}return v}(C.Graph),p=function(d,v){var y={},_=function(D,b){var S=0,k=0,A=D.length,K=b==null?void 0:b[(b==null?void 0:b.length)-1];return b==null||b.forEach(function(R,P){var N,U=(0,w.findOtherInnerSegmentNode)(d,R),B=U?d.node(U).order:A;(U||R===K)&&((N=b.slice(k,P+1))===null||N===void 0||N.forEach(function(ue){var ae;(ae=d.predecessors(ue))===null||ae===void 0||ae.forEach(function(de){var ve,we=d.node(de),Se=we.order;(Se<S||B<Se)&&!(we.dummy&&(!((ve=d.node(ue))===null||ve===void 0)&&ve.dummy))&&(0,w.addConflict)(y,de,ue)})}),k=P+1,S=B)}),b};return v!=null&&v.length&&v.reduce(_),y};w.findType1Conflicts=p;var u=function(d,v){var y={};function _(k,A,K,R,P){for(var N,U,B,ue=A;ue<K;ue++)B=k[ue],!((N=d.node(B))===null||N===void 0)&&N.dummy&&((U=d.predecessors(B))===null||U===void 0||U.forEach(function(ae){var de=d.node(ae);de.dummy&&(de.order<R||de.order>P)&&(0,w.addConflict)(y,ae,B)}))}function D(k){return JSON.stringify(k.slice(1))}function b(k,A){var K=D(k);A.get(K)||(_.apply(void 0,k),A.set(K,!0))}var S=function(k,A){var K=-1,R,P=0,N=new Map;return A==null||A.forEach(function(U,B){var ue;if(((ue=d.node(U))===null||ue===void 0?void 0:ue.dummy)==="border"){var ae=d.predecessors(U)||[];ae.length&&(R=d.node(ae[0]).order,b([A,P,B,K,R],N),P=B,K=R)}b([A,P,A.length,R,k.length],N)}),A};return v!=null&&v.length&&v.reduce(S),y};w.findType2Conflicts=u;var t=function(d,v){var y,_;if(!((y=d.node(v))===null||y===void 0)&&y.dummy)return(_=d.predecessors(v))===null||_===void 0?void 0:_.find(function(D){return d.node(D).dummy})};w.findOtherInnerSegmentNode=t;var n=function(d,v,y){var _=v,D=y;if(_>D){var b=_;_=D,D=b}var S=d[_];S||(d[_]=S={}),S[D]=!0};w.addConflict=n;var r=function(d,v,y){var _=v,D=y;if(_>D){var b=v;_=D,D=b}return!!d[_]};w.hasConflict=r;var e=function(d,v,y,_){var D={},b={},S={};return v==null||v.forEach(function(k){k==null||k.forEach(function(A,K){D[A]=A,b[A]=A,S[A]=K})}),v==null||v.forEach(function(k){var A=-1;k==null||k.forEach(function(K){var R=_(K);if(R.length){R=R.sort(function(ue,ae){return S[ue]-S[ae]});for(var P=(R.length-1)/2,N=Math.floor(P),U=Math.ceil(P);N<=U;++N){var B=R[N];b[K]===K&&A<S[B]&&!(0,w.hasConflict)(y,K,B)&&(b[B]=K,b[K]=D[K]=D[B],A=S[B])}}})}),{root:D,align:b}};w.verticalAlignment=e;var o=function(d,v,y,_,D){var b,S={},k=(0,w.buildBlockGraph)(d,v,y,D),A=D?"borderLeft":"borderRight",K=function(N,U){for(var B=k.nodes(),ue=B.pop(),ae={};ue;)ae[ue]?N(ue):(ae[ue]=!0,B.push(ue),B=B.concat(U(ue))),ue=B.pop()},R=function(N){S[N]=(k.inEdges(N)||[]).reduce(function(U,B){return Math.max(U,(S[B.v]||0)+k.edge(B))},0)},P=function(N){var U=(k.outEdges(N)||[]).reduce(function(ue,ae){return Math.min(ue,(S[ae.w]||0)-k.edge(ae))},Number.POSITIVE_INFINITY),B=d.node(N);U!==Number.POSITIVE_INFINITY&&B.borderType!==A&&(S[N]=Math.max(S[N],U))};return K(R,k.predecessors.bind(k)),K(P,k.successors.bind(k)),(b=Object.values(_))===null||b===void 0||b.forEach(function(N){S[N]=S[y[N]]}),S};w.horizontalCompaction=o;var a=function(d,v,y,_){var D=new E,b=d.graph(),S=(0,w.sep)(b.nodesep,b.edgesep,_);return v==null||v.forEach(function(k){var A;k==null||k.forEach(function(K){var R=y[K];if(D.setNode(R),A){var P=y[A],N=D.edgeFromArgs(P,R);D.setEdge(P,R,Math.max(S(d,K,A),N||0))}A=K})}),D};w.buildBlockGraph=a;var i=function(d,v){return(0,M.minBy)(Object.values(v),function(y){var _,D=Number.NEGATIVE_INFINITY,b=Number.POSITIVE_INFINITY;return(_=Object.keys(y))===null||_===void 0||_.forEach(function(S){var k=y[S],A=(0,w.width)(d,S)/2;D=Math.max(k+A,D),b=Math.min(k-A,b)}),D-b})};w.findSmallestWidthAlignment=i;function f(d,v){var y=Object.values(v),_=(0,x.min)(y),D=(0,x.max)(y);["u","d"].forEach(function(b){["l","r"].forEach(function(S){var k=b+S,A=d[k],K;if(A!==v){var R=Object.values(A);K=S==="l"?_-(0,x.min)(R):D-(0,x.max)(R),K&&(d[k]={},Object.keys(A).forEach(function(P){d[k][P]=A[P]+K}))}})})}w.alignCoordinates=f;var s=function(d,v){var y={};return Object.keys(d.ul).forEach(function(_){if(v)y[_]=d[v.toLowerCase()][_];else{var D=Object.values(d).map(function(b){return b[_]});y[_]=(D[0]+D[1])/2}}),y};w.balance=s;var l=function(d){var v=(0,M.buildLayerMatrix)(d),y=Object.assign((0,w.findType1Conflicts)(d,v),(0,w.findType2Conflicts)(d,v)),_={},D;["u","d"].forEach(function(S){D=S==="u"?v:Object.values(v).reverse(),["l","r"].forEach(function(k){k==="r"&&(D=D.map(function(P){return Object.values(P).reverse()}));var A=(S==="u"?d.predecessors:d.successors).bind(d),K=(0,w.verticalAlignment)(d,D,y,A),R=(0,w.horizontalCompaction)(d,D,K.root,K.align,k==="r");k==="r"&&Object.keys(R).forEach(function(P){R[P]=-R[P]}),_[S+k]=R})});var b=(0,w.findSmallestWidthAlignment)(d,_);return f(_,b),(0,w.balance)(_,d.graph().align)};w.positionX=l;var m=function(d,v,y){return function(_,D,b){var S=_.node(D),k=_.node(b),A=0,K;if(A+=S.width/2,S.hasOwnProperty("labelpos"))switch((S.labelpos||"").toLowerCase()){case"l":K=-S.width/2;break;case"r":K=S.width/2;break}if(K&&(A+=y?K:-K),K=0,A+=(S.dummy?v:d)/2,A+=(k.dummy?v:d)/2,A+=k.width/2,k.labelpos)switch((k.labelpos||"").toLowerCase()){case"l":K=k.width/2;break;case"r":K=-k.width/2;break}return K&&(A+=y?K:-K),K=0,A}};w.sep=m;var h=function(d,v){return d.node(v).width||0};w.width=h},90403:function(Je,w,G){"use strict";var T=this&&this.__spreadArray||function(u,t,n){if(n||arguments.length===2)for(var r=0,e=t.length,o;r<e;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return u.concat(o||Array.prototype.slice.call(t))};Object.defineProperty(w,"__esModule",{value:!0});var C=G(51279),x=G(10830),M=function(u){var t=(0,C.buildLayerMatrix)(u),n=u.graph().ranksep,r=0;t==null||t.forEach(function(e){var o=e.map(function(i){return u.node(i).height}),a=Math.max.apply(Math,T(T([],o,!1),[0],!1));e==null||e.forEach(function(i){u.node(i).y=r+a/2}),r+=a+n})},E=function(u){var t=(0,C.buildLayerMatrix)(u),n=Object.assign((0,x.findType1Conflicts)(u,t),(0,x.findType2Conflicts)(u,t)),r={},e=[];["u","d"].forEach(function(a){e=a==="u"?t:Object.values(t).reverse(),["l","r"].forEach(function(i){i==="r"&&(e=e.map(function(m){return Object.values(m).reverse()}));var f=(a==="u"?u.predecessors:u.successors).bind(u),s=(0,x.verticalAlignment)(u,e,n,f),l=(0,x.horizontalCompaction)(u,e,s.root,s.align,i==="r");i==="r"&&Object.keys(l).forEach(function(m){return l[m]=-l[m]}),r[a+i]=l})});var o=(0,x.findSmallestWidthAlignment)(u,r);return o&&(0,x.alignCoordinates)(r,o),(0,x.balance)(r,u.graph().align)},p=function(u){var t,n=(0,C.asNonCompoundGraph)(u);M(n);var r=E(n);(t=Object.keys(r))===null||t===void 0||t.forEach(function(e){n.node(e).x=r[e]})};w.default=p},41615:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.feasibleTreeWithLayer=w.feasibleTree=void 0;var T=G(8128),C=G(51279),x=G(49565),M=function(r){var e=new x.Graph({directed:!1}),o=r.nodes()[0],a=r.nodeCount();e.setNode(o,{});for(var i,f;E(e,r)<a;)i=t(e,r),f=e.hasNode(i.v)?(0,T.slack)(r,i):-(0,T.slack)(r,i),n(e,r,f);return e};w.feasibleTree=M;var E=function(r,e){var o=function(a){e.nodeEdges(a).forEach(function(i){var f=i.v,s=a===f?i.w:f;!r.hasNode(s)&&!(0,T.slack)(e,i)&&(r.setNode(s,{}),r.setEdge(a,s,{}),o(s))})};return r.nodes().forEach(o),r.nodeCount()},p=function(r){var e=new x.Graph({directed:!1}),o=r.nodes()[0],a=r.nodes().filter(function(s){return!!r.node(s)}).length;e.setNode(o,{});for(var i,f;u(e,r)<a;)i=t(e,r),f=e.hasNode(i.v)?(0,T.slack)(r,i):-(0,T.slack)(r,i),n(e,r,f);return e};w.feasibleTreeWithLayer=p;var u=function(r,e){var o=function(a){var i;(i=e.nodeEdges(a))===null||i===void 0||i.forEach(function(f){var s=f.v,l=a===s?f.w:s;!r.hasNode(l)&&(e.node(l).layer!==void 0||!(0,T.slack)(e,f))&&(r.setNode(l,{}),r.setEdge(a,l,{}),o(l))})};return r.nodes().forEach(o),r.nodeCount()},t=function(r,e){return(0,C.minBy)(e.edges(),function(o){return r.hasNode(o.v)!==r.hasNode(o.w)?(0,T.slack)(e,o):1/0})},n=function(r,e,o){r.nodes().forEach(function(a){e.node(a).rank||(e.node(a).rank=0),e.node(a).rank+=o})};w.default={feasibleTree:M,feasibleTreeWithLayer:p}},26758:function(Je,w,G){"use strict";var T=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(w,"__esModule",{value:!0});var C=G(8128),x=G(41615),M=T(G(9496)),E=function(n){switch(n.graph().ranker){case"network-simplex":t(n);break;case"tight-tree":u(n);break;case"longest-path":p(n);break;default:u(n)}},p=C.longestPath,u=function(n){(0,C.longestPathWithLayer)(n),(0,x.feasibleTreeWithLayer)(n)},t=function(n){(0,M.default)(n)};w.default=E},9496:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.exchangeEdges=w.enterEdge=w.leaveEdge=w.initLowLimValues=w.calcCutValue=w.initCutValues=void 0;var T=G(41615),C=G(8128),x=G(51279),M=G(3231),E=M.algorithm.preorder,p=M.algorithm.postorder,u=function(h){var d=(0,x.simplify)(h);(0,C.longestPath)(d);var v=(0,T.feasibleTree)(d);(0,w.initLowLimValues)(v),(0,w.initCutValues)(v,d);for(var y,_;y=(0,w.leaveEdge)(v);)_=(0,w.enterEdge)(v,d,y),(0,w.exchangeEdges)(v,d,y,_)},t=function(h,d){var v=p(h,h.nodes());v=v==null?void 0:v.slice(0,(v==null?void 0:v.length)-1),v==null||v.forEach(function(y){n(h,d,y)})};w.initCutValues=t;var n=function(h,d,v){var y=h.node(v),_=y.parent;h.edgeFromArgs(v,_).cutvalue=(0,w.calcCutValue)(h,d,v)},r=function(h,d,v){var y,_=h.node(v),D=_.parent,b=!0,S=d.edgeFromArgs(v,D),k=0;return S||(b=!1,S=d.edgeFromArgs(D,v)),k=S.weight,(y=d.nodeEdges(v))===null||y===void 0||y.forEach(function(A){var K=A.v===v,R=K?A.w:A.v;if(R!==D){var P=K===b,N=d.edge(A).weight;if(k+=P?N:-N,l(h,v,R)){var U=h.edgeFromArgs(v,R).cutvalue;k+=P?-U:U}}}),k};w.calcCutValue=r;var e=function(h,d){d===void 0&&(d=h.nodes()[0]),o(h,{},1,d)};w.initLowLimValues=e;var o=function(h,d,v,y,_){var D,b=v,S=v,k=h.node(y);return d[y]=!0,(D=h.neighbors(y))===null||D===void 0||D.forEach(function(A){d[A]||(S=o(h,d,S,A,y))}),k.low=b,k.lim=S++,_?k.parent=_:delete k.parent,S},a=function(h){return h.edges().find(function(d){return h.edge(d).cutvalue<0})};w.leaveEdge=a;var i=function(h,d,v){var y=v.v,_=v.w;d.hasEdge(y,_)||(y=v.w,_=v.v);var D=h.node(y),b=h.node(_),S=D,k=!1;D.lim>b.lim&&(S=b,k=!0);var A=d.edges().filter(function(K){return k===m(h,h.node(K.v),S)&&k!==m(h,h.node(K.w),S)});return(0,x.minBy)(A,function(K){return(0,C.slack)(d,K)})};w.enterEdge=i;var f=function(h,d,v,y){var _=v.v,D=v.w;h.removeEdge(_,D),h.setEdge(y.v,y.w,{}),(0,w.initLowLimValues)(h),(0,w.initCutValues)(h,d),s(h,d)};w.exchangeEdges=f;var s=function(h,d){var v=h.nodes().find(function(_){var D;return!(!((D=d.node(_))===null||D===void 0)&&D.parent)}),y=E(h,v);y=y==null?void 0:y.slice(1),y==null||y.forEach(function(_){var D=h.node(_).parent,b=d.edgeFromArgs(_,D),S=!1;b||(b=d.edgeFromArgs(D,_),S=!0),d.node(_).rank=d.node(D).rank+(S?b.minlen:-b.minlen)})},l=function(h,d,v){return h.hasEdge(d,v)},m=function(h,d,v){return v.low<=d.lim&&d.lim<=v.lim};w.default=u},8128:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.slack=w.longestPathWithLayer=w.longestPath=void 0;var G=function(x){var M,E={},p=function(u){var t,n=x.node(u);if(!n)return 0;if(E[u])return n.rank;E[u]=!0;var r;return(t=x.outEdges(u))===null||t===void 0||t.forEach(function(e){var o=p(e.w),a=x.edge(e).minlen,i=o-a;i&&(r===void 0||i<r)&&(r=i)}),r||(r=0),n.rank=r,r};(M=x.sources())===null||M===void 0||M.forEach(function(u){return p(u)})};w.longestPath=G;var T=function(x){var M,E={},p,u=function(r){var e,o=x.node(r);if(!o)return 0;if(E[r])return o.rank;E[r]=!0;var a;return(e=x.outEdges(r))===null||e===void 0||e.forEach(function(i){var f=u(i.w),s=x.edge(i).minlen,l=f-s;l&&(a===void 0||l<a)&&(a=l)}),a||(a=0),(p===void 0||a<p)&&(p=a),o.rank=a,a};(M=x.sources())===null||M===void 0||M.forEach(function(r){x.node(r)&&u(r)}),p===void 0&&(p=0);var t={},n=function(r,e){var o,a=x.node(r),i=isNaN(a.layer)?e:a.layer;(a.rank===void 0||a.rank<i)&&(a.rank=i),!t[r]&&(t[r]=!0,(o=x.outEdges(r))===null||o===void 0||o.map(function(f){n(f.w,i+x.edge(f).minlen)}))};x.nodes().forEach(function(r){var e=x.node(r);e&&(isNaN(e.layer)?e.rank-=p:n(r,e.layer))})};w.longestPathWithLayer=T;var C=function(x,M){return x.node(M.w).rank-x.node(M.v).rank-x.edge(M).minlen};w.slack=C,w.default={longestPath:G,longestPathWithLayer:T,slack:C}},51279:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.minBy=w.notime=w.time=w.partition=w.maxRank=w.addBorderNode=w.removeEmptyRanks=w.normalizeRanks=w.buildLayerMatrix=w.intersectRect=w.predecessorWeights=w.successorWeights=w.zipObject=w.asNonCompoundGraph=w.simplify=w.addDummyNode=void 0;var T=G(45612),C=G(49565),x=function(d,v){return Number(d)-Number(v)},M=function(d,v,y,_){var D;do D="".concat(_).concat(Math.random());while(d.hasNode(D));return y.dummy=v,d.setNode(D,y),D};w.addDummyNode=M;var E=function(d){var v=new C.Graph().setGraph(d.graph());return d.nodes().forEach(function(y){v.setNode(y,d.node(y))}),d.edges().forEach(function(y){var _=v.edgeFromArgs(y.v,y.w)||{weight:0,minlen:1},D=d.edge(y);v.setEdge(y.v,y.w,{weight:_.weight+D.weight,minlen:Math.max(_.minlen,D.minlen)})}),v};w.simplify=E;var p=function(d){var v=new C.Graph({multigraph:d.isMultigraph()}).setGraph(d.graph());return d.nodes().forEach(function(y){var _;!((_=d.children(y))===null||_===void 0)&&_.length||v.setNode(y,d.node(y))}),d.edges().forEach(function(y){v.setEdgeObj(y,d.edge(y))}),v};w.asNonCompoundGraph=p;var u=function(d,v){return d==null?void 0:d.reduce(function(y,_,D){return y[_]=v[D],y},{})};w.zipObject=u;var t=function(d){var v={};return d.nodes().forEach(function(y){var _,D={};(_=d.outEdges(y))===null||_===void 0||_.forEach(function(b){var S;D[b.w]=(D[b.w]||0)+(((S=d.edge(b))===null||S===void 0?void 0:S.weight)||0)}),v[y]=D}),v};w.successorWeights=t;var n=function(d){var v=d.nodes(),y=v.map(function(_){var D,b={};return(D=d.inEdges(_))===null||D===void 0||D.forEach(function(S){b[S.v]=(b[S.v]||0)+d.edge(S).weight}),b});return(0,w.zipObject)(v,y)};w.predecessorWeights=n;var r=function(d,v){var y=Number(d.x),_=Number(d.y),D=Number(v.x)-y,b=Number(v.y)-_,S=Number(d.width)/2,k=Number(d.height)/2;if(!D&&!b)return{x:0,y:0};var A,K;return Math.abs(b)*S>Math.abs(D)*k?(b<0&&(k=-k),A=k*D/b,K=k):(D<0&&(S=-S),A=S,K=S*b/D),{x:y+A,y:_+K}};w.intersectRect=r;var e=function(d){for(var v=[],y=(0,w.maxRank)(d)+1,_=0;_<y;_++)v.push([]);d.nodes().forEach(function(D){var b=d.node(D);if(b){var S=b.rank;S!==void 0&&v[S]&&v[S].push(D)}});for(var _=0;_<y;_++)v[_]=v[_].sort(function(b,S){var k,A;return x((k=d.node(b))===null||k===void 0?void 0:k.order,(A=d.node(S))===null||A===void 0?void 0:A.order)});return v};w.buildLayerMatrix=e;var o=function(d){var v=d.nodes().filter(function(_){var D;return((D=d.node(_))===null||D===void 0?void 0:D.rank)!==void 0}).map(function(_){return d.node(_).rank}),y=Math.min.apply(Math,v);d.nodes().forEach(function(_){var D=d.node(_);D.hasOwnProperty("rank")&&y!==1/0&&(D.rank-=y)})};w.normalizeRanks=o;var a=function(d){var v=d.nodes(),y=v.filter(function(K){var R;return((R=d.node(K))===null||R===void 0?void 0:R.rank)!==void 0}).map(function(K){return d.node(K).rank}),_=Math.min.apply(Math,y),D=[];v.forEach(function(K){var R,P=(((R=d.node(K))===null||R===void 0?void 0:R.rank)||0)-_;D[P]||(D[P]=[]),D[P].push(K)});for(var b=0,S=d.graph().nodeRankFactor||0,k=0;k<D.length;k++){var A=D[k];A===void 0?k%S!==0&&(b-=1):b&&(A==null||A.forEach(function(K){var R=d.node(K);R&&(R.rank=R.rank||0,R.rank+=b)}))}};w.removeEmptyRanks=a;var i=function(d,v,y,_){var D={width:0,height:0};return(0,T.isNumber)(y)&&(0,T.isNumber)(_)&&(D.rank=y,D.order=_),(0,w.addDummyNode)(d,"border",D,v)};w.addBorderNode=i;var f=function(d){var v;return d.nodes().forEach(function(y){var _,D=(_=d.node(y))===null||_===void 0?void 0:_.rank;D!==void 0&&(v===void 0||D>v)&&(v=D)}),v||(v=0),v};w.maxRank=f;var s=function(d,v){var y={lhs:[],rhs:[]};return d==null||d.forEach(function(_){v(_)?y.lhs.push(_):y.rhs.push(_)}),y};w.partition=s;var l=function(d,v){var y=Date.now();try{return v()}finally{console.log("".concat(d," time: ").concat(Date.now()-y,"ms"))}};w.time=l;var m=function(d,v){return v()};w.notime=m;var h=function(d,v){return d.reduce(function(y,_){var D=v(y),b=v(_);return D>b?_:y})};w.minBy=h},51561:function(Je,w,G){"use strict";var T=this&&this.__assign||function(){return T=Object.assign||function(e){for(var o,a=1,i=arguments.length;a<i;a++){o=arguments[a];for(var f in o)Object.prototype.hasOwnProperty.call(o,f)&&(e[f]=o[f])}return e},T.apply(this,arguments)},C=this&&this.__createBinding||(Object.create?function(e,o,a,i){i===void 0&&(i=a);var f=Object.getOwnPropertyDescriptor(o,a);(!f||("get"in f?!o.__esModule:f.writable||f.configurable))&&(f={enumerable:!0,get:function(){return o[a]}}),Object.defineProperty(e,i,f)}:function(e,o,a,i){i===void 0&&(i=a),e[i]=o[a]}),x=this&&this.__setModuleDefault||(Object.create?function(e,o){Object.defineProperty(e,"default",{enumerable:!0,value:o})}:function(e,o){e.default=o}),M=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var o={};if(e!=null)for(var a in e)a!=="default"&&Object.prototype.hasOwnProperty.call(e,a)&&C(o,e,a);return x(o,e),o},E=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(w,"__esModule",{value:!0});var p=M(G(13746)),u=E(G(45700)),t=E(G(77054)),n=G(78389);function r(e,o){var a=e.nodes,i=e.edges,f=o.width,s=o.height;if(!(a!=null&&a.length))return Promise.resolve();var l=[];a.forEach(function(b){var S=i.filter(function(A){return A.source===b.id||A.target===b.id});if(S.length>1){var k=T({},b);delete k.size,l.push(k)}});var m=[];i.forEach(function(b){var S=l.find(function(A){return A.id===b.source}),k=l.find(function(A){return A.id===b.target});S&&k&&m.push(b)});var h=new n.DagreLayout({type:"dagre",ranksep:o.nodeMinGap,nodesep:o.nodeMinGap}),d=h.layout({nodes:l,edges:m}).nodes;a.forEach(function(b){var S=(d||[]).find(function(k){return k.id===b.id});b.x=(S==null?void 0:S.x)||f/2,b.y=(S==null?void 0:S.y)||s/2});var v=JSON.parse(JSON.stringify(a)),y=JSON.parse(JSON.stringify(i)),_=p.forceSimulation().nodes(v).force("link",p.forceLink(y).id(function(b){return b.id}).distance(function(b){var S=m.find(function(k){return k.source===b.source&&k.target===b.target});return S?30:20})).force("charge",p.forceManyBody()).force("center",p.forceCenter(f/2,s/2)).force("x",p.forceX(f/2)).force("y",p.forceY(s/2)).alpha(.3).alphaDecay(.08).alphaMin(.001),D=new Promise(function(b){_.on("end",function(){a.forEach(function(N){var U=v.find(function(B){return B.id===N.id});U&&(N.x=U.x,N.y=U.y)});var S=Math.min.apply(Math,a.map(function(N){return N.x})),k=Math.max.apply(Math,a.map(function(N){return N.x})),A=Math.min.apply(Math,a.map(function(N){return N.y})),K=Math.max.apply(Math,a.map(function(N){return N.y})),R=f/(k-S),P=s/(K-A);a.forEach(function(N){N.x!==void 0&&R<1&&(N.x=(N.x-S)*R),N.y!==void 0&&P<1&&(N.y=(N.y-A)*P)}),a.forEach(function(N){N.sizeTemp=N.size,N.size=[10,10]}),(0,t.default)(a,i),a.forEach(function(N){N.size=N.sizeTemp||[],delete N.sizeTemp}),(0,u.default)({nodes:a,edges:i},o),b()})});return D}w.default=r},45700:function(Je,w,G){"use strict";var T=this&&this.__importDefault||function(M){return M&&M.__esModule?M:{default:M}};Object.defineProperty(w,"__esModule",{value:!0});var C=T(G(18597));function x(M,E){if(!M.nodes||M.nodes.length===0)return M;var p=E.width,u=E.height,t=E.nodeMinGap,n=1e4,r=1e4;M.nodes.forEach(function(y){var _=y.size[0]||50,D=y.size[1]||50;n=Math.min(_,n),r=Math.min(D,r)});var e=new C.default;e.init(p,u,{CELL_H:r,CELL_W:n}),M.nodes.forEach(function(y){var _=e.occupyNearest(y);_&&(_.node={id:y.id,size:y.size},y.x=_.x,y.y=_.y,y.dx=_.dx,y.dy=_.dy)});for(var o=0;o<M.nodes.length;o++){var a=M.nodes[o],i=e.findGridByNodeId(a.id);if(!i)throw new Error("can not find node cell");var f=i.column,s=i.row;if(a.size[0]+t>n){for(var l=Math.ceil((a.size[0]+t)/n)-1,m=l,h=0;h<l;h++){var d=e.additionColumn.indexOf(f+h+1)>-1;if(d&&!e.cells[f+h+1][s].node)m--;else break}e.insertColumn(f,m)}if(a.size[1]+t>r){for(var l=Math.ceil((a.size[1]+t)/r)-1,m=l,h=0;h<l;h++){var d=e.additionRow.indexOf(s+h+1)>-1;if(d&&!e.cells[f][s+h+1].node)m--;else break}e.insertRow(s,m)}}for(var o=0;o<e.columnNum;o++)for(var v=function(_){var D=e.cells[o][_];if(D.node){var b=M.nodes.find(function(S){var k;return S.id===((k=D==null?void 0:D.node)===null||k===void 0?void 0:k.id)});b&&(b.x=D.x+b.size[0]/2,b.y=D.y+b.size[1]/2)}},h=0;h<e.rowNum;h++)v(h)}w.default=x},18597:function(Je,w){"use strict";var G=this&&this.__assign||function(){return G=Object.assign||function(C){for(var x,M=1,E=arguments.length;M<E;M++){x=arguments[M];for(var p in x)Object.prototype.hasOwnProperty.call(x,p)&&(C[p]=x[p])}return C},G.apply(this,arguments)};Object.defineProperty(w,"__esModule",{value:!0});var T=function(){function C(){this.cells=[],this.columnNum=0,this.rowNum=0,this.additionColumn=[],this.additionRow=[]}return C.prototype.init=function(x,M,E){this.cells=[],this.CELL_W=E.CELL_W||C.DEFAULT_CELL_W,this.CELL_H=E.CELL_H||C.DEFAULT_CELL_H,this.columnNum=Math.ceil(x/this.CELL_W),this.rowNum=Math.ceil(M/this.CELL_H),C.MIN_DIST=Math.pow(x,2)+Math.pow(M,2);for(var p=0;p<this.columnNum;p++){for(var u=[],t=0;t<this.rowNum;t++){var n={dx:p,dy:t,x:p*this.CELL_W,y:t*this.CELL_H,occupied:!1};u.push(n)}this.cells.push(u)}},C.prototype.findGridByNodeId=function(x){for(var M,E,p=0;p<this.columnNum;p++)for(var u=0;u<this.rowNum;u++)if(this.cells[p][u].node&&((E=(M=this.cells[p][u])===null||M===void 0?void 0:M.node)===null||E===void 0?void 0:E.id)===x)return{column:p,row:u};return null},C.prototype.sqdist=function(x,M){return Math.pow(x.x-M.x,2)+Math.pow(x.y-M.y,2)},C.prototype.occupyNearest=function(x){for(var M=C.MIN_DIST,E,p=null,u=0;u<this.columnNum;u++)for(var t=0;t<this.rowNum;t++)!this.cells[u][t].occupied&&(E=this.sqdist(x,this.cells[u][t]))<M&&(M=E,p=this.cells[u][t]);return p&&(p.occupied=!0),p},C.prototype.insertColumn=function(x,M){if(!(M<=0)){for(var E=0;E<M;E++){this.cells[E+this.columnNum]=[];for(var p=0;p<this.rowNum;p++)this.cells[E+this.columnNum][p]={dx:E,dy:p,x:E*this.CELL_W,y:p*this.CELL_H,occupied:!1,node:null}}for(var E=this.columnNum-1;E>x;E--)for(var p=0;p<this.rowNum;p++)this.cells[E+M][p]=G(G({},this.cells[E][p]),{x:(E+M)*this.CELL_W,y:p*this.CELL_H}),this.cells[E][p]={x:E*this.CELL_W,y:p*this.CELL_H,occupied:!0,node:null};for(var p=0;p<this.additionColumn.length;p++)this.additionColumn[p]>=x&&(this.additionColumn[p]+=M);for(var E=0;E<M;E++)this.additionColumn.push(x+E+1);this.columnNum+=M}},C.prototype.insertRow=function(x,M){if(!(M<=0)){for(var E=0;E<M;E++)for(var p=0;p<this.columnNum;p++)this.cells[p][E+this.rowNum]={dx:p,dy:E,x:p*this.CELL_W,y:E*this.CELL_H,occupied:!1,node:null};for(var p=0;p<this.columnNum;p++)for(var E=this.rowNum-1;E>x;E--)this.cells[p][E+M]=G(G({},this.cells[p][E]),{dx:p,dy:E+M,x:p*this.CELL_W,y:(E+M)*this.CELL_H}),this.cells[p][E]={dx:p,dy:E,x:p*this.CELL_W,y:E*this.CELL_H,occupied:!1,node:null};for(var E=0;E<this.additionRow.length;E++)this.additionRow[E]>=x&&(this.additionRow[E]+=M);for(var p=0;p<M;p++)this.additionRow.push(x+p+1);this.rowNum+=M}},C.prototype.getNodes=function(){for(var x=[],M=0;M<this.columnNum;M++)for(var E=0;E<this.rowNum;E++)this.cells[M][E].node&&x.push(this.cells[M][E]);return x},C.MIN_DIST=50,C.DEFAULT_CELL_W=80,C.DEFAULT_CELL_H=80,C}();w.default=T},66812:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var p=function(u,t){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])},p(u,t)};return function(u,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");p(u,t);function n(){this.constructor=u}u.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),C=this&&this.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(w,"__esModule",{value:!0}),w.ERLayout=void 0;var x=G(2021),M=C(G(51561)),E=function(p){T(u,p);function u(t){var n=p.call(this)||this;return n.width=300,n.height=300,n.nodeMinGap=50,n.onLayoutEnd=function(){},t&&n.updateCfg(t),n}return u.prototype.getDefaultCfg=function(){return{width:300,height:300,nodeMinGap:50}},u.prototype.execute=function(){var t=this,n=t.nodes,r=t.edges;return n==null||n.forEach(function(e){e.size||(e.size=[50,50])}),(0,M.default)({nodes:n,edges:r},{width:this.width,height:this.height,nodeMinGap:this.nodeMinGap}).then(function(){t.onLayoutEnd&&t.onLayoutEnd()})},u.prototype.getType=function(){return"er"},u}(x.Base);w.ERLayout=E},77054:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=1200,T=800,C=1e7,x=10,M=3.141592653589793,E=1.5707963267948966,p=M*.375,u=M*.625,t=new Map,n=10,r=10,e=.8,o=.1,a=.5;function i(v,y,_){var D=v.x-v.size[0]/2,b=v.y-v.size[1]/2,S=v.x+v.size[0]/2,k=v.y+v.size[1]/2,A=y.x-y.size[0]/2,K=y.y-y.size[1]/2,R=y.x+y.size[0]/2,P=y.y+y.size[1]/2,N=v.x,U=v.y,B=y.x,ue=y.y,ae=B-N,de=Math.atan2(ae,ue-U),ve=0,we=0,Se=0,Le=0;de>E?(we=b-P,ve=A-S,Se=parseFloat(we?(we/Math.cos(de)).toFixed(2):ve.toFixed(2)),Le=parseFloat(ve?(ve/Math.sin(de)).toFixed(2):we.toFixed(2))):0<de&&de<=E?(we=K-k,ve=A-S,we>ve?Se=Le=parseFloat(we?(we/Math.cos(de)).toFixed(2):ve.toFixed(2)):Se=Le=parseFloat(ve?(ve/Math.sin(de)).toFixed(2):we.toFixed(2))):de<-E?(we=b-P,ve=-(R-D),we>ve?Se=Le=parseFloat(we?(we/Math.cos(de)).toFixed(2):ve.toFixed(2)):Se=Le=parseFloat(ve?(ve/Math.sin(de)).toFixed(2):we.toFixed(2))):(we=K-k,Math.abs(ae)>(S-D)/2?ve=D-R:ve=ae,we>ve?Se=Le=parseFloat(we?(we/Math.cos(de)).toFixed(2):ve.toFixed(2)):Se=Le=parseFloat(ve&&de!==0?(ve/Math.sin(de)).toFixed(2):we.toFixed(2)));var q=parseFloat(de.toFixed(2)),se=_;return _&&(se=p<q&&q<u),{distance:Math.abs(Se<Le?Se:Le),isHoriz:se}}function f(v,y){var _=t.get(v.id)||[],D=_.find(function(tt){return tt.source===y.id||tt.target===y.id}),b=v.size[0]*v.size[1],S=y.size[0]*y.size[1],k=b>S?y:v,A=b>S?v:y,K=k.x-k.size[0]/2,R=k.y-k.size[1]/2,P=k.x+k.size[0]/2,N=k.y+k.size[1]/2,U=A.x-A.size[0]/2,B=A.y-A.size[1]/2,ue=A.x+A.size[0]/2,ae=A.y+A.size[1]/2,de=k.x,ve=k.y,we=A.x,Se=A.y,Le=P>=U&&ue>=K&&N>=B&&ae>=R,q=0,se=0;if(Le){se=Math.sqrt(Math.pow(we-de,2)+Math.pow(Se-ve,2));var ne=K>U?K:U,pe=R>B?R:B,Me=P<ue?P:ue,he=N<ae?N:ae,De=Me-ne,Pe=he-pe,He=De*Pe;se===0&&(se=1e-7),q=x*1/se*100+He,q*=C}else{var Ze=!1,Ue=i(k,A,Ze);se=Ue.distance,Ze=Ue.isHoriz,se<=x?se!==0?D?q+=x+C*1/se:q+=x+C*x/se:q+=C:(q+=se,D&&(q+=se*se))}return q}function s(v){for(var y=0,_=0;_<v.length;_++){var D=v[_];(D.x<0||D.y<0||D.x>G||D.y>T)&&(y+=1e12);for(var b=_+1;b<v.length;b++)y+=f(D,v[b])}return y}function l(v,y,_,D){var b=new Map;_.forEach(function(de,ve){b.set(de.id,de)});var S=D.filter(function(de){return de.source===v.id||de.target===v.id})||[],k=[];S.forEach(function(de){var ve=de.source===v.id?de.target:de.source,we=b.get(ve);we&&k.push(we)});for(var A=!0,K=0;K<k.length;K++){var R=k[K],P=Math.atan((v.y-R.y)/(R.x-v.y))*180,N=Math.atan((y.y-R.y)/(R.x-y.y))*180,U=P<30||P>150,B=N<30||N>150,ue=P>70&&P<110,ae=N>70&&N<110;if(U&&!B||P*N<0){A=!1;break}else if(ue&&!ae||P*N<0){A=!1;break}else if((R.x-v.x)*(R.x-y.x)<0){A=!1;break}else if((R.y-v.y)*(R.y-y.y)<0){A=!1;break}}return A}function m(v,y){for(var _=!1,D=1,b=n*D,S=r*D,k=[b,-b,0,0],A=[0,0,S,-S],K=0;K<v.length;++K)for(var R=v[K],P=h(R,v),N=0;N<k.length;N++){var U=l(R,{x:R.x+k[N],y:R.y+A[N]},v,y);if(U){R.x+=k[N],R.y+=A[N];var B=h(R,v),ue=Math.random();B<P||ue<e&&ue>o?(P=B,_=!0):(R.x-=k[N],R.y-=A[N])}}return e>o&&(e*=a),_?s(v):0}function h(v,y){var _=0;(v.x<0||v.y<0||v.x+v.size[0]+20>G||v.y+v.size[1]+20>T)&&(_+=1e12);for(var D=0;D<y.length;++D)v.id!==y[D].id&&(_+=f(v,y[D]));return _}function d(v,y){if(v.length===0)return{nodes:v,edges:y};v.forEach(function(R){var P=y.filter(function(N){return N.source===R.id||N.target===R.id});t.set(R,P)}),v.sort(function(R,P){var N,U;return((N=t.get(R.id))===null||N===void 0?void 0:N.length)-((U=t.get(P.id))===null||U===void 0?void 0:U.length)});for(var _=s(v),D=20,b=1,S=0,k=50,A=0;D>0&&(A++,!(A>=k));){var K=m(v,y);K!==0&&(S=K),b=S-_,_=S,b===0?--D:D=20}return v.forEach(function(R){R.x=R.x-R.size[0]/2,R.y=R.y-R.size[1]/2}),{nodes:v,edges:y}}w.default=d},35031:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.forceNBody=void 0;var T=G(39470),C=.81,x=.1;function M(t,n,r,e,o){var a=r/e,i=t.map(function(s,l){var m=n[s.id],h=m.data,d=m.x,v=m.y,y=m.size,_=h.layout.force.nodeStrength;return{x:d,y:v,size:y,index:l,vx:0,vy:0,weight:a*_}}),f=(0,T.quadtree)(i,function(s){return s.x},function(s){return s.y}).visitAfter(E);return i.forEach(function(s){u(s,f)}),i.map(function(s,l){var m=n[t[l].id],h=m.data.layout.force.mass,d=h===void 0?1:h;o[2*l]=s.vx/d,o[2*l+1]=s.vy/d}),o}w.forceNBody=M;function E(t){var n=0,r=0,e=0;if(t.length){for(var o=0;o<4;o++){var a=t[o];a&&a.weight&&(n+=a.weight,r+=a.x*a.weight,e+=a.y*a.weight)}t.x=r/n,t.y=e/n,t.weight=n}else{var a=t;t.x=a.data.x,t.y=a.data.y,t.weight=a.data.weight}}var p=function(t,n,r,e,o,a){var i=a.x-t.x||x,f=a.y-t.y||x,s=e-n,l=i*i+f*f,m=Math.sqrt(l)*l;if(s*s*C<l){var h=t.weight/m;return a.vx+=i*h,a.vy+=f*h,!0}if(t.length)return!1;if(t.data!==a){var h=t.data.weight/m;a.vx+=i*h,a.vy+=f*h}};function u(t,n){n.visit(function(r,e,o,a,i){return p(r,e,o,a,i,t)})}},87905:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])},t(n,r)};return function(n,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");t(n,r);function e(){this.constructor=n}n.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}}(),C=this&&this.__assign||function(){return C=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},C.apply(this,arguments)};Object.defineProperty(w,"__esModule",{value:!0}),w.Force2Layout=void 0;var x=G(2021),M=G(45612),E=G(35031),p=function(t,n){var r;return t?(0,M.isNumber)(t)?r=function(e){return t}:r=t:r=function(e){return n||1},r},u=function(t){T(n,t);function n(r){var e=t.call(this)||this;e.maxIteration=1e3,e.workerEnabled=!1,e.edgeStrength=200,e.nodeStrength=1e3,e.coulombDisScale=.005,e.damping=.9,e.maxSpeed=500,e.minMovement=.4,e.interval=.02,e.factor=1,e.linkDistance=200,e.gravity=0,e.clusterNodeStrength=20,e.preventOverlap=!0,e.distanceThresholdMode="mean",e.tick=function(){},e.nodes=[],e.edges=[],e.width=300,e.height=300,e.nodeMap={},e.nodeIdxMap={},e.judgingDistance=0,e.centripetalOptions={leaf:2,single:2,others:1,center:function(a){return{x:e.width/2,y:e.height/2}}};var o=r.getMass;return e.propsGetMass=o,e.updateCfg(r),e}return n.prototype.getCentripetalOptions=function(){var r=this,e=r.leafCluster,o=r.clustering,a=r.nodeClusterBy,i=r.nodes,f=r.nodeMap,s=r.clusterNodeStrength,l=function(k){return typeof s=="function"?s(k):s},m={},h;if(e){h=this.getSameTypeLeafMap()||{};var d=Array.from(new Set(i==null?void 0:i.map(function(k){return k[a]})))||[];m={single:100,leaf:function(k,A,K){var R=h[k.id]||{},P=R.relativeLeafNodes,N=R.sameTypeLeafNodes;return(N==null?void 0:N.length)===(P==null?void 0:P.length)||(d==null?void 0:d.length)===1?1:l(k)},others:1,center:function(k,A,K){var R,P=(((R=k.data)===null||R===void 0?void 0:R.layout)||{}).degree;if(!P)return{x:100,y:100};var N;if(P===1){var U=(h[k.id]||{}).sameTypeLeafNodes,B=U===void 0?[]:U;B.length===1?N=void 0:B.length>1&&(N=(0,M.getAvgNodePosition)(B))}else N=void 0;return{x:N==null?void 0:N.x,y:N==null?void 0:N.y}}}}if(o){h||(h=this.getSameTypeLeafMap());var v=Array.from(new Set(i.map(function(k,A){return k[a]}))).filter(function(k){return k!==void 0}),y={};v.forEach(function(k){var A=i.filter(function(K){return K[a]===k}).map(function(K){return f[K.id]});y[k]=(0,M.getAvgNodePosition)(A)}),m={single:function(k){return l(k)},leaf:function(k){return l(k)},others:function(k){return l(k)},center:function(k,A,K){var R=y[k[a]];return{x:R==null?void 0:R.x,y:R==null?void 0:R.y}}}}this.centripetalOptions=C(C({},this.centripetalOptions),m);var _=this.centripetalOptions,D=_.leaf,b=_.single,S=_.others;D&&typeof D!="function"&&(this.centripetalOptions.leaf=function(){return D}),b&&typeof b!="function"&&(this.centripetalOptions.single=function(){return b}),S&&typeof S!="function"&&(this.centripetalOptions.others=function(){return S})},n.prototype.updateCfg=function(r){r&&Object.assign(this,r)},n.prototype.getDefaultCfg=function(){return{maxIteration:500,gravity:10,enableTick:!0,animate:!0}},n.prototype.execute=function(){var r=this;r.stop();var e=r.nodes,o=r.edges,a=r.defSpringLen;if(r.judgingDistance=0,!e||e.length===0){r.onLayoutEnd([]);return}!r.width&&typeof window!="undefined"&&(r.width=window.innerWidth),!r.height&&typeof window!="undefined"&&(r.height=window.innerHeight),r.center||(r.center=[r.width/2,r.height/2]);var i=r.center;if(e.length===1){e[0].x=i[0],e[0].y=i[1],r.onLayoutEnd([C({},e[0])]);return}r.degreesMap=(0,M.getDegreeMap)(e,o),r.propsGetMass?r.getMass=r.propsGetMass:r.getMass=function(v){var y=1;(0,M.isNumber)(v.mass)&&(y=v.mass);var _=r.degreesMap[v.id].all;return!_||_<5?y:_*5*y};var f=r.nodeSize,s;if(r.preventOverlap){var l=r.nodeSpacing,m;(0,M.isNumber)(l)?m=function(){return l}:(0,M.isFunction)(l)?m=l:m=function(){return 0},f?(0,M.isArray)(f)?s=function(v){return Math.max(f[0],f[1])+m(v)}:s=function(v){return f+m(v)}:s=function(v){return v.size?(0,M.isArray)(v.size)?Math.max(v.size[0],v.size[1])+m(v):(0,M.isObject)(v.size)?Math.max(v.size.width,v.size.height)+m(v):v.size+m(v):10+m(v)}}r.nodeSize=s,r.linkDistance=p(r.linkDistance,1),r.nodeStrength=p(r.nodeStrength,1),r.edgeStrength=p(r.edgeStrength,1);var h={},d={};e.forEach(function(v,y){(0,M.isNumber)(v.x)||(v.x=Math.random()*r.width),(0,M.isNumber)(v.y)||(v.y=Math.random()*r.height);var _=r.degreesMap[v.id];h[v.id]=C(C({},v),{data:C(C({},v.data),{size:r.nodeSize(v)||30,layout:{inDegree:_.in,outDegree:_.out,degree:_.all,tDegree:_.in,sDegree:_.out,force:{mass:r.getMass(v),nodeStrength:r.nodeStrength(v,o)}}})}),d[v.id]=y}),r.nodeMap=h,r.nodeIdxMap=d,r.edgeInfos=[],o==null||o.forEach(function(v){var y=h[v.source],_=h[v.target];!y||!_?elf.edgeInfos.push({}):r.edgeInfos.push({edgeStrength:r.edgeStrength(v),linkDistance:a?a(C(C({},v),{source:y,target:_}),y,_):r.linkDistance(v,y,_)||1+(f(y)+f(y)||0)/2})}),this.getCentripetalOptions(),r.onLayoutEnd=r.onLayoutEnd||function(){},r.run()},n.prototype.run=function(){var r=this,e=r.maxIteration,o=r.nodes,a=r.edges,i=r.workerEnabled,f=r.minMovement,s=r.animate,l=r.nodeMap,m=r.height;if(r.currentMinY=0,r.currentMaxY=m,!!o){var h=[];if(o.forEach(function(S,k){h[2*k]=0,h[2*k+1]=0}),this.defSideCoe&&typeof this.defSideCoe=="function"){var d={};a.forEach(function(S){var k=S.source,A=S.target;d[k]=d[k]||[],d[k].push(S),d[A]=d[A]||[],d[A].push(S)}),this.relatedEdges=d}var v=e,y=!s;if(i||y){for(var _=0,D=0;(r.judgingDistance>f||D<1)&&D<v;D++)_=D,r.runOneStep(D,h);r.onLayoutEnd(Object.values(l))}else{if(typeof window=="undefined")return;var b=0;this.timeInterval=window.setInterval(function(){o&&(r.runOneStep(b,h),b++,(b>=v||r.judgingDistance<f)&&(r.onLayoutEnd(Object.values(l)),window.clearInterval(r.timeInterval)))},0)}}},n.prototype.runOneStep=function(r,e){var o,a=this,i=a.nodes,f=a.edges,s=a.nodeMap,l=a.monitor,m=[];if(i!=null&&i.length){a.calRepulsive(m),f&&a.calAttractive(m),a.calGravity(m),a.attractToSide(m);var h=a.interval;if(a.updateVelocity(m,e,h),a.updatePosition(e,h),(o=a.tick)===null||o===void 0||o.call(a),l){var d=this.calTotalEnergy(m);l({energy:d,nodes:i,edges:f,iterations:r})}}},n.prototype.calTotalEnergy=function(r){var e=this,o=e.nodes,a=e.nodeMap;if(!(o!=null&&o.length))return 0;var i=0;return o.forEach(function(f,s){var l=r[2*s],m=r[2*s+1],h=l*l+m*m,d=a[f.id].data.layout.force.mass,v=d===void 0?1:d;i+=v*h*.5}),i},n.prototype.calRepulsive=function(r){var e=this,o=e.nodes,a=e.nodeMap,i=e.factor,f=e.coulombDisScale,s=e.nodeSize;(0,E.forceNBody)(o,a,i,f*f,r)},n.prototype.calAttractive=function(r){var e=this,o=e.edges,a=e.nodeMap,i=e.nodeIdxMap,f=e.edgeInfos,s=e.nodeSize;o.forEach(function(l,m){var h=(0,M.getEdgeTerminal)(l,"source"),d=(0,M.getEdgeTerminal)(l,"target"),v=a[h],y=a[d];if(!(!v||!y)){var _=y.x-v.x,D=y.y-v.y;!_&&!D&&(_=Math.random()*.01,D=Math.random()*.01);var b=Math.sqrt(_*_+D*D),S=_/b,k=D/b,A=f[m]||{},K=A.linkDistance,R=K===void 0?200:K,P=A.edgeStrength,N=P===void 0?200:P,U=R-b,B=U*N,ue=v.data.layout.force.mass||1,ae=y.data.layout.force.mass||1,de=1/ue,ve=1/ae,we=S*B,Se=k*B,Le=2*i[h],q=2*i[d];r[Le]-=we*de,r[Le+1]-=Se*de,r[q]+=we*ve,r[q+1]+=Se*ve}})},n.prototype.calGravity=function(r){var e,o=this,a=o.nodes,i=o.edges,f=i===void 0?[]:i,s=o.nodeMap,l=o.width,m=o.height,h=o.center,d=o.gravity,v=o.degreesMap,y=o.centripetalOptions;if(a)for(var _=a.length,D=0;D<_;D++){var b=2*D,S=s[a[D].id],k=S.data.layout.force.mass,A=k===void 0?1:k,K=0,R=0,P=d,N=v[S.id],U=N.in,B=N.out,ue=N.all,ae=(e=o.getCenter)===null||e===void 0?void 0:e.call(o,S,ue);if(ae){var de=ae[0],ve=ae[1],we=ae[2];K=S.x-de,R=S.y-ve,P=we}else K=S.x-h[0],R=S.y-h[1];if(P&&(r[b]-=P*K/A,r[b+1]-=P*R/A),y){var Se=y.leaf,Le=y.single,q=y.others,se=y.center,ne=(se==null?void 0:se(S,a,f,l,m))||{x:0,y:0,centerStrength:0},pe=ne.x,Me=ne.y,he=ne.centerStrength;if(!(0,M.isNumber)(pe)||!(0,M.isNumber)(Me))continue;var De=(S.x-pe)/A,Pe=(S.y-Me)/A;if(he&&(r[b]-=he*De,r[b+1]-=he*Pe),ue===0){var He=Le(S);if(!He)continue;r[b]-=He*De,r[b+1]-=He*Pe;continue}if(U===0||B===0){var Ze=Se(S,a,f);if(!Ze)continue;r[b]-=Ze*De,r[b+1]-=Ze*Pe;continue}var Ue=q(S);if(!Ue)continue;r[b]-=Ue*De,r[b+1]-=Ue*Pe}}},n.prototype.attractToSide=function(r){var e=this,o=e.defSideCoe,a=e.height,i=e.nodes,f=e.relatedEdges,s=e.currentMinY,l=s===void 0?0:s,m=e.currentMaxY,h=m===void 0?this.height:m;!o||typeof o!="function"||!(i!=null&&i.length)||i.forEach(function(d,v){var y=o(d,f[d.id]||[]);if(y!==0){var _=y<0?l:h,D=Math.abs(y);r[2*v+1]-=D*(d.y-_)}})},n.prototype.updateVelocity=function(r,e,o){var a=this,i=a.nodes,f=a.damping,s=a.maxSpeed;i!=null&&i.length&&i.forEach(function(l,m){var h=(e[2*m]+r[2*m]*o)*f||.01,d=(e[2*m+1]+r[2*m+1]*o)*f||.01,v=Math.sqrt(h*h+d*d);if(v>s){var y=s/v;h=y*h,d=y*d}e[2*m]=h,e[2*m+1]=d})},n.prototype.updatePosition=function(r,e){var o=this,a=o.nodes,i=o.distanceThresholdMode,f=o.nodeMap;if(!(a!=null&&a.length)){this.judgingDistance=0;return}var s=0;i==="max"?o.judgingDistance=-1/0:i==="min"&&(o.judgingDistance=1/0);var l=1/0,m=-1/0;a.forEach(function(h,d){var v=f[h.id];if((0,M.isNumber)(h.fx)&&(0,M.isNumber)(h.fy)){h.x=h.fx,h.y=h.fy,v.x=h.x,v.y=h.y;return}var y=r[2*d]*e,_=r[2*d+1]*e;h.x+=y,h.y+=_,v.x=h.x,v.y=h.y,h.y<l&&(l=h.y),h.y>m&&(m=h.y);var D=Math.sqrt(y*y+_*_);switch(i){case"max":o.judgingDistance<D&&(o.judgingDistance=D);break;case"min":o.judgingDistance>D&&(o.judgingDistance=D);break;default:s=s+D;break}}),this.currentMinY=l,this.currentMaxY=m,(!i||i==="mean")&&(o.judgingDistance=s/a.length)},n.prototype.stop=function(){this.timeInterval&&typeof window!="undefined"&&window.clearInterval(this.timeInterval)},n.prototype.destroy=function(){var r=this;r.stop(),r.tick=null,r.nodes=null,r.edges=null,r.destroyed=!0},n.prototype.getType=function(){return"force2"},n.prototype.getSameTypeLeafMap=function(){var r=this,e=r.nodeClusterBy,o=r.nodes,a=r.edges,i=r.nodeMap,f=r.degreesMap;if(o!=null&&o.length){var s={};return o.forEach(function(l,m){var h=f[l.id].all;h===1&&(s[l.id]=(0,M.getCoreNodeAndRelativeLeafNodes)("leaf",l,a,e,f,i))}),s}},n}(x.Base);w.Force2Layout=u},75209:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(){function T(C){this.id=C.id||0,this.rx=C.rx,this.ry=C.ry,this.fx=0,this.fy=0,this.mass=C.mass,this.degree=C.degree,this.g=C.g||0}return T.prototype.distanceTo=function(C){var x=this.rx-C.rx,M=this.ry-C.ry;return Math.hypot(x,M)},T.prototype.setPos=function(C,x){this.rx=C,this.ry=x},T.prototype.resetForce=function(){this.fx=0,this.fy=0},T.prototype.addForce=function(C){var x=C.rx-this.rx,M=C.ry-this.ry,E=Math.hypot(x,M);E=E<1e-4?1e-4:E;var p=this.g*(this.degree+1)*(C.degree+1)/E;this.fx+=p*x/E,this.fy+=p*M/E},T.prototype.in=function(C){return C.contains(this.rx,this.ry)},T.prototype.add=function(C){var x=this.mass+C.mass,M=(this.rx*this.mass+C.rx*C.mass)/x,E=(this.ry*this.mass+C.ry*C.mass)/x,p=this.degree+C.degree,u={rx:M,ry:E,mass:x,degree:p};return new T(u)},T}();w.default=G},3856:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var n=function(r,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,a){o.__proto__=a}||function(o,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(o[i]=a[i])},n(r,e)};return function(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(r,e);function o(){this.constructor=r}r.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}}(),C=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(w,"__esModule",{value:!0}),w.ForceAtlas2Layout=void 0;var x=G(2021),M=G(45612),E=C(G(75209)),p=C(G(14523)),u=C(G(3008)),t=function(n){T(r,n);function r(e){var o=n.call(this)||this;return o.center=[0,0],o.width=300,o.height=300,o.nodes=[],o.edges=[],o.kr=5,o.kg=1,o.mode="normal",o.preventOverlap=!1,o.dissuadeHubs=!1,o.barnesHut=void 0,o.maxIteration=0,o.ks=.1,o.ksmax=10,o.tao=.1,o.onLayoutEnd=function(){},o.prune=void 0,o.updateCfg(e),o}return r.prototype.getDefaultCfg=function(){return{}},r.prototype.execute=function(){var e=this,o=e.nodes,a=e.onLayoutEnd,i=e.prune,f=e.maxIteration;!e.width&&typeof window!="undefined"&&(e.width=window.innerWidth),!e.height&&typeof window!="undefined"&&(e.height=window.innerHeight);for(var s=[],l=o.length,m=0;m<l;m+=1){var h=o[m],d=10,v=10;(0,M.isNumber)(h.size)&&(d=h.size,v=h.size),(0,M.isArray)(h.size)?(isNaN(h.size[0])||(d=h.size[0]),isNaN(h.size[1])||(v=h.size[1])):(0,M.isObject)(h.size)&&(d=h.size.width,v=h.size.height),e.getWidth&&!isNaN(e.getWidth(h))&&(v=e.getWidth(h)),e.getHeight&&!isNaN(e.getHeight(h))&&(d=e.getHeight(h));var y=Math.max(d,v);s.push(y)}e.barnesHut===void 0&&l>250&&(e.barnesHut=!0),e.prune===void 0&&l>100&&(e.prune=!0),this.maxIteration===0&&!e.prune?(f=250,l<=200&&l>100?f=1e3:l>200&&(f=1200),this.maxIteration=f):this.maxIteration===0&&i&&(f=100,l<=200&&l>100?f=500:l>200&&(f=950),this.maxIteration=f),e.kr||(e.kr=50,l>100&&l<=500?e.kr=20:l>500&&(e.kr=1)),e.kg||(e.kg=20,l>100&&l<=500?e.kg=10:l>500&&(e.kg=1)),this.nodes=e.updateNodesByForces(s),a()},r.prototype.updateNodesByForces=function(e){for(var o=this,a=o.edges,i=o.maxIteration,f=o.nodes,s=a.filter(function(N){var U=(0,M.getEdgeTerminal)(N,"source"),B=(0,M.getEdgeTerminal)(N,"target");return U!==B}),l=f.length,m=s.length,h=[],d={},v={},y=[],_=0;_<l;_+=1)d[f[_].id]=_,h[_]=0,(f[_].x===void 0||isNaN(f[_].x))&&(f[_].x=Math.random()*1e3),(f[_].y===void 0||isNaN(f[_].y))&&(f[_].y=Math.random()*1e3),y.push({x:f[_].x,y:f[_].y});for(var _=0;_<m;_+=1){for(var D=void 0,b=void 0,S=0,k=0,A=0;A<l;A+=1){var K=(0,M.getEdgeTerminal)(s[_],"source"),R=(0,M.getEdgeTerminal)(s[_],"target");f[A].id===K?(D=f[A],S=A):f[A].id===R&&(b=f[A],k=A),v[_]={sourceIdx:S,targetIdx:k}}D&&(h[d[D.id]]+=1),b&&(h[d[b.id]]+=1)}var P=i;if(f=this.iterate(P,d,v,m,h,e),o.prune){for(var A=0;A<m;A+=1)h[v[A].sourceIdx]<=1?(f[v[A].sourceIdx].x=f[v[A].targetIdx].x,f[v[A].sourceIdx].y=f[v[A].targetIdx].y):h[v[A].targetIdx]<=1&&(f[v[A].targetIdx].x=f[v[A].sourceIdx].x,f[v[A].targetIdx].y=f[v[A].sourceIdx].y);o.prune=!1,o.barnesHut=!1,P=100,f=this.iterate(P,d,v,m,h,e)}return f},r.prototype.iterate=function(e,o,a,i,f,s){for(var l=this,m=l.nodes,h=l.kr,d=l.preventOverlap,v=l.barnesHut,y=m.length,_=0,D=100,b=e,S=50,k=[],A=[],K=[],R=0;R<y;R+=1)if(k[2*R]=0,k[2*R+1]=0,v){var P={id:R,rx:m[R].x,ry:m[R].y,mass:1,g:h,degree:f[R]};K[R]=new E.default(P)}for(;b>0;){for(var R=0;R<y;R+=1)A[2*R]=k[2*R],A[2*R+1]=k[2*R+1],k[2*R]=0,k[2*R+1]=0;k=this.getAttrForces(b,S,i,o,a,f,s,k),v&&(d&&b>S||!d)?k=this.getOptRepGraForces(k,K,f):k=this.getRepGraForces(b,S,k,D,s,f);var N=this.updatePos(k,A,_,f);m=N.nodes,_=N.sg,b--,l.tick&&l.tick()}return m},r.prototype.getAttrForces=function(e,o,a,i,f,s,l,m){for(var h=this,d=h.nodes,v=h.preventOverlap,y=h.dissuadeHubs,_=h.mode,D=h.prune,b=0;b<a;b+=1){var S=d[f[b].sourceIdx],k=f[b].sourceIdx,A=d[f[b].targetIdx],K=f[b].targetIdx;if(!(D&&(s[k]<=1||s[K]<=1))){var R=[A.x-S.x,A.y-S.y],P=Math.hypot(R[0],R[1]);P=P<1e-4?1e-4:P,R[0]=R[0]/P,R[1]=R[1]/P,v&&e<o&&(P=P-l[k]-l[K]);var N=P,U=N;_==="linlog"&&(N=Math.log(1+P),U=N),y&&(N=P/s[k],U=P/s[K]),v&&e<o&&P<=0?(N=0,U=0):v&&e<o&&P>0&&(N=P,U=P),m[2*i[S.id]]+=N*R[0],m[2*i[A.id]]-=U*R[0],m[2*i[S.id]+1]+=N*R[1],m[2*i[A.id]+1]-=U*R[1]}}return m},r.prototype.getRepGraForces=function(e,o,a,i,f,s){for(var l=this,m=l.nodes,h=l.preventOverlap,d=l.kr,v=l.kg,y=l.center,_=l.prune,D=m.length,b=0;b<D;b+=1){for(var S=b+1;S<D;S+=1)if(!(_&&(s[b]<=1||s[S]<=1))){var k=[m[S].x-m[b].x,m[S].y-m[b].y],A=Math.hypot(k[0],k[1]);A=A<1e-4?1e-4:A,k[0]=k[0]/A,k[1]=k[1]/A,h&&e<o&&(A=A-f[b]-f[S]);var K=d*(s[b]+1)*(s[S]+1)/A;h&&e<o&&A<0?K=i*(s[b]+1)*(s[S]+1):h&&e<o&&A===0?K=0:h&&e<o&&A>0&&(K=d*(s[b]+1)*(s[S]+1)/A),a[2*b]-=K*k[0],a[2*S]+=K*k[0],a[2*b+1]-=K*k[1],a[2*S+1]+=K*k[1]}var R=[m[b].x-y[0],m[b].y-y[1]],P=Math.hypot(R[0],R[1]);R[0]=R[0]/P,R[1]=R[1]/P;var N=v*(s[b]+1);a[2*b]-=N*R[0],a[2*b+1]-=N*R[1]}return a},r.prototype.getOptRepGraForces=function(e,o,a){for(var i=this,f=i.nodes,s=i.kg,l=i.center,m=i.prune,h=f.length,d=9e10,v=-9e10,y=9e10,_=-9e10,D=0;D<h;D+=1)m&&a[D]<=1||(o[D].setPos(f[D].x,f[D].y),f[D].x>=v&&(v=f[D].x),f[D].x<=d&&(d=f[D].x),f[D].y>=_&&(_=f[D].y),f[D].y<=y&&(y=f[D].y));for(var b=Math.max(v-d,_-y),S={xmid:(v+d)/2,ymid:(_+y)/2,length:b,massCenter:l,mass:h},k=new p.default(S),A=new u.default(k),D=0;D<h;D+=1)m&&a[D]<=1||o[D].in(k)&&A.insert(o[D]);for(var D=0;D<h;D+=1)if(!(m&&a[D]<=1)){o[D].resetForce(),A.updateForce(o[D]),e[2*D]-=o[D].fx,e[2*D+1]-=o[D].fy;var K=[f[D].x-l[0],f[D].y-l[1]],R=Math.hypot(K[0],K[1]);R=R<1e-4?1e-4:R,K[0]=K[0]/R,K[1]=K[1]/R;var P=s*(a[D]+1);e[2*D]-=P*K[0],e[2*D+1]-=P*K[1]}return e},r.prototype.updatePos=function(e,o,a,i){for(var f=this,s=f.nodes,l=f.ks,m=f.tao,h=f.prune,d=f.ksmax,v=s.length,y=[],_=[],D=0,b=0,S=0;S<v;S+=1)if(!(h&&i[S]<=1)){var k=[e[2*S]-o[2*S],e[2*S+1]-o[2*S+1]],A=Math.hypot(k[0],k[1]),K=[e[2*S]+o[2*S],e[2*S+1]+o[2*S+1]],R=Math.hypot(K[0],K[1]);y[S]=A,_[S]=R/2,D+=(i[S]+1)*y[S],b+=(i[S]+1)*_[S]}var P=a;a=m*b/D,P!==0&&(a=a>1.5*P?1.5*P:a);for(var S=0;S<v;S+=1)if(!(h&&i[S]<=1)&&!((0,M.isNumber)(s[S].fx)&&(0,M.isNumber)(s[S].fy))){var N=l*a/(1+a*Math.sqrt(y[S])),U=Math.hypot(e[2*S],e[2*S+1]);U=U<1e-4?1e-4:U;var B=d/U;N=N>B?B:N;var ue=N*e[2*S],ae=N*e[2*S+1];s[S].x+=ue,s[S].y+=ae}return{nodes:s,sg:a}},r}(x.Base);w.ForceAtlas2Layout=t},14523:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(){function T(C){this.xmid=C.xmid,this.ymid=C.ymid,this.length=C.length,this.massCenter=C.massCenter||[0,0],this.mass=C.mass||1}return T.prototype.getLength=function(){return this.length},T.prototype.contains=function(C,x){var M=this.length/2;return C<=this.xmid+M&&C>=this.xmid-M&&x<=this.ymid+M&&x>=this.ymid-M},T.prototype.NW=function(){var C=this.xmid-this.length/4,x=this.ymid+this.length/4,M=this.length/2,E={xmid:C,ymid:x,length:M},p=new T(E);return p},T.prototype.NE=function(){var C=this.xmid+this.length/4,x=this.ymid+this.length/4,M=this.length/2,E={xmid:C,ymid:x,length:M},p=new T(E);return p},T.prototype.SW=function(){var C=this.xmid-this.length/4,x=this.ymid-this.length/4,M=this.length/2,E={xmid:C,ymid:x,length:M},p=new T(E);return p},T.prototype.SE=function(){var C=this.xmid+this.length/4,x=this.ymid-this.length/4,M=this.length/2,E={xmid:C,ymid:x,length:M},p=new T(E);return p},T}();w.default=G},3008:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=function(){function T(C){this.body=null,this.quad=null,this.NW=null,this.NE=null,this.SW=null,this.SE=null,this.theta=.5,C!=null&&(this.quad=C)}return T.prototype.insert=function(C){if(this.body==null){this.body=C;return}this._isExternal()?(this.quad&&(this.NW=new T(this.quad.NW()),this.NE=new T(this.quad.NE()),this.SW=new T(this.quad.SW()),this.SE=new T(this.quad.SE())),this._putBody(this.body),this._putBody(C),this.body=this.body.add(C)):(this.body=this.body.add(C),this._putBody(C))},T.prototype._putBody=function(C){this.quad&&(C.in(this.quad.NW())&&this.NW?this.NW.insert(C):C.in(this.quad.NE())&&this.NE?this.NE.insert(C):C.in(this.quad.SW())&&this.SW?this.SW.insert(C):C.in(this.quad.SE())&&this.SE&&this.SE.insert(C))},T.prototype._isExternal=function(){return this.NW==null&&this.NE==null&&this.SW==null&&this.SE==null},T.prototype.updateForce=function(C){if(!(this.body==null||C===this.body))if(this._isExternal())C.addForce(this.body);else{var x=this.quad?this.quad.getLength():0,M=this.body.distanceTo(C);x/M<this.theta?C.addForce(this.body):(this.NW&&this.NW.updateForce(C),this.NE&&this.NE.updateForce(C),this.SW&&this.SW.updateForce(C),this.SE&&this.SE.updateForce(C))}},T}();w.default=G},8929:function(Je,w,G){"use strict";var T=this&&this.__createBinding||(Object.create?function(u,t,n,r){r===void 0&&(r=n);var e=Object.getOwnPropertyDescriptor(t,n);(!e||("get"in e?!t.__esModule:e.writable||e.configurable))&&(e={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(u,r,e)}:function(u,t,n,r){r===void 0&&(r=n),u[r]=t[n]}),C=this&&this.__setModuleDefault||(Object.create?function(u,t){Object.defineProperty(u,"default",{enumerable:!0,value:t})}:function(u,t){u.default=t}),x=this&&this.__importStar||function(u){if(u&&u.__esModule)return u;var t={};if(u!=null)for(var n in u)n!=="default"&&Object.prototype.hasOwnProperty.call(u,n)&&T(t,u,n);return C(t,u),t};Object.defineProperty(w,"__esModule",{value:!0});var M=x(G(13746)),E=G(45612);function p(){function u(he){return function(){return he}}var t=function(he){return he.cluster},n=u(1),r=u(-1),e=u(100),o=u(.1),a=[0,0],i=[],f={},s=[],l=100,m=100,h={none:{x:0,y:0}},d=[],v,y="force",_=!0,D=.1;function b(he){if(!_)return b;v.tick(),P();for(var De=0,Pe=i.length,He=void 0,Ze=he*D;De<Pe;++De)He=i[De],He.vx+=(h[t(He)].x-He.x)*Ze,He.vy+=(h[t(He)].y-He.y)*Ze}function S(){i&&k()}function k(){if(!(!i||!i.length)){if(t(i[0])===void 0)throw Error("Couldnt find the grouping attribute for the nodes. Make sure to set it up with forceInABox.groupBy('clusterAttr') before calling .links()");var he=A();v=M.forceSimulation(he.nodes).force("x",M.forceX(l).strength(.1)).force("y",M.forceY(m).strength(.1)).force("collide",M.forceCollide(function(De){return De.r}).iterations(4)).force("charge",M.forceManyBody().strength(r)).force("links",M.forceLink(he.nodes.length?he.links:[]).distance(e).strength(o)),d=v.nodes(),P()}}function A(){var he=[],De=[],Pe={},He=[],Ze={},Ue=[];return Ze=K(i),Ue=R(s),He=Object.keys(Ze),He.forEach(function(tt,pt){var Mt=Ze[tt];he.push({id:tt,size:Mt.count,r:Math.sqrt(Mt.sumforceNodeSize/Math.PI)}),Pe[tt]=pt}),Ue.forEach(function(tt){var pt=(0,E.getEdgeTerminal)(tt,"source"),Mt=(0,E.getEdgeTerminal)(tt,"target"),jt=Pe[pt],Ct=Pe[Mt];jt!==void 0&&Ct!==void 0&&De.push({source:jt,target:Ct,count:tt.count})}),{nodes:he,links:De}}function K(he){var De={};return he.forEach(function(Pe){var He=t(Pe);De[He]||(De[He]={count:0,sumforceNodeSize:0})}),he.forEach(function(Pe){var He=t(Pe),Ze=n(Pe),Ue=De[He];Ue.count=Ue.count+1,Ue.sumforceNodeSize=Ue.sumforceNodeSize+Math.PI*(Ze*Ze)*1.3,De[He]=Ue}),De}function R(he){var De={},Pe=[];he.forEach(function(Ze){var Ue=N(Ze),tt=0;De[Ue]!==void 0&&(tt=De[Ue]),tt+=1,De[Ue]=tt});var He=Object.entries(De);return He.forEach(function(Ze){var Ue=Ze[0],tt=Ze[1],pt=Ue.split("~")[0],Mt=Ue.split("~")[1];pt!==void 0&&Mt!==void 0&&Pe.push({source:pt,target:Mt,count:tt})}),Pe}function P(){return h={none:{x:0,y:0}},d.forEach(function(he){h[he.id]={x:he.x-a[0],y:he.y-a[1]}}),h}function N(he){var De=(0,E.getEdgeTerminal)(he,"source"),Pe=(0,E.getEdgeTerminal)(he,"target"),He=t(f[De]),Ze=t(f[Pe]);return He<=Ze?"".concat(He,"~").concat(Ze):"".concat(Ze,"~").concat(He)}function U(he){f={},he.forEach(function(De){f[De.id]=De})}function B(he){return arguments.length?(y=he,S(),b):y}function ue(he){return arguments.length?typeof he=="string"?(t=function(De){return De[he]},b):(t=he,b):t}function ae(he){return arguments.length?(_=he,b):_}function de(he){return arguments.length?(D=he,b):D}function ve(he){return arguments.length?(l=he,b):l}function we(he){return arguments.length?(m=he,b):m}function Se(he){return arguments.length?(U(he||[]),i=he||[],b):i}function Le(he){return arguments.length?(s=he||[],S(),b):s}function q(he){return arguments.length?(typeof he=="function"?n=he:n=u(+he),S(),b):n}function se(he){return arguments.length?(typeof he=="function"?r=he:r=u(+he),S(),b):r}function ne(he){return arguments.length?(typeof he=="function"?e=he:e=u(+he),S(),b):e}function pe(he){return arguments.length?(typeof he=="function"?o=he:o=u(+he),S(),b):o}function Me(he){return arguments.length?(a=he,b):a}return b.initialize=function(he){i=he,S()},b.template=B,b.groupBy=ue,b.enableGrouping=ae,b.strength=de,b.centerX=ve,b.centerY=we,b.nodes=Se,b.links=Le,b.forceNodeSize=q,b.nodeSize=b.forceNodeSize,b.forceCharge=se,b.forceLinkDistance=ne,b.forceLinkStrength=pe,b.offset=Me,b.getFocis=P,b}w.default=p},57403:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var i=function(f,s){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(l,m){l.__proto__=m}||function(l,m){for(var h in m)Object.prototype.hasOwnProperty.call(m,h)&&(l[h]=m[h])},i(f,s)};return function(f,s){if(typeof s!="function"&&s!==null)throw new TypeError("Class extends value "+String(s)+" is not a constructor or null");i(f,s);function l(){this.constructor=f}f.prototype=s===null?Object.create(s):(l.prototype=s.prototype,new l)}}(),C=this&&this.__createBinding||(Object.create?function(i,f,s,l){l===void 0&&(l=s);var m=Object.getOwnPropertyDescriptor(f,s);(!m||("get"in m?!f.__esModule:m.writable||m.configurable))&&(m={enumerable:!0,get:function(){return f[s]}}),Object.defineProperty(i,l,m)}:function(i,f,s,l){l===void 0&&(l=s),i[l]=f[s]}),x=this&&this.__setModuleDefault||(Object.create?function(i,f){Object.defineProperty(i,"default",{enumerable:!0,value:f})}:function(i,f){i.default=f}),M=this&&this.__importStar||function(i){if(i&&i.__esModule)return i;var f={};if(i!=null)for(var s in i)s!=="default"&&Object.prototype.hasOwnProperty.call(i,s)&&C(f,i,s);return x(f,i),f},E=this&&this.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(w,"__esModule",{value:!0}),w.ForceLayout=void 0;var p=M(G(13746)),u=E(G(8929)),t=G(45612),n=G(2021),r=G(22688),e=function(i){T(f,i);function f(s){var l=i.call(this)||this;return l.center=[0,0],l.nodeStrength=null,l.edgeStrength=null,l.preventOverlap=!1,l.clusterNodeStrength=null,l.clusterEdgeStrength=null,l.clusterEdgeDistance=null,l.clusterNodeSize=null,l.clusterFociStrength=null,l.linkDistance=50,l.alphaDecay=.028,l.alphaMin=.001,l.alpha=.3,l.collideStrength=1,l.workerEnabled=!1,l.tick=function(){},l.onLayoutEnd=function(){},l.ticking=void 0,s&&l.updateCfg(s),l}return f.prototype.getDefaultCfg=function(){return{center:[0,0],nodeStrength:null,edgeStrength:null,preventOverlap:!1,nodeSize:void 0,nodeSpacing:void 0,linkDistance:50,forceSimulation:null,alphaDecay:.028,alphaMin:.001,alpha:.3,collideStrength:1,clustering:!1,clusterNodeStrength:-1,clusterEdgeStrength:.1,clusterEdgeDistance:100,clusterFociStrength:.8,clusterNodeSize:10,tick:function(){},onLayoutEnd:function(){},workerEnabled:!1}},f.prototype.init=function(s){var l=this;l.nodes=s.nodes||[];var m=s.edges||[];l.edges=m.map(function(h){var d={},v=["targetNode","sourceNode","startPoint","endPoint"];return Object.keys(h).forEach(function(y){v.indexOf(y)>-1||(d[y]=h[y])}),d}),l.ticking=!1},f.prototype.execute=function(s){var l=this,m=l.nodes,h=l.edges;if(!l.ticking){var d=l.forceSimulation,v=l.alphaMin,y=l.alphaDecay,_=l.alpha;if(d){if(s){if(l.clustering&&l.clusterForce&&(l.clusterForce.nodes(m),l.clusterForce.links(h)),d.nodes(m),h&&l.edgeForce)l.edgeForce.links(h);else if(h&&!l.edgeForce){var S=p.forceLink().id(function(R){return R.id}).links(h);l.edgeStrength&&S.strength(l.edgeStrength),l.linkDistance&&S.distance(l.linkDistance),l.edgeForce=S,d.force("link",S)}}l.preventOverlap&&l.overlapProcess(d),d.alpha(_).restart(),this.ticking=!0}else try{var D=p.forceManyBody();if(l.nodeStrength&&D.strength(l.nodeStrength),d=p.forceSimulation().nodes(m),l.clustering){var b=(0,u.default)();b.centerX(l.center[0]).centerY(l.center[1]).template("force").strength(l.clusterFociStrength),h&&b.links(h),m&&b.nodes(m),b.forceLinkDistance(l.clusterEdgeDistance).forceLinkStrength(l.clusterEdgeStrength).forceCharge(l.clusterNodeStrength).forceNodeSize(l.clusterNodeSize),l.clusterForce=b,d.force("group",b)}if(d.force("center",p.forceCenter(l.center[0],l.center[1])).force("charge",D).alpha(_).alphaDecay(y).alphaMin(v),l.preventOverlap&&l.overlapProcess(d),h){var S=p.forceLink().id(function(K){return K.id}).links(h);l.edgeStrength&&S.strength(l.edgeStrength),l.linkDistance&&S.distance(l.linkDistance),l.edgeForce=S,d.force("link",S)}if(l.workerEnabled&&!a()&&(l.workerEnabled=!1,console.warn("workerEnabled option is only supported when running in web worker.")),!l.workerEnabled)d.on("tick",function(){l.tick()}).on("end",function(){l.ticking=!1,l.onLayoutEnd&&l.onLayoutEnd()}),l.ticking=!0;else{d.stop();for(var k=o(d),A=1;A<=k;A++)d.tick(),postMessage({nodes:m,currentTick:A,totalTicks:k,type:r.LAYOUT_MESSAGE.TICK},void 0);l.ticking=!1}l.forceSimulation=d,l.ticking=!0}catch(K){l.ticking=!1,console.warn(K)}}},f.prototype.overlapProcess=function(s){var l=this,m=l.nodeSize,h=l.nodeSpacing,d,v,y=l.collideStrength;if((0,t.isNumber)(h)?v=function(){return h}:(0,t.isFunction)(h)?v=h:v=function(){return 0},!m)d=function(S){if(S.size){if((0,t.isArray)(S.size)){var k=S.size[0]>S.size[1]?S.size[0]:S.size[1];return k/2+v(S)}if((0,t.isObject)(S.size)){var k=S.size.width>S.size.height?S.size.width:S.size.height;return k/2+v(S)}return S.size/2+v(S)}return 10+v(S)};else if((0,t.isFunction)(m))d=function(S){var k=m(S);return k+v(S)};else if((0,t.isArray)(m)){var _=m[0]>m[1]?m[0]:m[1],D=_/2;d=function(S){return D+v(S)}}else if((0,t.isNumber)(m)){var b=m/2;d=function(S){return b+v(S)}}else d=function(){return 10};s.force("collisionForce",p.forceCollide(d).strength(y))},f.prototype.updateCfg=function(s){var l=this;l.ticking&&(l.forceSimulation.stop(),l.ticking=!1),l.forceSimulation=null,Object.assign(l,s)},f.prototype.destroy=function(){var s=this;s.ticking&&(s.forceSimulation.stop(),s.ticking=!1),s.nodes=null,s.edges=null,s.destroyed=!0},f}(n.Base);w.ForceLayout=e;function o(i){var f=i.alphaMin(),s=i.alphaTarget(),l=i.alpha(),m=Math.log((f-s)/(l-s))/Math.log(1-i.alphaDecay()),h=Math.ceil(m);return h}function a(){return typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope}},84496:function(Je,w,G){"use strict";var T=this&&this.__createBinding||(Object.create?function(x,M,E,p){p===void 0&&(p=E);var u=Object.getOwnPropertyDescriptor(M,E);(!u||("get"in u?!M.__esModule:u.writable||u.configurable))&&(u={enumerable:!0,get:function(){return M[E]}}),Object.defineProperty(x,p,u)}:function(x,M,E,p){p===void 0&&(p=E),x[p]=M[E]}),C=this&&this.__exportStar||function(x,M){for(var E in x)E!=="default"&&!Object.prototype.hasOwnProperty.call(M,E)&&T(M,x,E)};Object.defineProperty(w,"__esModule",{value:!0}),C(G(57403),w)},11929:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var p=function(u,t){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])},p(u,t)};return function(u,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");p(u,t);function n(){this.constructor=u}u.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.FruchtermanLayout=void 0;var C=G(2021),x=G(45612),M=800,E=function(p){T(u,p);function u(t){var n=p.call(this)||this;return n.maxIteration=1e3,n.workerEnabled=!1,n.gravity=10,n.speed=5,n.clustering=!1,n.clusterGravity=10,n.nodes=[],n.edges=[],n.width=300,n.height=300,n.nodeMap={},n.nodeIdxMap={},n.onLayoutEnd=function(){},n.tick=function(){},n.animate=!0,n.updateCfg(t),n}return u.prototype.getDefaultCfg=function(){return{maxIteration:1e3,gravity:10,speed:1,clustering:!1,clusterGravity:10,animate:!0}},u.prototype.execute=function(){var t=this,n,r,e=this,o=e.nodes;if(e.timeInterval!==void 0&&typeof window!="undefined"&&window.clearInterval(e.timeInterval),!o||o.length===0){(n=e.onLayoutEnd)===null||n===void 0||n.call(e);return}!e.width&&typeof window!="undefined"&&(e.width=window.innerWidth),!e.height&&typeof window!="undefined"&&(e.height=window.innerHeight),e.center||(e.center=[e.width/2,e.height/2]);var a=e.center;if(o.length===1){o[0].x=a[0],o[0].y=a[1],(r=e.onLayoutEnd)===null||r===void 0||r.call(e);return}var i={},f={};return o.forEach(function(s,l){(0,x.isNumber)(s.x)||(s.x=Math.random()*t.width),(0,x.isNumber)(s.y)||(s.y=Math.random()*t.height),i[s.id]=s,f[s.id]=l}),e.nodeMap=i,e.nodeIdxMap=f,e.run()},u.prototype.run=function(){var t,n=this,r=n.nodes;if(r){var e=n.edges,o=n.maxIteration,a=n.workerEnabled,i=n.clustering,f=n.animate,s={};if(i&&r.forEach(function(h){s[h.cluster]===void 0&&(s[h.cluster]={name:h.cluster,cx:0,cy:0,count:0})}),a||!f){for(var l=0;l<o;l++)n.runOneStep(s);(t=n.onLayoutEnd)===null||t===void 0||t.call(n)}else{if(typeof window=="undefined")return;var m=0;this.timeInterval=window.setInterval(function(){var h;n.runOneStep(s),m++,m>=o&&((h=n.onLayoutEnd)===null||h===void 0||h.call(n),window.clearInterval(n.timeInterval))},0)}return{nodes:r,edges:e}}},u.prototype.runOneStep=function(t){var n,r=this,e=r.nodes;if(e){var o=r.edges,a=r.center,i=r.gravity,f=r.speed,s=r.clustering,l=r.height*r.width,m=Math.sqrt(l)/10,h=l/(e.length+1),d=Math.sqrt(h),v=[];if(e.forEach(function(D,b){v[b]={x:0,y:0}}),r.applyCalculate(e,o,v,d,h),s){for(var y in t)t[y].cx=0,t[y].cy=0,t[y].count=0;e.forEach(function(D){var b=t[D.cluster];(0,x.isNumber)(D.x)&&(b.cx+=D.x),(0,x.isNumber)(D.y)&&(b.cy+=D.y),b.count++});for(var y in t)t[y].cx/=t[y].count,t[y].cy/=t[y].count;var _=r.clusterGravity||i;e.forEach(function(D,b){if(!(!(0,x.isNumber)(D.x)||!(0,x.isNumber)(D.y))){var S=t[D.cluster],k=Math.sqrt((D.x-S.cx)*(D.x-S.cx)+(D.y-S.cy)*(D.y-S.cy)),A=d*_;v[b].x-=A*(D.x-S.cx)/k,v[b].y-=A*(D.y-S.cy)/k}})}e.forEach(function(D,b){if(!(!(0,x.isNumber)(D.x)||!(0,x.isNumber)(D.y))){var S=.01*d*i;v[b].x-=S*(D.x-a[0]),v[b].y-=S*(D.y-a[1])}}),e.forEach(function(D,b){if((0,x.isNumber)(D.fx)&&(0,x.isNumber)(D.fy)){D.x=D.fx,D.y=D.fy;return}if(!(!(0,x.isNumber)(D.x)||!(0,x.isNumber)(D.y))){var S=Math.sqrt(v[b].x*v[b].x+v[b].y*v[b].y);if(S>0){var k=Math.min(m*(f/M),S);D.x+=v[b].x/S*k,D.y+=v[b].y/S*k}}}),(n=r.tick)===null||n===void 0||n.call(r)}},u.prototype.applyCalculate=function(t,n,r,e,o){var a=this;a.calRepulsive(t,r,o),n&&a.calAttractive(n,r,e)},u.prototype.calRepulsive=function(t,n,r){t.forEach(function(e,o){n[o]={x:0,y:0},t.forEach(function(a,i){if(o!==i&&!(!(0,x.isNumber)(e.x)||!(0,x.isNumber)(a.x)||!(0,x.isNumber)(e.y)||!(0,x.isNumber)(a.y))){var f=e.x-a.x,s=e.y-a.y,l=f*f+s*s;if(l===0){l=1;var m=o>i?1:-1;f=.01*m,s=.01*m}var h=r/l;n[o].x+=f*h,n[o].y+=s*h}})})},u.prototype.calAttractive=function(t,n,r){var e=this;t.forEach(function(o){var a=(0,x.getEdgeTerminal)(o,"source"),i=(0,x.getEdgeTerminal)(o,"target");if(!(!a||!i)){var f=e.nodeIdxMap[a],s=e.nodeIdxMap[i];if(f!==s){var l=e.nodeMap[a],m=e.nodeMap[i];if(!(!(0,x.isNumber)(m.x)||!(0,x.isNumber)(l.x)||!(0,x.isNumber)(m.y)||!(0,x.isNumber)(l.y))){var h=m.x-l.x,d=m.y-l.y,v=Math.sqrt(h*h+d*d),y=v*v/r;n[s].x-=h/v*y,n[s].y-=d/v*y,n[f].x+=h/v*y,n[f].y+=d/v*y}}}})},u.prototype.stop=function(){this.timeInterval&&typeof window!="undefined"&&window.clearInterval(this.timeInterval)},u.prototype.destroy=function(){var t=this;t.stop(),t.tick=null,t.nodes=null,t.edges=null,t.destroyed=!0},u.prototype.getType=function(){return"fruchterman"},u}(C.Base);w.FruchtermanLayout=E},15091:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var p=function(u,t){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])},p(u,t)};return function(u,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");p(u,t);function n(){this.constructor=u}u.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.GForceLayout=void 0;var C=G(2021),x=G(45612),M=function(p,u){var t;return p?(0,x.isNumber)(p)?t=function(n){return p}:t=p:t=function(n){return u||1},t},E=function(p){T(u,p);function u(t){var n=p.call(this)||this;return n.maxIteration=500,n.workerEnabled=!1,n.edgeStrength=200,n.nodeStrength=1e3,n.coulombDisScale=.005,n.damping=.9,n.maxSpeed=1e3,n.minMovement=.5,n.interval=.02,n.factor=1,n.linkDistance=1,n.gravity=10,n.preventOverlap=!0,n.collideStrength=1,n.tick=function(){},n.nodes=[],n.edges=[],n.width=300,n.height=300,n.nodeMap={},n.nodeIdxMap={},n.animate=!0,n.updateCfg(t),n}return u.prototype.getDefaultCfg=function(){return{maxIteration:500,gravity:10,enableTick:!0,animate:!0}},u.prototype.execute=function(){var t,n,r=this,e=r.nodes;if(r.timeInterval!==void 0&&typeof window!="undefined"&&window.clearInterval(r.timeInterval),!e||e.length===0){(t=r.onLayoutEnd)===null||t===void 0||t.call(r);return}!r.width&&typeof window!="undefined"&&(r.width=window.innerWidth),!r.height&&typeof window!="undefined"&&(r.height=window.innerHeight),r.center||(r.center=[r.width/2,r.height/2]);var o=r.center;if(e.length===1){e[0].x=o[0],e[0].y=o[1],(n=r.onLayoutEnd)===null||n===void 0||n.call(r);return}var a={},i={};e.forEach(function(d,v){(0,x.isNumber)(d.x)||(d.x=Math.random()*r.width),(0,x.isNumber)(d.y)||(d.y=Math.random()*r.height),a[d.id]=d,i[d.id]=v}),r.nodeMap=a,r.nodeIdxMap=i,r.linkDistance=M(r.linkDistance,1),r.nodeStrength=M(r.nodeStrength,1),r.edgeStrength=M(r.edgeStrength,1);var f=r.nodeSize,s;if(r.preventOverlap){var l=r.nodeSpacing,m;(0,x.isNumber)(l)?m=function(){return l}:(0,x.isFunction)(l)?m=l:m=function(){return 0},f?(0,x.isArray)(f)?s=function(d){return Math.max(f[0],f[1])+m(d)}:s=function(d){return f+m(d)}:s=function(d){return d.size?(0,x.isArray)(d.size)?Math.max(d.size[0],d.size[1])+m(d):(0,x.isObject)(d.size)?Math.max(d.size.width,d.size.height)+m(d):d.size+m(d):10+m(d)}}r.nodeSize=s;var h=r.edges;r.degrees=(0,x.getDegree)(e.length,r.nodeIdxMap,h),r.getMass||(r.getMass=function(d){var v=d.mass||r.degrees[r.nodeIdxMap[d.id]].all||1;return v}),r.run()},u.prototype.run=function(){var t,n=this,r=n.maxIteration,e=n.nodes,o=n.workerEnabled,a=n.minMovement,i=n.animate;if(e)if(o||!i){for(var f=0;f<r;f++){var s=n.runOneStep(f);if(n.reachMoveThreshold(e,s,a))break}(t=n.onLayoutEnd)===null||t===void 0||t.call(n)}else{if(typeof window=="undefined")return;var l=0;this.timeInterval=window.setInterval(function(){var m,h;if(e){var d=n.runOneStep(l)||[];n.reachMoveThreshold(e,d,a)&&((m=n.onLayoutEnd)===null||m===void 0||m.call(n),window.clearInterval(n.timeInterval)),l++,l>=r&&((h=n.onLayoutEnd)===null||h===void 0||h.call(n),window.clearInterval(n.timeInterval))}},0)}},u.prototype.reachMoveThreshold=function(t,n,r){var e=0;return t.forEach(function(o,a){var i=o.x-n[a].x,f=o.y-n[a].y;e+=Math.sqrt(i*i+f*f)}),e/=t.length,e<r},u.prototype.runOneStep=function(t){var n,r=this,e=r.nodes,o=r.edges,a=[],i=[];if(e){e.forEach(function(l,m){a[2*m]=0,a[2*m+1]=0,i[2*m]=0,i[2*m+1]=0}),r.calRepulsive(a,e),o&&r.calAttractive(a,o),r.calGravity(a,e);var f=Math.max(.02,r.interval-t*.002);r.updateVelocity(a,i,f,e);var s=[];return e.forEach(function(l){s.push({x:l.x,y:l.y})}),r.updatePosition(i,f,e),(n=r.tick)===null||n===void 0||n.call(r),s}},u.prototype.calRepulsive=function(t,n){var r=this,e=r.getMass,o=r.factor,a=r.coulombDisScale,i=r.preventOverlap,f=r.collideStrength,s=f===void 0?1:f,l=r.nodeStrength,m=r.nodeSize;n.forEach(function(h,d){var v=e?e(h):1;n.forEach(function(y,_){if(!(d>=_)){var D=h.x-y.x,b=h.y-y.y;D===0&&b===0&&(D=Math.random()*.01,b=Math.random()*.01);var S=D*D+b*b,k=Math.sqrt(S),A=(k+.1)*a,K=D/k,R=b/k,P=(l(h)+l(y))*.5*o/(A*A),N=e?e(y):1;if(t[2*d]+=K*P,t[2*d+1]+=R*P,t[2*_]-=K*P,t[2*_+1]-=R*P,i&&(m(h)+m(y))/2>k){var U=s*(l(h)+l(y))*.5/S;t[2*d]+=K*U/v,t[2*d+1]+=R*U/v,t[2*_]-=K*U/N,t[2*_+1]-=R*U/N}}})})},u.prototype.calAttractive=function(t,n){var r=this,e=r.nodeMap,o=r.nodeIdxMap,a=r.linkDistance,i=r.edgeStrength,f=r.nodeSize,s=r.getMass;n.forEach(function(l,m){var h=(0,x.getEdgeTerminal)(l,"source"),d=(0,x.getEdgeTerminal)(l,"target"),v=e[h],y=e[d],_=y.x-v.x,D=y.y-v.y;_===0&&D===0&&(_=Math.random()*.01,D=Math.random()*.01);var b=Math.sqrt(_*_+D*D),S=_/b,k=D/b,A=a(l,v,y)||1+(f(v)+f(y)||0)/2,K=A-b,R=K*i(l),P=o[h],N=o[d],U=s?s(v):1,B=s?s(y):1;t[2*P]-=S*R/U,t[2*P+1]-=k*R/U,t[2*N]+=S*R/B,t[2*N+1]+=k*R/B})},u.prototype.calGravity=function(t,n){for(var r=this,e=r.center,o=r.gravity,a=r.degrees,i=n.length,f=0;f<i;f++){var s=n[f],l=s.x-e[0],m=s.y-e[1],h=o;if(r.getCenter){var d=r.getCenter(s,a[f].all);d&&(0,x.isNumber)(d[0])&&(0,x.isNumber)(d[1])&&(0,x.isNumber)(d[2])&&(l=s.x-d[0],m=s.y-d[1],h=d[2])}h&&(t[2*f]-=h*l,t[2*f+1]-=h*m)}},u.prototype.updateVelocity=function(t,n,r,e){var o=this,a=r*o.damping;e.forEach(function(i,f){var s=t[2*f]*a||.01,l=t[2*f+1]*a||.01,m=Math.sqrt(s*s+l*l);if(m>o.maxSpeed){var h=o.maxSpeed/m;s=h*s,l=h*l}n[2*f]=s,n[2*f+1]=l})},u.prototype.updatePosition=function(t,n,r){r.forEach(function(e,o){if((0,x.isNumber)(e.fx)&&(0,x.isNumber)(e.fy)){e.x=e.fx,e.y=e.fy;return}var a=t[2*o]*n,i=t[2*o+1]*n;e.x+=a,e.y+=i})},u.prototype.stop=function(){this.timeInterval&&typeof window!="undefined"&&window.clearInterval(this.timeInterval)},u.prototype.destroy=function(){var t=this;t.stop(),t.tick=null,t.nodes=null,t.edges=null,t.destroyed=!0},u.prototype.getType=function(){return"gForce"},u}(C.Base);w.GForceLayout=E},22073:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var e=function(o,a){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,f){i.__proto__=f}||function(i,f){for(var s in f)Object.prototype.hasOwnProperty.call(f,s)&&(i[s]=f[s])},e(o,a)};return function(o,a){if(typeof a!="function"&&a!==null)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");e(o,a);function i(){this.constructor=o}o.prototype=a===null?Object.create(a):(i.prototype=a.prototype,new i)}}(),C=this&&this.__awaiter||function(e,o,a,i){function f(s){return s instanceof a?s:new a(function(l){l(s)})}return new(a||(a=Promise))(function(s,l){function m(v){try{d(i.next(v))}catch(y){l(y)}}function h(v){try{d(i.throw(v))}catch(y){l(y)}}function d(v){v.done?s(v.value):f(v.value).then(m,h)}d((i=i.apply(e,o||[])).next())})},x=this&&this.__generator||function(e,o){var a={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},i,f,s,l;return l={next:m(0),throw:m(1),return:m(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function m(d){return function(v){return h([d,v])}}function h(d){if(i)throw new TypeError("Generator is already executing.");for(;l&&(l=0,d[0]&&(a=0)),a;)try{if(i=1,f&&(s=d[0]&2?f.return:d[0]?f.throw||((s=f.return)&&s.call(f),0):f.next)&&!(s=s.call(f,d[1])).done)return s;switch(f=0,s&&(d=[d[0]&2,s.value]),d[0]){case 0:case 1:s=d;break;case 4:return a.label++,{value:d[1],done:!1};case 5:a.label++,f=d[1],d=[0];continue;case 7:d=a.ops.pop(),a.trys.pop();continue;default:if(s=a.trys,!(s=s.length>0&&s[s.length-1])&&(d[0]===6||d[0]===2)){a=0;continue}if(d[0]===3&&(!s||d[1]>s[0]&&d[1]<s[3])){a.label=d[1];break}if(d[0]===6&&a.label<s[1]){a.label=s[1],s=d;break}if(s&&a.label<s[2]){a.label=s[2],a.ops.push(d);break}s[2]&&a.ops.pop(),a.trys.pop();continue}d=o.call(e,a)}catch(v){d=[6,v],f=0}finally{i=s=0}if(d[0]&5)throw d[1];return{value:d[0]?d[1]:void 0,done:!0}}};Object.defineProperty(w,"__esModule",{value:!0}),w.FruchtermanGPULayout=void 0;var M=G(2021),E=G(45612),p=G(78631),u=G(71379),t=G(96949),n=G(22688),r=function(e){T(o,e);function o(a){var i=e.call(this)||this;return i.maxIteration=1e3,i.gravity=10,i.speed=1,i.clustering=!1,i.clusterField="cluster",i.clusterGravity=10,i.workerEnabled=!1,i.nodes=[],i.edges=[],i.width=300,i.height=300,i.nodeMap={},i.nodeIdxMap={},i.updateCfg(a),i}return o.prototype.getDefaultCfg=function(){return{maxIteration:1e3,gravity:10,speed:1,clustering:!1,clusterGravity:10}},o.prototype.execute=function(){return C(this,void 0,void 0,function(){var a,i,f,s,l,m=this;return x(this,function(h){switch(h.label){case 0:return a=this,i=a.nodes,!i||i.length===0?(a.onLayoutEnd&&a.onLayoutEnd(),[2]):(!a.width&&typeof window!="undefined"&&(a.width=window.innerWidth),!a.height&&typeof window!="undefined"&&(a.height=window.innerHeight),a.center||(a.center=[a.width/2,a.height/2]),f=a.center,i.length===1?(i[0].x=f[0],i[0].y=f[1],a.onLayoutEnd&&a.onLayoutEnd(),[2]):(s={},l={},i.forEach(function(d,v){(0,E.isNumber)(d.x)||(d.x=Math.random()*m.width),(0,E.isNumber)(d.y)||(d.y=Math.random()*m.height),s[d.id]=d,l[d.id]=v}),a.nodeMap=s,a.nodeIdxMap=l,[4,a.run()]));case 1:return h.sent(),[2]}})})},o.prototype.executeWithWorker=function(a,i){return C(this,void 0,void 0,function(){var f,s,l,m,h,d=this;return x(this,function(v){switch(v.label){case 0:return f=this,s=f.nodes,l=f.center,!s||s.length===0?[2]:s.length===1?(s[0].x=l[0],s[0].y=l[1],[2]):(m={},h={},s.forEach(function(y,_){(0,E.isNumber)(y.x)||(y.x=Math.random()*d.width),(0,E.isNumber)(y.y)||(y.y=Math.random()*d.height),m[y.id]=y,h[y.id]=_}),f.nodeMap=m,f.nodeIdxMap=h,[4,f.run(a,i)]);case 1:return v.sent(),[2]}})})},o.prototype.run=function(a,i){return C(this,void 0,void 0,function(){var f,s,l,m,h,d,v,y,_,D,b,S,k,A,K,R,P,N,U,B,ue,ae,de,ve,we,Se,Le=this;return x(this,function(q){switch(q.label){case 0:for(f=this,s=f.nodes,l=f.edges,m=f.maxIteration,h=f.center,d=f.height*f.width,v=Math.sqrt(d)/10,y=d/(s.length+1),_=Math.sqrt(y),D=f.speed,b=f.clustering,S=(0,u.attributesToTextureData)([f.clusterField],s),k=S.array,A=S.count,s.forEach(function(se,ne){var pe=0,Me=0;(0,E.isNumber)(se.fx)&&(0,E.isNumber)(se.fy)&&(pe=se.fx||.001,Me=se.fy||.001),k[4*ne+1]=pe,k[4*ne+2]=Me}),K=s.length,R=(0,u.buildTextureData)(s,l),P=R.maxEdgePerVetex,N=R.array,U=f.workerEnabled,U?B=p.World.create({canvas:a,engineOptions:{supportCompute:!0}}):B=p.World.create({engineOptions:{supportCompute:!0}}),ue=f.onLayoutEnd,ae=[],de=0;de<A;de++)ae.push(0,0,0,0);return ve=B.createKernel(t.fruchtermanBundle).setDispatch([K,1,1]).setBinding({u_Data:N,u_K:_,u_K2:y,u_Gravity:f.gravity,u_ClusterGravity:f.clusterGravity||f.gravity||1,u_Speed:D,u_MaxDisplace:v,u_Clustering:b?1:0,u_Center:h,u_AttributeArray:k,u_ClusterCenters:ae,MAX_EDGE_PER_VERTEX:P,VERTEX_COUNT:K}),b&&(we=B.createKernel(t.clusterBundle).setDispatch([A,1,1]).setBinding({u_Data:N,u_NodeAttributes:k,u_ClusterCenters:ae,VERTEX_COUNT:K,CLUSTER_COUNT:A})),Se=function(){return C(Le,void 0,void 0,function(){var se,ne;return x(this,function(pe){switch(pe.label){case 0:se=0,pe.label=1;case 1:return se<m?[4,ve.execute()]:[3,6];case 2:return pe.sent(),b?(we.setBinding({u_Data:ve}),[4,we.execute()]):[3,4];case 3:pe.sent(),ve.setBinding({u_ClusterCenters:we}),pe.label=4;case 4:ve.setBinding({u_MaxDisplace:v*=.99}),pe.label=5;case 5:return se++,[3,1];case 6:return[4,ve.getOutput()];case 7:return ne=pe.sent(),a?i.postMessage({type:n.LAYOUT_MESSAGE.GPUEND,vertexEdgeData:ne}):s.forEach(function(Me,he){var De=ne[4*he],Pe=ne[4*he+1];Me.x=De,Me.y=Pe}),ue&&ue(),[2]}})})},[4,Se()];case 1:return q.sent(),[2]}})})},o.prototype.getType=function(){return"fruchterman-gpu"},o}(M.Base);w.FruchtermanGPULayout=r},96949:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.clusterBundle=w.clusterCode=w.fruchtermanBundle=w.fruchtermanCode=void 0,w.fruchtermanCode=`
import { globalInvocationID } from 'g-webgpu';
const MAX_EDGE_PER_VERTEX;
const VERTEX_COUNT;
@numthreads(1, 1, 1)
class Fruchterman {
  @in @out
  u_Data: vec4[];
  @in
  u_K: float;
  @in
  u_K2: float;
  
  @in
  u_Center: vec2;
  @in
  u_Gravity: float;
  @in
  u_ClusterGravity: float;
  @in
  u_Speed: float;
  @in
  u_MaxDisplace: float;
  @in
  u_Clustering: float;
  @in
  u_AttributeArray: vec4[];
  @in
  u_ClusterCenters: vec4[];
  calcRepulsive(i: int, currentNode: vec4): vec2 {
    let dx = 0, dy = 0;
    for (let j = 0; j < VERTEX_COUNT; j++) {
      if (i != j) {
        const nextNode = this.u_Data[j];
        const xDist = currentNode[0] - nextNode[0];
        const yDist = currentNode[1] - nextNode[1];
        const dist = (xDist * xDist + yDist * yDist) + 0.01;
        let param = this.u_K2 / dist;
        
        if (dist > 0.0) {
          dx += param * xDist;
          dy += param * yDist;
          if (xDist == 0 && yDist == 0) {
            const sign = i < j ? 1 : -1;
            dx += param * sign;
            dy += param * sign;
          }
        }
      }
    }
    return [dx, dy];
  }
  calcGravity(currentNode: vec4, nodeAttributes: vec4): vec2 { // 
    let dx = 0, dy = 0;
    const vx = currentNode[0] - this.u_Center[0];
    const vy = currentNode[1] - this.u_Center[1];
    const gf = 0.01 * this.u_K * this.u_Gravity;
    dx = gf * vx;
    dy = gf * vy;
    if (this.u_Clustering == 1) {
      const clusterIdx = int(nodeAttributes[0]);
      const center = this.u_ClusterCenters[clusterIdx];
      const cvx = currentNode[0] - center[0];
      const cvy = currentNode[1] - center[1];
      const dist = sqrt(cvx * cvx + cvy * cvy) + 0.01;
      const parma = this.u_K * this.u_ClusterGravity / dist;
      dx += parma * cvx;
      dy += parma * cvy;
    }
    return [dx, dy];
  }
  calcAttractive(i: int, currentNode: vec4): vec2 {
    let dx = 0, dy = 0;
    const arr_offset = int(floor(currentNode[2] + 0.5));
    const length = int(floor(currentNode[3] + 0.5));
    const node_buffer: vec4;
    for (let p = 0; p < MAX_EDGE_PER_VERTEX; p++) {
      if (p >= length) break;
      const arr_idx = arr_offset + p;
      // when arr_idx % 4 == 0 update currentNodedx_buffer
      const buf_offset = arr_idx - arr_idx / 4 * 4;
      if (p == 0 || buf_offset == 0) {
        node_buffer = this.u_Data[int(arr_idx / 4)];
      }
      const float_j = buf_offset == 0 ? node_buffer[0] :
                      buf_offset == 1 ? node_buffer[1] :
                      buf_offset == 2 ? node_buffer[2] :
                                        node_buffer[3];
      const nextNode = this.u_Data[int(float_j)];
      const xDist = currentNode[0] - nextNode[0];
      const yDist = currentNode[1] - nextNode[1];
      const dist = sqrt(xDist * xDist + yDist * yDist) + 0.01;
      let attractiveF = dist / this.u_K;
    
      if (dist > 0.0) {
        dx -= xDist * attractiveF;
        dy -= yDist * attractiveF;
        if (xDist == 0 && yDist == 0) {
          const sign = i < int(float_j) ? 1 : -1;
          dx -= sign * attractiveF;
          dy -= sign * attractiveF;
        }
      }
    }
    return [dx, dy];
  }
  @main
  compute() {
    const i = globalInvocationID.x;
    const currentNode = this.u_Data[i];
    let dx = 0, dy = 0;
    if (i >= VERTEX_COUNT) {
      this.u_Data[i] = currentNode;
      return;
    }

    // [gravity, fx, fy, 0]
    const nodeAttributes = this.u_AttributeArray[i];

    if (nodeAttributes[1] != 0 && nodeAttributes[2] != 0) {
      // the node is fixed
      this.u_Data[i] = [
        nodeAttributes[1],
        nodeAttributes[2],
        currentNode[2],
        currentNode[3]
      ];
      return;
    }

    // repulsive
    const repulsive = this.calcRepulsive(i, currentNode);
    dx += repulsive[0];
    dy += repulsive[1];
    // attractive
    const attractive = this.calcAttractive(i, currentNode);
    dx += attractive[0];
    dy += attractive[1];
    // gravity
    const gravity = this.calcGravity(currentNode, nodeAttributes);
    dx -= gravity[0];
    dy -= gravity[1];
    // speed
    dx *= this.u_Speed;
    dy *= this.u_Speed;

    // move
    const distLength = sqrt(dx * dx + dy * dy);
    if (distLength > 0.0) {
      const limitedDist = min(this.u_MaxDisplace * this.u_Speed, distLength);
      this.u_Data[i] = [
        currentNode[0] + dx / distLength * limitedDist,
        currentNode[1] + dy / distLength * limitedDist,
        currentNode[2],
        currentNode[3]
      ];
    }
  }
}
`,w.fruchtermanBundle='{"shaders":{"WGSL":"","GLSL450":"","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define MAX_EDGE_PER_VERTEX __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform float u_K;\\nuniform float u_K2;\\nuniform vec2 u_Center;\\nuniform float u_Gravity;\\nuniform float u_ClusterGravity;\\nuniform float u_Speed;\\nuniform float u_MaxDisplace;\\nuniform float u_Clustering;\\nuniform sampler2D u_AttributeArray;\\nuniform vec2 u_AttributeArraySize;\\nvec4 getDatau_AttributeArray(vec2 address2D) {\\n  return vec4(texture2D(u_AttributeArray, address2D).rgba);\\n}\\nvec4 getDatau_AttributeArray(float address1D) {\\n  return getDatau_AttributeArray(addrTranslation_1Dto2D(address1D, u_AttributeArraySize));\\n}\\nvec4 getDatau_AttributeArray(int address1D) {\\n  return getDatau_AttributeArray(float(address1D));\\n}\\nuniform sampler2D u_ClusterCenters;\\nuniform vec2 u_ClusterCentersSize;\\nvec4 getDatau_ClusterCenters(vec2 address2D) {\\n  return vec4(texture2D(u_ClusterCenters, address2D).rgba);\\n}\\nvec4 getDatau_ClusterCenters(float address1D) {\\n  return getDatau_ClusterCenters(addrTranslation_1Dto2D(address1D, u_ClusterCentersSize));\\n}\\nvec4 getDatau_ClusterCenters(int address1D) {\\n  return getDatau_ClusterCenters(float(address1D));\\n}\\nvec2 calcRepulsive(int i, vec4 currentNode) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {if (i != j) {vec4 nextNode = getDatau_Data(j);\\nfloat xDist = currentNode.x - nextNode.x;\\nfloat yDist = currentNode.y - nextNode.y;\\nfloat dist = ((xDist * xDist) + (yDist * yDist)) + 0.01;\\nfloat param = u_K2 / dist;\\nif (dist > 0.0) {dx += param * xDist;\\ndy += param * yDist;\\nif ((xDist == 0.0) && (yDist == 0.0)) {float sign = (i < j) ? (1.0) : (-1.0);\\ndx += param * sign;\\ndy += param * sign;}}}}\\nreturn vec2(dx, dy);}\\nvec2 calcGravity(vec4 currentNode, vec4 nodeAttributes) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nfloat vx = currentNode.x - u_Center.x;\\nfloat vy = currentNode.y - u_Center.y;\\nfloat gf = (0.01 * u_K) * u_Gravity;\\ndx = gf * vx;\\ndy = gf * vy;\\nif (u_Clustering == 1.0) {int clusterIdx = int(nodeAttributes.x);\\nvec4 center = getDatau_ClusterCenters(clusterIdx);\\nfloat cvx = currentNode.x - center.x;\\nfloat cvy = currentNode.y - center.y;\\nfloat dist = sqrt((cvx * cvx) + (cvy * cvy)) + 0.01;\\nfloat parma = (u_K * u_ClusterGravity) / dist;\\ndx += parma * cvx;\\ndy += parma * cvy;}\\nreturn vec2(dx, dy);}\\nvec2 calcAttractive(int i, vec4 currentNode) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nint arr_offset = int(floor(currentNode.z + 0.5));\\nint length = int(floor(currentNode.w + 0.5));\\nvec4 node_buffer;\\nfor (int p = 0; p < MAX_EDGE_PER_VERTEX; p++) {if (p >= length) {break;}\\nint arr_idx = arr_offset + int(p);\\nint buf_offset = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = getDatau_Data(int(arr_idx / 4));}\\nfloat float_j = (buf_offset == 0) ? (node_buffer.x) : ((buf_offset == 1) ? (node_buffer.y) : ((buf_offset == 2) ? (node_buffer.z) : (node_buffer.w)));\\nvec4 nextNode = getDatau_Data(int(float_j));\\nfloat xDist = currentNode.x - nextNode.x;\\nfloat yDist = currentNode.y - nextNode.y;\\nfloat dist = sqrt((xDist * xDist) + (yDist * yDist)) + 0.01;\\nfloat attractiveF = dist / u_K;\\nif (dist > 0.0) {dx -= xDist * attractiveF;\\ndy -= yDist * attractiveF;\\nif ((xDist == 0.0) && (yDist == 0.0)) {float sign = (i < int(float_j)) ? (1.0) : (-1.0);\\ndx -= sign * attractiveF;\\ndy -= sign * attractiveF;}}}\\nreturn vec2(dx, dy);}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint i = globalInvocationID.x;\\nvec4 currentNode = getDatau_Data(i);\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nif (i >= VERTEX_COUNT) {gl_FragColor = vec4(currentNode);\\nreturn ;}\\nvec4 nodeAttributes = getDatau_AttributeArray(i);\\nif ((nodeAttributes.y != 0.0) && (nodeAttributes.z != 0.0)) {gl_FragColor = vec4(vec4(nodeAttributes.y, nodeAttributes.z, currentNode.z, currentNode.w));\\nreturn ;}\\nvec2 repulsive = calcRepulsive(i, currentNode);\\ndx += repulsive.x;\\ndy += repulsive.y;\\nvec2 attractive = calcAttractive(i, currentNode);\\ndx += attractive.x;\\ndy += attractive.y;\\nvec2 gravity = calcGravity(currentNode, nodeAttributes);\\ndx -= gravity.x;\\ndy -= gravity.y;\\ndx *= u_Speed;\\ndy *= u_Speed;\\nfloat distLength = sqrt((dx * dx) + (dy * dy));\\nif (distLength > 0.0) {float limitedDist = min(u_MaxDisplace * u_Speed, distLength);\\ngl_FragColor = vec4(vec4(currentNode.x + ((dx / distLength) * limitedDist), currentNode.y + ((dy / distLength) * limitedDist), currentNode.z, currentNode.w));}if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"MAX_EDGE_PER_VERTEX","type":"Float","runtime":true},{"name":"VERTEX_COUNT","type":"Float","runtime":true}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]},{"name":"u_K","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_K2","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Center","type":"vec2<f32>","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Gravity","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_ClusterGravity","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Speed","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_MaxDisplace","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Clustering","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_AttributeArray","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_ClusterCenters","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_Data","size":[1,1],"length":1},"needPingpong":true}}',w.clusterCode=`
import { globalInvocationID } from 'g-webgpu';
const VERTEX_COUNT;
const CLUSTER_COUNT;
@numthreads(1, 1, 1)
class CalcCenter {
  @in
  u_Data: vec4[];
  @in
  u_NodeAttributes: vec4[]; // [[clusterIdx, 0, 0, 0], ...]
  @in @out
  u_ClusterCenters: vec4[]; // [[cx, cy, nodeCount, clusterIdx], ...]
  @main
  compute() {
    const i = globalInvocationID.x;
    const center = this.u_ClusterCenters[i];
    let sumx = 0;
    let sumy = 0;
    let count = 0;
    for (let j = 0; j < VERTEX_COUNT; j++) {
      const attributes = this.u_NodeAttributes[j];
      const clusterIdx = int(attributes[0]);
      const vertex = this.u_Data[j];
      if (clusterIdx == i) {
        sumx += vertex.x;
        sumy += vertex.y;
        count += 1;
      }
    }
    this.u_ClusterCenters[i] = [
      sumx / count,
      sumy / count,
      count,
      i
    ];
  }
}
`,w.clusterBundle='{"shaders":{"WGSL":"","GLSL450":"","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n#define CLUSTER_COUNT __DefineValuePlaceholder__CLUSTER_COUNT\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform sampler2D u_NodeAttributes;\\nuniform vec2 u_NodeAttributesSize;\\nvec4 getDatau_NodeAttributes(vec2 address2D) {\\n  return vec4(texture2D(u_NodeAttributes, address2D).rgba);\\n}\\nvec4 getDatau_NodeAttributes(float address1D) {\\n  return getDatau_NodeAttributes(addrTranslation_1Dto2D(address1D, u_NodeAttributesSize));\\n}\\nvec4 getDatau_NodeAttributes(int address1D) {\\n  return getDatau_NodeAttributes(float(address1D));\\n}\\nuniform sampler2D u_ClusterCenters;\\nuniform vec2 u_ClusterCentersSize;\\nvec4 getDatau_ClusterCenters(vec2 address2D) {\\n  return vec4(texture2D(u_ClusterCenters, address2D).rgba);\\n}\\nvec4 getDatau_ClusterCenters(float address1D) {\\n  return getDatau_ClusterCenters(addrTranslation_1Dto2D(address1D, u_ClusterCentersSize));\\n}\\nvec4 getDatau_ClusterCenters(int address1D) {\\n  return getDatau_ClusterCenters(float(address1D));\\n}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint i = globalInvocationID.x;\\nvec4 center = getDatau_ClusterCenters(i);\\nfloat sumx = 0.0;\\nfloat sumy = 0.0;\\nfloat count = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {vec4 attributes = getDatau_NodeAttributes(j);\\nint clusterIdx = int(attributes.x);\\nvec4 vertex = getDatau_Data(j);\\nif (clusterIdx == i) {sumx += vertex.x;\\nsumy += vertex.y;\\ncount += 1.0;}}\\ngl_FragColor = vec4(vec4(sumx / count, sumy / count, count, i));if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"VERTEX_COUNT","type":"Float","runtime":true},{"name":"CLUSTER_COUNT","type":"Float","runtime":true}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_NodeAttributes","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_ClusterCenters","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_ClusterCenters","size":[1,1],"length":1},"needPingpong":true}}'},46230:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var o=function(a,i){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,s){f.__proto__=s}||function(f,s){for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(f[l]=s[l])},o(a,i)};return function(a,i){if(typeof i!="function"&&i!==null)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");o(a,i);function f(){this.constructor=a}a.prototype=i===null?Object.create(i):(f.prototype=i.prototype,new f)}}(),C=this&&this.__awaiter||function(o,a,i,f){function s(l){return l instanceof i?l:new i(function(m){m(l)})}return new(i||(i=Promise))(function(l,m){function h(y){try{v(f.next(y))}catch(_){m(_)}}function d(y){try{v(f.throw(y))}catch(_){m(_)}}function v(y){y.done?l(y.value):s(y.value).then(h,d)}v((f=f.apply(o,a||[])).next())})},x=this&&this.__generator||function(o,a){var i={label:0,sent:function(){if(l[0]&1)throw l[1];return l[1]},trys:[],ops:[]},f,s,l,m;return m={next:h(0),throw:h(1),return:h(2)},typeof Symbol=="function"&&(m[Symbol.iterator]=function(){return this}),m;function h(v){return function(y){return d([v,y])}}function d(v){if(f)throw new TypeError("Generator is already executing.");for(;m&&(m=0,v[0]&&(i=0)),i;)try{if(f=1,s&&(l=v[0]&2?s.return:v[0]?s.throw||((l=s.return)&&l.call(s),0):s.next)&&!(l=l.call(s,v[1])).done)return l;switch(s=0,l&&(v=[v[0]&2,l.value]),v[0]){case 0:case 1:l=v;break;case 4:return i.label++,{value:v[1],done:!1};case 5:i.label++,s=v[1],v=[0];continue;case 7:v=i.ops.pop(),i.trys.pop();continue;default:if(l=i.trys,!(l=l.length>0&&l[l.length-1])&&(v[0]===6||v[0]===2)){i=0;continue}if(v[0]===3&&(!l||v[1]>l[0]&&v[1]<l[3])){i.label=v[1];break}if(v[0]===6&&i.label<l[1]){i.label=l[1],l=v;break}if(l&&i.label<l[2]){i.label=l[2],i.ops.push(v);break}l[2]&&i.ops.pop(),i.trys.pop();continue}v=a.call(o,i)}catch(y){v=[6,y],s=0}finally{f=l=0}if(v[0]&5)throw v[1];return{value:v[0]?v[1]:void 0,done:!0}}};Object.defineProperty(w,"__esModule",{value:!0}),w.GForceGPULayout=void 0;var M=G(2021),E=G(45612),p=G(78631),u=G(71379),t=G(79039),n=G(4156),r=G(22688),e=function(o){T(a,o);function a(i){var f=o.call(this)||this;return f.maxIteration=1e3,f.edgeStrength=200,f.nodeStrength=1e3,f.coulombDisScale=.005,f.damping=.9,f.maxSpeed=1e3,f.minMovement=.5,f.interval=.02,f.factor=1,f.linkDistance=1,f.gravity=10,f.workerEnabled=!1,f.nodes=[],f.edges=[],f.width=300,f.height=300,f.nodeMap={},f.nodeIdxMap={},f.updateCfg(i),f}return a.prototype.getDefaultCfg=function(){return{maxIteration:2e3,gravity:10,clustering:!1,clusterGravity:10}},a.prototype.execute=function(){return C(this,void 0,void 0,function(){var i,f,s,l,m;return x(this,function(h){switch(h.label){case 0:return i=this,f=i.nodes,!f||f.length===0?(i.onLayoutEnd&&i.onLayoutEnd(),[2]):(!i.width&&typeof window!="undefined"&&(i.width=window.innerWidth),!i.height&&typeof window!="undefined"&&(i.height=window.innerHeight),i.center||(i.center=[i.width/2,i.height/2]),s=i.center,f.length===1?(f[0].x=s[0],f[0].y=s[1],i.onLayoutEnd&&i.onLayoutEnd(),[2]):(l={},m={},f.forEach(function(d,v){(0,E.isNumber)(d.x)||(d.x=Math.random()*i.width),(0,E.isNumber)(d.y)||(d.y=Math.random()*i.height),l[d.id]=d,m[d.id]=v}),i.nodeMap=l,i.nodeIdxMap=m,i.nodeStrength=(0,u.proccessToFunc)(i.nodeStrength,1),i.edgeStrength=(0,u.proccessToFunc)(i.edgeStrength,1),[4,i.run()]));case 1:return h.sent(),[2]}})})},a.prototype.executeWithWorker=function(i,f){var s=this,l=s.nodes,m=s.center;if(!(!l||l.length===0)){if(l.length===1){l[0].x=m[0],l[0].y=m[1];return}var h={},d={};l.forEach(function(v,y){(0,E.isNumber)(v.x)||(v.x=Math.random()*s.width),(0,E.isNumber)(v.y)||(v.y=Math.random()*s.height),h[v.id]=v,d[v.id]=y}),s.nodeMap=h,s.nodeIdxMap=d,s.nodeStrength=(0,u.proccessToFunc)(s.nodeStrength,1),s.edgeStrength=(0,u.proccessToFunc)(s.edgeStrength,1),s.run(i,f)}},a.prototype.run=function(i,f){return C(this,void 0,void 0,function(){var s,l,m,h,d,v,y,_,D,b,S,k,A,K,R,P,N,U,B,ue,ae,de,ve,we,Se,Le,q,se=this;return x(this,function(ne){switch(ne.label){case 0:for(s=this,l=s.nodes,m=s.edges,h=s.maxIteration,!s.width&&typeof window!="undefined"&&(s.width=window.innerWidth),!s.height&&typeof window!="undefined"&&(s.height=window.innerHeight),d=l.length,s.linkDistance=(0,u.proccessToFunc)(s.linkDistance),s.edgeStrength=(0,u.proccessToFunc)(s.edgeStrength),v=(0,u.buildTextureDataWithTwoEdgeAttr)(l,m,s.linkDistance,s.edgeStrength),y=v.maxEdgePerVetex,_=v.array,s.degrees=(0,t.getDegree)(l.length,s.nodeIdxMap,m).map(function(pe){return pe.all}),D=[],b=[],S=[],k=[],A=[],K=[],R=[],s.getMass||(s.getMass=function(pe){return s.degrees[s.nodeIdxMap[pe.id]]||1}),P=s.gravity,N=s.center,l.forEach(function(pe,Me){D.push(s.getMass(pe)),b.push(s.nodeStrength(pe)),s.degrees[Me]||(s.degrees[Me]=0);var he=[N[0],N[1],P];if(s.getCenter){var De=s.getCenter(pe,s.degrees[Me]);De&&(0,E.isNumber)(De[0])&&(0,E.isNumber)(De[1])&&(0,E.isNumber)(De[2])&&(he=De)}S.push(he[0]),k.push(he[1]),A.push(he[2]),(0,E.isNumber)(pe.fx)&&(0,E.isNumber)(pe.fy)?(K.push(pe.fx||.001),R.push(pe.fy||.001)):(K.push(0),R.push(0))}),U=(0,u.arrayToTextureData)([D,s.degrees,b,K]),B=(0,u.arrayToTextureData)([S,k,A,R]),ue=s.workerEnabled,ue?ae=p.World.create({canvas:i,engineOptions:{supportCompute:!0}}):ae=p.World.create({engineOptions:{supportCompute:!0}}),de=s.onLayoutEnd,ve=[],_.forEach(function(pe){ve.push(pe)}),we=0;we<4;we++)ve.push(0);return Se=ae.createKernel(n.gForceBundle).setDispatch([d,1,1]).setBinding({u_Data:_,u_damping:s.damping,u_maxSpeed:s.maxSpeed,u_minMovement:s.minMovement,u_coulombDisScale:s.coulombDisScale,u_factor:s.factor,u_NodeAttributeArray1:U,u_NodeAttributeArray2:B,MAX_EDGE_PER_VERTEX:y,VERTEX_COUNT:d,u_AveMovement:ve,u_interval:s.interval}),Le=ae.createKernel(n.aveMovementBundle).setDispatch([1,1,1]).setBinding({u_Data:_,VERTEX_COUNT:d,u_AveMovement:[0,0,0,0]}),q=function(){return C(se,void 0,void 0,function(){var pe,Me,he;return x(this,function(De){switch(De.label){case 0:pe=0,De.label=1;case 1:return pe<h?[4,Se.execute()]:[3,5];case 2:return De.sent(),Le.setBinding({u_Data:Se}),[4,Le.execute()];case 3:De.sent(),Me=Math.max(.02,s.interval-pe*.002),Se.setBinding({u_interval:Me,u_AveMovement:Le}),De.label=4;case 4:return pe++,[3,1];case 5:return[4,Se.getOutput()];case 6:return he=De.sent(),i?f.postMessage({type:r.LAYOUT_MESSAGE.GPUEND,vertexEdgeData:he}):l.forEach(function(Pe,He){var Ze=he[4*He],Ue=he[4*He+1];Pe.x=Ze,Pe.y=Ue}),de&&de(),[2]}})})},[4,q()];case 1:return ne.sent(),[2]}})})},a.prototype.getType=function(){return"gForce-gpu"},a}(M.Base);w.GForceGPULayout=e},4156:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.aveMovementBundle=w.aveMovementCode=w.gForceBundle=w.gForceCode=void 0,w.gForceCode=`
import { globalInvocationID } from 'g-webgpu';

const MAX_EDGE_PER_VERTEX;
const VERTEX_COUNT;
const SHIFT_20 = 1048576;

@numthreads(1, 1, 1)
class GGForce {
  @in @out
  u_Data: vec4[];

  @in
  u_damping: float;
  
  @in
  u_maxSpeed: float;

  @in
  u_minMovement: float;

  @in
  u_AveMovement: vec4[];

  @in
  u_coulombDisScale: float;

  @in
  u_factor: float;

  @in
  u_NodeAttributeArray1: vec4[];

  @in
  u_NodeAttributeArray2: vec4[];

  @in
  u_interval: float;

  unpack_float(packedValue: float): ivec2 {
    const packedIntValue = int(packedValue);
    const v0 = packedIntValue / SHIFT_20;
    return [v0, packedIntValue - v0 * SHIFT_20];
  }

  calcRepulsive(i: int, currentNode: vec4): vec2 {
    let ax = 0, ay = 0;
    for (let j: int = 0; j < VERTEX_COUNT; j++) {
      if (i != j) {
        const nextNode = this.u_Data[j];
        const vx = currentNode[0] - nextNode[0];
        const vy = currentNode[1] - nextNode[1];
        const dist = sqrt(vx * vx + vy * vy) + 0.01;
        const n_dist = (dist + 0.1) * this.u_coulombDisScale;
        const direx = vx / dist;
        const direy = vy / dist;
        const attributesi = this.u_NodeAttributeArray1[i];
        const attributesj = this.u_NodeAttributeArray1[j];
        const massi = attributesi[0];
        const nodeStrengthi = attributesi[2];
        const nodeStrengthj = attributesj[2];
        const nodeStrength = (nodeStrengthi + nodeStrengthj) / 2;
        // const param = nodeStrength * this.u_factor / (n_dist * n_dist * massi);
        const param = nodeStrength * this.u_factor / (n_dist * n_dist);
        ax += direx * param;
        ay += direy * param;
      }
    }
    return [ax, ay];
  }

  calcGravity(i: int, currentNode: vec4, attributes2: vec4): vec2 {
    // note: attributes2 = [centerX, centerY, gravity, 0]

    const vx = currentNode[0] - attributes2[0];
    const vy = currentNode[1] - attributes2[1];
    
    const ax = vx * attributes2[2];
    const ay = vy * attributes2[2];
    
    return [ax, ay];
  }

  calcAttractive(i: int, currentNode: vec4, attributes1: vec4): vec2 {
    // note: attributes1 = [mass, degree, nodeSterngth, 0]

    const mass = attributes1[0];
    let ax = 0, ay = 0;
    // const arr_offset = int(floor(currentNode[2] + 0.5));
    // const length = int(floor(currentNode[3] + 0.5));

    const compressed = this.unpack_float(currentNode[2]);
    const length = compressed[0];
    const arr_offset = compressed[1];

    const node_buffer: vec4;
    for (let p: int = 0; p < MAX_EDGE_PER_VERTEX; p++) {
      if (p >= length) break;
      const arr_idx = arr_offset + 4 * p; // i \u8282\u70B9\u7684\u7B2C p \u6761\u8FB9\u5F00\u59CB\u7684\u5C0F\u683C\u5B50\u4F4D\u7F6E
      const buf_offset = arr_idx - arr_idx / 4 * 4;
      if (p == 0 || buf_offset == 0) {
        node_buffer = this.u_Data[int(arr_idx / 4)]; // \u5927\u683C\u5B50\uFF0C\u5927\u683C\u5B50\u4F4D\u7F6E=\u5C0F\u4E2A\u5B50\u4F4D\u7F6E / 4\uFF0C
      }

      let float_j: float = node_buffer[0];

      const nextNode = this.u_Data[int(float_j)];
      const vx = nextNode[0] - currentNode[0];
      const vy = nextNode[1] - currentNode[1];
      const dist = sqrt(vx * vx + vy * vy) + 0.01;
      const direx = vx / dist;
      const direy = vy / dist;
      const edgeLength = node_buffer[1];
      const edgeStrength = node_buffer[2];
      const diff: float = edgeLength - dist;//edgeLength
      // const param = diff * this.u_stiffness / mass; //
      const param = diff * edgeStrength / mass; // 
      ax -= direx * param;
      ay -= direy * param;
    }
    return [ax, ay];
  }

  @main
  compute() {
    const i = globalInvocationID.x;
    const currentNode = this.u_Data[i];
    const movement = u_AveMovement[0];
    let ax = 0, ay = 0;

    if (i >= VERTEX_COUNT || movement.x < u_minMovement) {
      this.u_Data[i] = currentNode;
      return;
    }

    // \u6BCF\u4E2A\u8282\u70B9\u5C5E\u6027\u5360\u4E24\u4E2A\u6570\u7EC4\u4E2D\u5404\u4E00\u683C
    // [mass, degree, nodeStrength, fx]
    const nodeAttributes1 = this.u_NodeAttributeArray1[i];
    // [centerX, centerY, centerGravity, fy]
    const nodeAttributes2 = this.u_NodeAttributeArray2[i];

    // repulsive
    const repulsive = this.calcRepulsive(i, currentNode);
    ax += repulsive[0];
    ay += repulsive[1];

    // attractive
    const attractive = this.calcAttractive(i, currentNode, nodeAttributes1);
    ax += attractive[0];
    ay += attractive[1];

    // gravity
    const gravity = this.calcGravity(i, currentNode, nodeAttributes2);
    ax -= gravity[0];
    ay -= gravity[1];

    // speed
    const param = this.u_interval * this.u_damping;
    let vx = ax * param;
    let vy = ay * param;
    const vlength = sqrt(vx * vx + vy * vy) + 0.0001;
    if (vlength > this.u_maxSpeed) {
      const param2 = this.u_maxSpeed / vlength;
      vx = param2 * vx;
      vy = param2 * vy;
    }

    // move
    if (nodeAttributes1[3] != 0 && nodeAttributes2[3] != 0) {
      this.u_Data[i] = [
        nodeAttributes1[3],
        nodeAttributes2[3],
        currentNode[2],
        0
      ];
    } else {
      const distx = vx * this.u_interval;
      const disty = vy * this.u_interval;
      const distLength = sqrt(distx * distx + disty * disty);
      this.u_Data[i] = [
        currentNode[0] + distx,
        currentNode[1] + disty,
        currentNode[2],
        distLength
      ];
    }
    
    // the avarage move distance
    // need to share memory
    
  }
}
`,w.gForceBundle='{"shaders":{"WGSL":"","GLSL450":"","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define MAX_EDGE_PER_VERTEX __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n#define SHIFT_20 1048576.0\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform float u_damping;\\nuniform float u_maxSpeed;\\nuniform float u_minMovement;\\nuniform sampler2D u_AveMovement;\\nuniform vec2 u_AveMovementSize;\\nvec4 getDatau_AveMovement(vec2 address2D) {\\n  return vec4(texture2D(u_AveMovement, address2D).rgba);\\n}\\nvec4 getDatau_AveMovement(float address1D) {\\n  return getDatau_AveMovement(addrTranslation_1Dto2D(address1D, u_AveMovementSize));\\n}\\nvec4 getDatau_AveMovement(int address1D) {\\n  return getDatau_AveMovement(float(address1D));\\n}\\nuniform float u_coulombDisScale;\\nuniform float u_factor;\\nuniform sampler2D u_NodeAttributeArray1;\\nuniform vec2 u_NodeAttributeArray1Size;\\nvec4 getDatau_NodeAttributeArray1(vec2 address2D) {\\n  return vec4(texture2D(u_NodeAttributeArray1, address2D).rgba);\\n}\\nvec4 getDatau_NodeAttributeArray1(float address1D) {\\n  return getDatau_NodeAttributeArray1(addrTranslation_1Dto2D(address1D, u_NodeAttributeArray1Size));\\n}\\nvec4 getDatau_NodeAttributeArray1(int address1D) {\\n  return getDatau_NodeAttributeArray1(float(address1D));\\n}\\nuniform sampler2D u_NodeAttributeArray2;\\nuniform vec2 u_NodeAttributeArray2Size;\\nvec4 getDatau_NodeAttributeArray2(vec2 address2D) {\\n  return vec4(texture2D(u_NodeAttributeArray2, address2D).rgba);\\n}\\nvec4 getDatau_NodeAttributeArray2(float address1D) {\\n  return getDatau_NodeAttributeArray2(addrTranslation_1Dto2D(address1D, u_NodeAttributeArray2Size));\\n}\\nvec4 getDatau_NodeAttributeArray2(int address1D) {\\n  return getDatau_NodeAttributeArray2(float(address1D));\\n}\\nuniform float u_interval;\\nivec2 unpack_float(float packedValue) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint packedIntValue = int(packedValue);\\nint v0 = packedIntValue / int(SHIFT_20);\\nreturn ivec2(v0, packedIntValue - (v0 * int(SHIFT_20)));}\\nvec2 calcRepulsive(int i, vec4 currentNode) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {if (i != j) {vec4 nextNode = getDatau_Data(j);\\nfloat vx = currentNode.x - nextNode.x;\\nfloat vy = currentNode.y - nextNode.y;\\nfloat dist = sqrt((vx * vx) + (vy * vy)) + 0.01;\\nfloat n_dist = (dist + 0.1) * u_coulombDisScale;\\nfloat direx = vx / dist;\\nfloat direy = vy / dist;\\nvec4 attributesi = getDatau_NodeAttributeArray1(i);\\nvec4 attributesj = getDatau_NodeAttributeArray1(j);\\nfloat massi = attributesi.x;\\nfloat nodeStrengthi = attributesi.z;\\nfloat nodeStrengthj = attributesj.z;\\nfloat nodeStrength = (nodeStrengthi + nodeStrengthj) / 2.0;\\nfloat param = (nodeStrength * u_factor) / (n_dist * n_dist);\\nax += direx * param;\\nay += direy * param;}}\\nreturn vec2(ax, ay);}\\nvec2 calcGravity(int i, vec4 currentNode, vec4 attributes2) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat vx = currentNode.x - attributes2.x;\\nfloat vy = currentNode.y - attributes2.y;\\nfloat ax = vx * attributes2.z;\\nfloat ay = vy * attributes2.z;\\nreturn vec2(ax, ay);}\\nvec2 calcAttractive(int i, vec4 currentNode, vec4 attributes1) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat mass = attributes1.x;\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nivec2 compressed = unpack_float(currentNode.z);\\nint length = compressed.x;\\nint arr_offset = compressed.y;\\nvec4 node_buffer;\\nfor (int p = 0; p < MAX_EDGE_PER_VERTEX; p++) {if (p >= length) {break;}\\nint arr_idx = arr_offset + (4 * p);\\nint buf_offset = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = getDatau_Data(int(arr_idx / 4));}\\nfloat float_j = node_buffer.x;\\nvec4 nextNode = getDatau_Data(int(float_j));\\nfloat vx = nextNode.x - currentNode.x;\\nfloat vy = nextNode.y - currentNode.y;\\nfloat dist = sqrt((vx * vx) + (vy * vy)) + 0.01;\\nfloat direx = vx / dist;\\nfloat direy = vy / dist;\\nfloat edgeLength = node_buffer.y;\\nfloat edgeStrength = node_buffer.z;\\nfloat diff = edgeLength - dist;\\nfloat param = (diff * edgeStrength) / mass;\\nax -= direx * param;\\nay -= direy * param;}\\nreturn vec2(ax, ay);}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint i = globalInvocationID.x;\\nvec4 currentNode = getDatau_Data(i);\\nvec4 movement = getDatau_AveMovement(0.0);\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nif ((i >= VERTEX_COUNT) || (movement.x < u_minMovement)) {gl_FragColor = vec4(currentNode);\\nreturn ;}\\nvec4 nodeAttributes1 = getDatau_NodeAttributeArray1(i);\\nvec4 nodeAttributes2 = getDatau_NodeAttributeArray2(i);\\nvec2 repulsive = calcRepulsive(i, currentNode);\\nax += repulsive.x;\\nay += repulsive.y;\\nvec2 attractive = calcAttractive(i, currentNode, nodeAttributes1);\\nax += attractive.x;\\nay += attractive.y;\\nvec2 gravity = calcGravity(i, currentNode, nodeAttributes2);\\nax -= gravity.x;\\nay -= gravity.y;\\nfloat param = u_interval * u_damping;\\nfloat vx = ax * param;\\nfloat vy = ay * param;\\nfloat vlength = sqrt((vx * vx) + (vy * vy)) + 0.0001;\\nif (vlength > u_maxSpeed) {float param2 = u_maxSpeed / vlength;\\nvx = param2 * vx;\\nvy = param2 * vy;}\\nif ((nodeAttributes1.w != 0.0) && (nodeAttributes2.w != 0.0)) {gl_FragColor = vec4(vec4(nodeAttributes1.w, nodeAttributes2.w, currentNode.z, 0.0));}else {float distx = vx * u_interval;\\nfloat disty = vy * u_interval;\\nfloat distLength = sqrt((distx * distx) + (disty * disty));\\ngl_FragColor = vec4(vec4(currentNode.x + distx, currentNode.y + disty, currentNode.z, distLength));}if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"MAX_EDGE_PER_VERTEX","type":"Float","runtime":true},{"name":"VERTEX_COUNT","type":"Float","runtime":true},{"name":"SHIFT_20","type":"Float","value":1048576,"runtime":false}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]},{"name":"u_damping","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_maxSpeed","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_minMovement","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_AveMovement","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_coulombDisScale","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_factor","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_NodeAttributeArray1","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_NodeAttributeArray2","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_interval","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_Data","size":[1,1],"length":1},"needPingpong":true}}',w.aveMovementCode=`
const VERTEX_COUNT;
@numthreads(1, 1, 1)
class CalcAveMovement {
  @in
  u_Data: vec4[];
  @in
  u_iter: float;
  @in @out
  u_AveMovement: vec4[];
  @main
  compute() {
    let movement = 0;
    for (let j: int = 0; j < VERTEX_COUNT; j++) {
      const vertex = this.u_Data[j];
      movement += vertex[3];
    }
    movement = movement / float(VERTEX_COUNT);
    this.u_AveMovement[0] = [movement, 0, 0, 0];
  }
}
`,w.aveMovementBundle='{"shaders":{"WGSL":"","GLSL450":"","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform float u_iter;\\nuniform sampler2D u_AveMovement;\\nuniform vec2 u_AveMovementSize;\\nvec4 getDatau_AveMovement(vec2 address2D) {\\n  return vec4(texture2D(u_AveMovement, address2D).rgba);\\n}\\nvec4 getDatau_AveMovement(float address1D) {\\n  return getDatau_AveMovement(addrTranslation_1Dto2D(address1D, u_AveMovementSize));\\n}\\nvec4 getDatau_AveMovement(int address1D) {\\n  return getDatau_AveMovement(float(address1D));\\n}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat movement = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {vec4 vertex = getDatau_Data(j);\\nmovement += vertex.w;}\\nmovement = movement / float(VERTEX_COUNT);\\ngl_FragColor = vec4(vec4(movement, 0.0, 0.0, 0.0));if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"VERTEX_COUNT","type":"Float","runtime":true}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_iter","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_AveMovement","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_AveMovement","size":[1,1],"length":1},"needPingpong":true}}'},68708:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var E=function(p,u){return E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])},E(p,u)};return function(p,u){if(typeof u!="function"&&u!==null)throw new TypeError("Class extends value "+String(u)+" is not a constructor or null");E(p,u);function t(){this.constructor=p}p.prototype=u===null?Object.create(u):(t.prototype=u.prototype,new t)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.GridLayout=void 0;var C=G(45612),x=G(2021),M=function(E){T(p,E);function p(u){var t=E.call(this)||this;return t.begin=[0,0],t.preventOverlap=!0,t.preventOverlapPadding=10,t.condense=!1,t.sortBy="degree",t.nodes=[],t.edges=[],t.width=300,t.height=300,t.row=0,t.col=0,t.cellWidth=0,t.cellHeight=0,t.cellUsed={},t.id2manPos={},t.onLayoutEnd=function(){},t.updateCfg(u),t}return p.prototype.getDefaultCfg=function(){return{begin:[0,0],preventOverlap:!0,preventOverlapPadding:10,condense:!1,rows:void 0,cols:void 0,position:void 0,sortBy:"degree",nodeSize:30}},p.prototype.execute=function(){var u=this,t=u.nodes,n=u.edges,r=u.begin,e=t.length;if(e===0)return u.onLayoutEnd&&u.onLayoutEnd(),{nodes:t,edges:n};if(e===1)return t[0].x=r[0],t[0].y=r[1],u.onLayoutEnd&&u.onLayoutEnd(),{nodes:t,edges:n};var o=u.sortBy,a=u.width,i=u.height,f=u.condense,s=u.preventOverlapPadding,l=u.preventOverlap,m=u.nodeSpacing,h=u.nodeSize,d=[];t.forEach(function(U){d.push(U)});var v={};if(d.forEach(function(U,B){v[U.id]=B}),(o==="degree"||!(0,C.isString)(o)||d[0][o]===void 0)&&(o="degree",(0,C.isNaN)(t[0].degree))){var y=(0,C.getDegree)(d.length,v,n);d.forEach(function(U,B){U.degree=y[B].all})}d.sort(function(U,B){return B[o]-U[o]}),!a&&typeof window!="undefined"&&(a=window.innerWidth),!i&&typeof window!="undefined"&&(i=window.innerHeight);var _=u.rows,D=u.cols!=null?u.cols:u.columns;if(u.cells=e,_!=null&&D!=null?(u.rows=_,u.cols=D):_!=null&&D==null?(u.rows=_,u.cols=Math.ceil(u.cells/u.rows)):_==null&&D!=null?(u.cols=D,u.rows=Math.ceil(u.cells/u.cols)):(u.splits=Math.sqrt(u.cells*u.height/u.width),u.rows=Math.round(u.splits),u.cols=Math.round(u.width/u.height*u.splits)),u.rows=Math.max(u.rows,1),u.cols=Math.max(u.cols,1),u.cols*u.rows>u.cells){var b=u.small(),S=u.large();(b-1)*S>=u.cells?u.small(b-1):(S-1)*b>=u.cells&&u.large(S-1)}else for(;u.cols*u.rows<u.cells;){var b=u.small(),S=u.large();(S+1)*b>=u.cells?u.large(S+1):u.small(b+1)}if(u.cellWidth=a/u.cols,u.cellHeight=i/u.rows,f&&(u.cellWidth=0,u.cellHeight=0),l||m){var k=(0,C.getFuncByUnknownType)(10,m),A=(0,C.getFuncByUnknownType)(30,h,!1);d.forEach(function(U){(!U.x||!U.y)&&(U.x=0,U.y=0);var B=A(U)||30,ue,ae;(0,C.isArray)(B)?(ue=B[0],ae=B[1]):(ue=B,ae=B);var de=k!==void 0?k(U):s,ve=ue+de,we=ae+de;u.cellWidth=Math.max(u.cellWidth,ve),u.cellHeight=Math.max(u.cellHeight,we)})}u.cellUsed={},u.row=0,u.col=0,u.id2manPos={};for(var K=0;K<d.length;K++){var R=d[K],P=void 0;if(u.position&&(P=u.position(R)),P&&(P.row!==void 0||P.col!==void 0)){var N={row:P.row,col:P.col};if(N.col===void 0)for(N.col=0;u.used(N.row,N.col);)N.col++;else if(N.row===void 0)for(N.row=0;u.used(N.row,N.col);)N.row++;u.id2manPos[R.id]=N,u.use(N.row,N.col)}u.getPos(R)}return u.onLayoutEnd&&u.onLayoutEnd(),{edges:n,nodes:d}},p.prototype.small=function(u){var t=this,n,r=t.rows||5,e=t.cols||5;if(u==null)n=Math.min(r,e);else{var o=Math.min(r,e);o===t.rows?t.rows=u:t.cols=u}return n},p.prototype.large=function(u){var t=this,n,r=t.rows||5,e=t.cols||5;if(u==null)n=Math.max(r,e);else{var o=Math.max(r,e);o===t.rows?t.rows=u:t.cols=u}return n},p.prototype.used=function(u,t){var n=this;return n.cellUsed["c-".concat(u,"-").concat(t)]||!1},p.prototype.use=function(u,t){var n=this;n.cellUsed["c-".concat(u,"-").concat(t)]=!0},p.prototype.moveToNextCell=function(){var u=this,t=u.cols||5;u.col++,u.col>=t&&(u.col=0,u.row++)},p.prototype.getPos=function(u){var t=this,n=t.begin,r=t.cellWidth,e=t.cellHeight,o,a,i=t.id2manPos[u.id];if(i)o=i.col*r+r/2+n[0],a=i.row*e+e/2+n[1];else{for(;t.used(t.row,t.col);)t.moveToNextCell();o=t.col*r+r/2+n[0],a=t.row*e+e/2+n[1],t.use(t.row,t.col),t.moveToNextCell()}u.x=o,u.y=a},p.prototype.getType=function(){return"grid"},p}(x.Base);w.GridLayout=M},66929:function(Je,w,G){"use strict";var T=this&&this.__createBinding||(Object.create?function(y,_,D,b){b===void 0&&(b=D);var S=Object.getOwnPropertyDescriptor(_,D);(!S||("get"in S?!_.__esModule:S.writable||S.configurable))&&(S={enumerable:!0,get:function(){return _[D]}}),Object.defineProperty(y,b,S)}:function(y,_,D,b){b===void 0&&(b=D),y[b]=_[D]}),C=this&&this.__exportStar||function(y,_){for(var D in y)D!=="default"&&!Object.prototype.hasOwnProperty.call(_,D)&&T(_,y,D)};Object.defineProperty(w,"__esModule",{value:!0}),w.ERLayout=w.ForceAtlas2Layout=w.ComboCombinedLayout=w.ComboForceLayout=w.GForceGPULayout=w.FruchtermanGPULayout=w.FruchtermanLayout=w.MDSLayout=w.ConcentricLayout=w.RadialLayout=w.DagreCompoundLayout=w.DagreLayout=w.CircularLayout=w.ForceLayout=w.Force2Layout=w.GForceLayout=w.RandomLayout=w.GridLayout=w.Layouts=w.Layout=void 0;var x=G(68708);Object.defineProperty(w,"GridLayout",{enumerable:!0,get:function(){return x.GridLayout}});var M=G(22971);Object.defineProperty(w,"RandomLayout",{enumerable:!0,get:function(){return M.RandomLayout}});var E=G(15091);Object.defineProperty(w,"GForceLayout",{enumerable:!0,get:function(){return E.GForceLayout}});var p=G(87905);Object.defineProperty(w,"Force2Layout",{enumerable:!0,get:function(){return p.Force2Layout}});var u=G(84496);Object.defineProperty(w,"ForceLayout",{enumerable:!0,get:function(){return u.ForceLayout}});var t=G(93978);Object.defineProperty(w,"CircularLayout",{enumerable:!0,get:function(){return t.CircularLayout}});var n=G(78389);Object.defineProperty(w,"DagreLayout",{enumerable:!0,get:function(){return n.DagreLayout}});var r=G(63473);Object.defineProperty(w,"DagreCompoundLayout",{enumerable:!0,get:function(){return r.DagreCompoundLayout}});var e=G(89211);Object.defineProperty(w,"RadialLayout",{enumerable:!0,get:function(){return e.RadialLayout}});var o=G(52342);Object.defineProperty(w,"ConcentricLayout",{enumerable:!0,get:function(){return o.ConcentricLayout}});var a=G(57042);Object.defineProperty(w,"MDSLayout",{enumerable:!0,get:function(){return a.MDSLayout}});var i=G(11929);Object.defineProperty(w,"FruchtermanLayout",{enumerable:!0,get:function(){return i.FruchtermanLayout}});var f=G(22073);Object.defineProperty(w,"FruchtermanGPULayout",{enumerable:!0,get:function(){return f.FruchtermanGPULayout}});var s=G(46230);Object.defineProperty(w,"GForceGPULayout",{enumerable:!0,get:function(){return s.GForceGPULayout}});var l=G(51773);Object.defineProperty(w,"ComboForceLayout",{enumerable:!0,get:function(){return l.ComboForceLayout}});var m=G(3467);Object.defineProperty(w,"ComboCombinedLayout",{enumerable:!0,get:function(){return m.ComboCombinedLayout}});var h=G(3856);Object.defineProperty(w,"ForceAtlas2Layout",{enumerable:!0,get:function(){return h.ForceAtlas2Layout}});var d=G(66812);Object.defineProperty(w,"ERLayout",{enumerable:!0,get:function(){return d.ERLayout}});var v=G(88755);Object.defineProperty(w,"Layout",{enumerable:!0,get:function(){return v.Layout}}),Object.defineProperty(w,"Layouts",{enumerable:!0,get:function(){return v.Layouts}}),C(G(48781),w)},88755:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.Layouts=w.Layout=void 0;var T=G(27653),C=G(68708),x=G(22971),M=G(87905),E=G(15091),p=G(84496),u=G(93978),t=G(78389),n=G(89211),r=G(52342),e=G(57042),o=G(11929),a=G(22073),i=G(46230),f=G(51773),s=G(3467),l=G(3856),m=G(66812),h=G(63473),d=G(45612),v=function(){function y(_){var D=(0,T.getLayoutByName)(_.type);this.layoutInstance=new D(_)}return y.prototype.layout=function(_){return this.layoutInstance.layout(_)},y.prototype.updateCfg=function(_){this.layoutInstance.updateCfg(_)},y.prototype.init=function(_){this.correctLayers(_.nodes),this.layoutInstance.init(_)},y.prototype.correctLayers=function(_){if(_!=null&&_.length){var D=1/0,b=[];if(_.forEach(function(k){(0,d.isString)(k.layer)&&(k.layer=parseInt(k.layer,10)),!(k.layer===void 0||isNaN(k.layer))&&(b.push(k),k.layer<D&&(D=k.layer))}),D<=0){var S=Math.abs(D)+1;b.forEach(function(k){return k.layer+=S})}}},y.prototype.execute=function(){this.layoutInstance.execute()},y.prototype.getDefaultCfg=function(){return this.layoutInstance.getDefaultCfg()},y.prototype.destroy=function(){return this.layoutInstance.destroy()},y}();w.Layout=v,w.Layouts={force:p.ForceLayout,fruchterman:o.FruchtermanLayout,forceAtlas2:l.ForceAtlas2Layout,gForce:E.GForceLayout,force2:M.Force2Layout,dagre:t.DagreLayout,dagreCompound:h.DagreCompoundLayout,circular:u.CircularLayout,radial:n.RadialLayout,concentric:r.ConcentricLayout,grid:C.GridLayout,mds:e.MDSLayout,comboForce:f.ComboForceLayout,comboCombined:s.ComboCombinedLayout,random:x.RandomLayout,"gForce-gpu":i.GForceGPULayout,"fruchterman-gpu":a.FruchtermanGPULayout,er:m.ERLayout}},57042:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var p=function(u,t){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(n[e]=r[e])},p(u,t)};return function(u,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");p(u,t);function n(){this.constructor=u}u.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.MDSLayout=void 0;var C=G(32373),x=G(45612),M=G(2021),E=function(p){T(u,p);function u(t){var n=p.call(this)||this;return n.center=[0,0],n.linkDistance=50,n.nodes=[],n.edges=[],n.onLayoutEnd=function(){},n.updateCfg(t),n}return u.prototype.getDefaultCfg=function(){return{center:[0,0],linkDistance:50}},u.prototype.execute=function(){var t=this,n=t.nodes,r=t.edges,e=r===void 0?[]:r,o=t.center;if(!n||n.length===0){t.onLayoutEnd&&t.onLayoutEnd();return}if(n.length===1){n[0].x=o[0],n[0].y=o[1],t.onLayoutEnd&&t.onLayoutEnd();return}var a=t.linkDistance,i=(0,x.getAdjMatrix)({nodes:n,edges:e},!1),f=(0,x.floydWarshall)(i);t.handleInfinity(f);var s=(0,x.scaleMatrix)(f,a);t.scaledDistances=s;var l=t.runMDS();return t.positions=l,l.forEach(function(m,h){n[h].x=m[0]+o[0],n[h].y=m[1]+o[1]}),t.onLayoutEnd&&t.onLayoutEnd(),{nodes:n,edges:e}},u.prototype.runMDS=function(){var t=this,n=2,r=t.scaledDistances,e=C.Matrix.mul(C.Matrix.pow(r,2),-.5),o=e.mean("row"),a=e.mean("column"),i=e.mean();e.add(i).subRowVector(o).subColumnVector(a);var f=new C.SingularValueDecomposition(e),s=C.Matrix.sqrt(f.diagonalMatrix).diagonal();return f.leftSingularVectors.toJSON().map(function(l){return C.Matrix.mul([l],[s]).toJSON()[0].splice(0,n)})},u.prototype.handleInfinity=function(t){var n=-999999;t.forEach(function(r){r.forEach(function(e){e!==1/0&&n<e&&(n=e)})}),t.forEach(function(r,e){r.forEach(function(o,a){o===1/0&&(t[e][a]=n)})})},u.prototype.getType=function(){return"mds"},u}(M.Base);w.MDSLayout=E},89211:function(Je,w,G){"use strict";var T=this&&this.__createBinding||(Object.create?function(x,M,E,p){p===void 0&&(p=E);var u=Object.getOwnPropertyDescriptor(M,E);(!u||("get"in u?!M.__esModule:u.writable||u.configurable))&&(u={enumerable:!0,get:function(){return M[E]}}),Object.defineProperty(x,p,u)}:function(x,M,E,p){p===void 0&&(p=E),x[p]=M[E]}),C=this&&this.__exportStar||function(x,M){for(var E in x)E!=="default"&&!Object.prototype.hasOwnProperty.call(M,E)&&T(M,x,E)};Object.defineProperty(w,"__esModule",{value:!0}),C(G(67608),w)},30297:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var T=G(32373),C=function(){function x(M){this.distances=M.distances,this.dimension=M.dimension||2,this.linkDistance=M.linkDistance}return x.prototype.layout=function(){var M=this,E=M.dimension,p=M.distances,u=M.linkDistance;try{var t=T.Matrix.mul(T.Matrix.pow(p,2),-.5),n=t.mean("row"),r=t.mean("column"),e=t.mean();t.add(e).subRowVector(n).subColumnVector(r);var o=new T.SingularValueDecomposition(t),a=T.Matrix.sqrt(o.diagonalMatrix).diagonal();return o.leftSingularVectors.toJSON().map(function(m){return T.Matrix.mul([m],[a]).toJSON()[0].splice(0,E)})}catch(m){for(var i=[],f=0;f<p.length;f++){var s=Math.random()*u,l=Math.random()*u;i.push([s,l])}return i}},x}();w.default=C},67608:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var e=function(o,a){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,f){i.__proto__=f}||function(i,f){for(var s in f)Object.prototype.hasOwnProperty.call(f,s)&&(i[s]=f[s])},e(o,a)};return function(o,a){if(typeof a!="function"&&a!==null)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");e(o,a);function i(){this.constructor=o}o.prototype=a===null?Object.create(a):(i.prototype=a.prototype,new i)}}(),C=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(w,"__esModule",{value:!0}),w.RadialLayout=void 0;var x=G(45612),M=G(2021),E=C(G(30297)),p=C(G(81328));function u(e){for(var o=e.length,a=e[0].length,i=[],f=0;f<o;f++){for(var s=[],l=0;l<a;l++)e[f][l]!==0?s.push(1/(e[f][l]*e[f][l])):s.push(0);i.push(s)}return i}function t(e,o){var a=-1;return e.forEach(function(i,f){i.id===o&&(a=f)}),a}function n(e,o){return Math.sqrt((e[0]-o[0])*(e[0]-o[0])+(e[1]-o[1])*(e[1]-o[1]))}var r=function(e){T(o,e);function o(a){var i=e.call(this)||this;return i.maxIteration=1e3,i.focusNode=null,i.unitRadius=null,i.linkDistance=50,i.preventOverlap=!1,i.strictRadial=!0,i.maxPreventOverlapIteration=200,i.sortStrength=10,i.nodes=[],i.edges=[],i.updateCfg(a),i}return o.prototype.getDefaultCfg=function(){return{maxIteration:1e3,focusNode:null,unitRadius:null,linkDistance:50,preventOverlap:!1,nodeSize:void 0,nodeSpacing:void 0,strictRadial:!0,maxPreventOverlapIteration:200,sortBy:void 0,sortStrength:10}},o.prototype.execute=function(){var a=this,i=a.nodes,f=a.edges||[];if(!i||i.length===0){a.onLayoutEnd&&a.onLayoutEnd();return}!a.width&&typeof window!="undefined"&&(a.width=window.innerWidth),!a.height&&typeof window!="undefined"&&(a.height=window.innerHeight),a.center||(a.center=[a.width/2,a.height/2]);var s=a.center;if(i.length===1){i[0].x=s[0],i[0].y=s[1],a.onLayoutEnd&&a.onLayoutEnd();return}var l=a.linkDistance,m=null;if((0,x.isString)(a.focusNode)){for(var h=!1,d=0;d<i.length;d++)i[d].id===a.focusNode&&(m=i[d],a.focusNode=m,h=!0,d=i.length);h||(m=null)}else m=a.focusNode;m||(m=i[0],a.focusNode=m);var v=t(i,m.id);v<0&&(v=0),a.focusIndex=v;var y=(0,x.getAdjMatrix)({nodes:i,edges:f},!1),_=(0,x.floydWarshall)(y),D=a.maxToFocus(_,v);a.handleInfinity(_,v,D+1),a.distances=_;var b=_[v],S=a.width||500,k=a.height||500,A=S-s[0]>s[0]?s[0]:S-s[0],K=k-s[1]>s[1]?s[1]:k-s[1];A===0&&(A=S/2),K===0&&(K=k/2);var R=K>A?A:K,P=Math.max.apply(Math,b),N=[];b.forEach(function(pe,Me){a.unitRadius||(a.unitRadius=R/P),N[Me]=pe*a.unitRadius}),a.radii=N;var U=a.eIdealDisMatrix();a.eIdealDistances=U;var B=u(U);a.weights=B;var ue=new E.default({linkDistance:l,distances:U}),ae=ue.layout();ae.forEach(function(pe){(0,x.isNaN)(pe[0])&&(pe[0]=Math.random()*l),(0,x.isNaN)(pe[1])&&(pe[1]=Math.random()*l)}),a.positions=ae,ae.forEach(function(pe,Me){i[Me].x=pe[0]+s[0],i[Me].y=pe[1]+s[1]}),ae.forEach(function(pe){pe[0]-=ae[v][0],pe[1]-=ae[v][1]}),a.run();var de=a.preventOverlap,ve=a.nodeSize,we,Se=a.strictRadial;if(de){var Le=a.nodeSpacing,q;(0,x.isNumber)(Le)?q=function(){return Le}:(0,x.isFunction)(Le)?q=Le:q=function(){return 0},ve?(0,x.isArray)(ve)?we=function(pe){var Me=ve[0]>ve[1]?ve[0]:ve[1];return Me+q(pe)}:we=function(pe){return ve+q(pe)}:we=function(pe){if(pe.size){if((0,x.isArray)(pe.size)){var Me=pe.size[0]>pe.size[1]?pe.size[0]:pe.size[1];return Me+q(pe)}if((0,x.isObject)(pe.size)){var Me=pe.size.width>pe.size.height?pe.size.width:pe.size.height;return Me+q(pe)}return pe.size+q(pe)}return 10+q(pe)};var se={nodes:i,nodeSizeFunc:we,adjMatrix:y,positions:ae,radii:N,height:k,width:S,strictRadial:Se,focusID:v,iterations:a.maxPreventOverlapIteration||200,k:ae.length/4.5},ne=new p.default(se);ae=ne.layout()}return ae.forEach(function(pe,Me){i[Me].x=pe[0]+s[0],i[Me].y=pe[1]+s[1]}),a.onLayoutEnd&&a.onLayoutEnd(),{nodes:i,edges:f}},o.prototype.run=function(){for(var a=this,i=a.maxIteration,f=a.positions||[],s=a.weights||[],l=a.eIdealDistances||[],m=a.radii||[],h=0;h<=i;h++){var d=h/i;a.oneIteration(d,f,m,l,s)}},o.prototype.oneIteration=function(a,i,f,s,l){var m=this,h=1-a,d=m.focusIndex;i.forEach(function(v,y){var _=n(v,[0,0]),D=_===0?0:1/_;if(y!==d){var b=0,S=0,k=0;i.forEach(function(K,R){if(y!==R){var P=n(v,K),N=P===0?0:1/P,U=s[R][y];k+=l[y][R],b+=l[y][R]*(K[0]+U*(v[0]-K[0])*N),S+=l[y][R]*(K[1]+U*(v[1]-K[1])*N)}});var A=f[y]===0?0:1/f[y];k*=h,k+=a*A*A,b*=h,b+=a*A*v[0]*D,v[0]=b/k,S*=h,S+=a*A*v[1]*D,v[1]=S/k}})},o.prototype.eIdealDisMatrix=function(){var a=this,i=a.nodes;if(!i)return[];var f=a.distances,s=a.linkDistance,l=a.radii||[],m=a.unitRadius||50,h=[];return f&&f.forEach(function(d,v){var y=[];d.forEach(function(_,D){if(v===D)y.push(0);else if(l[v]===l[D])if(a.sortBy==="data")y.push(_*(Math.abs(v-D)*a.sortStrength)/(l[v]/m));else if(a.sortBy){var b=i[v][a.sortBy]||0,S=i[D][a.sortBy]||0;(0,x.isString)(b)&&(b=b.charCodeAt(0)),(0,x.isString)(S)&&(S=S.charCodeAt(0)),y.push(_*(Math.abs(b-S)*a.sortStrength)/(l[v]/m))}else y.push(_*s/(l[v]/m));else{var k=(s+m)/2;y.push(_*k)}}),h.push(y)}),h},o.prototype.handleInfinity=function(a,i,f){for(var s=a.length,l=0;l<s;l++)if(a[i][l]===1/0){a[i][l]=f,a[l][i]=f;for(var m=0;m<s;m++)a[l][m]!==1/0&&a[i][m]===1/0&&(a[i][m]=f+a[l][m],a[m][i]=f+a[l][m])}for(var l=0;l<s;l++)if(l!==i){for(var m=0;m<s;m++)if(a[l][m]===1/0){var h=Math.abs(a[i][l]-a[i][m]);h=h===0?1:h,a[l][m]=h}}},o.prototype.maxToFocus=function(a,i){for(var f=0,s=0;s<a[i].length;s++)a[i][s]!==1/0&&(f=a[i][s]>f?a[i][s]:f);return f},o.prototype.getType=function(){return"radial"},o}(M.Base);w.RadialLayout=r},81328:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0});var G=800,T=function(){function C(x){this.disp=[],this.positions=x.positions,this.adjMatrix=x.adjMatrix,this.focusID=x.focusID,this.radii=x.radii,this.iterations=x.iterations||10,this.height=x.height||10,this.width=x.width||10,this.speed=x.speed||100,this.gravity=x.gravity||10,this.nodeSizeFunc=x.nodeSizeFunc,this.k=x.k||5,this.strictRadial=x.strictRadial,this.nodes=x.nodes}return C.prototype.layout=function(){var x=this,M=x.positions,E=[],p=x.iterations,u=x.width/10;x.maxDisplace=u,x.disp=E;for(var t=0;t<p;t++)M.forEach(function(n,r){E[r]={x:0,y:0}}),x.getRepulsion(),x.updatePositions();return M},C.prototype.getRepulsion=function(){var x=this,M=x.positions,E=x.nodes,p=x.disp,u=x.k,t=x.radii||[];M.forEach(function(n,r){p[r]={x:0,y:0},M.forEach(function(e,o){if(r!==o&&t[r]===t[o]){var a=n[0]-e[0],i=n[1]-e[1],f=Math.sqrt(a*a+i*i);if(f===0){f=1;var s=r>o?1:-1;a=.01*s,i=.01*s}if(f<x.nodeSizeFunc(E[r])/2+x.nodeSizeFunc(E[o])/2){var l=u*u/f;p[r].x+=a/f*l,p[r].y+=i/f*l}}})})},C.prototype.updatePositions=function(){var x=this,M=x.positions,E=x.disp,p=x.speed,u=x.strictRadial,t=x.focusID,n=x.maxDisplace||x.width/10;u&&E.forEach(function(e,o){var a=M[o][0]-M[t][0],i=M[o][1]-M[t][1],f=Math.sqrt(a*a+i*i),s=i/f,l=-a/f,m=Math.sqrt(e.x*e.x+e.y*e.y),h=Math.acos((s*e.x+l*e.y)/m);h>Math.PI/2&&(h-=Math.PI/2,s*=-1,l*=-1);var d=Math.cos(h)*m;e.x=s*d,e.y=l*d});var r=x.radii;M.forEach(function(e,o){if(o!==t){var a=Math.sqrt(E[o].x*E[o].x+E[o].y*E[o].y);if(a>0&&o!==t){var i=Math.min(n*(p/G),a);if(e[0]+=E[o].x/a*i,e[1]+=E[o].y/a*i,u){var f=e[0]-M[t][0],s=e[1]-M[t][1],l=Math.sqrt(f*f+s*s);f=f/l*r[o],s=s/l*r[o],e[0]=M[t][0]+f,e[1]=M[t][1]+s}}}})},C}();w.default=T},22971:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var M=function(E,p){return M=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(u,t){u.__proto__=t}||function(u,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(u[n]=t[n])},M(E,p)};return function(E,p){if(typeof p!="function"&&p!==null)throw new TypeError("Class extends value "+String(p)+" is not a constructor or null");M(E,p);function u(){this.constructor=E}E.prototype=p===null?Object.create(p):(u.prototype=p.prototype,new u)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.RandomLayout=void 0;var C=G(2021),x=function(M){T(E,M);function E(p){var u=M.call(this)||this;return u.center=[0,0],u.width=300,u.height=300,u.nodes=[],u.edges=[],u.onLayoutEnd=function(){},u.updateCfg(p),u}return E.prototype.getDefaultCfg=function(){return{center:[0,0],width:300,height:300}},E.prototype.execute=function(){var p=this,u=p.nodes,t=.9,n=p.center;return!p.width&&typeof window!="undefined"&&(p.width=window.innerWidth),!p.height&&typeof window!="undefined"&&(p.height=window.innerHeight),u&&u.forEach(function(r){r.x=(Math.random()-.5)*t*p.width+n[0],r.y=(Math.random()-.5)*t*p.height+n[1]}),p.onLayoutEnd&&p.onLayoutEnd(),{nodes:u,edges:this.edges}},E.prototype.getType=function(){return"random"},E}(C.Base);w.RandomLayout=x},48781:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0})},27653:function(Je,w,G){"use strict";var T=this&&this.__extends||function(){var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])},t(n,r)};return function(n,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");t(n,r);function e(){this.constructor=n}n.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}}();Object.defineProperty(w,"__esModule",{value:!0}),w.getLayoutByName=w.unRegisterLayout=w.registerLayout=void 0;var C=G(2021),x=G(45612),M=new Map,E=function(t,n){if(M.get(t)&&console.warn("The layout with the name ".concat(t," exists already, it will be overridden")),(0,x.isObject)(n)){var r=function(e){T(o,e);function o(a){var i=this,f;i=e.call(this)||this;var s=i,l={},m=Object.assign({},s.getDefaultCfg(),((f=n.getDefaultCfg)===null||f===void 0?void 0:f.call(n))||{});return Object.assign(l,m,n,a),Object.keys(l).forEach(function(h){var d=l[h];s[h]=d}),i}return o}(C.Base);M.set(t,r)}else M.set(t,n);return M.get(t)};w.registerLayout=E;var p=function(t){M.has(t)&&M.delete(t)};w.unRegisterLayout=p;var u=function(t){return M.has(t)?M.get(t):null};w.getLayoutByName=u},20108:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.isArray=void 0,w.isArray=Array.isArray},41416:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.getFuncByUnknownType=w.getFunc=w.isFunction=void 0;var T=G(45612),C=G(10047),x=function(p){return typeof p=="function"};w.isFunction=x;var M=function(p,u,t){var n;return t?n=t:(0,C.isNumber)(p)?n=function(){return p}:n=function(){return u},n};w.getFunc=M;var E=function(p,u,t){return t===void 0&&(t=!0),!u&&u!==0?function(n){return n.size?(0,T.isArray)(n.size)?n.size[0]>n.size[1]?n.size[0]:n.size[1]:(0,T.isObject)(n.size)?n.size.width>n.size.height?n.size.width:n.size.height:n.size:p}:(0,w.isFunction)(u)?u:(0,C.isNumber)(u)?function(){return u}:(0,T.isArray)(u)?function(){if(t){var n=Math.max.apply(Math,u);return isNaN(n)?p:n}return u}:(0,T.isObject)(u)?function(){if(t){var n=Math.max(u.width,u.height);return isNaN(n)?p:n}return[u.width,u.height]}:function(){return p}};w.getFuncByUnknownType=E},71379:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.arrayToTextureData=w.attributesToTextureData=w.buildTextureDataWithTwoEdgeAttr=w.buildTextureData=w.proccessToFunc=void 0;var T=G(45612),C=G(79039),x=function(t,n){var r;return t?(0,T.isNumber)(t)?r=function(){return t}:r=t:r=function(){return n||1},r};w.proccessToFunc=x;var M=function(t,n){var r=[],e=[],o={},a=0;for(a=0;a<t.length;a++){var i=t[a];o[i.id]=a,r.push(i.x),r.push(i.y),r.push(0),r.push(0),e.push([])}for(a=0;a<n.length;a++){var f=n[a],s=(0,C.getEdgeTerminal)(f,"source"),l=(0,C.getEdgeTerminal)(f,"target");!isNaN(o[s])&&!isNaN(o[l])&&(e[o[s]].push(o[l]),e[o[l]].push(o[s]))}var m=0;for(a=0;a<t.length;a++){var h=r.length,d=e[a],v=d.length;r[a*4+2]=h,r[a*4+3]=v,m=Math.max(m,v);for(var y=0;y<v;++y){var _=d[y];r.push(+_)}}for(;r.length%4!==0;)r.push(0);return{maxEdgePerVetex:m,array:new Float32Array(r)}};w.buildTextureData=M;var E=function(t,n,r,e){var o=[],a=[],i={},f=0;for(f=0;f<t.length;f++){var s=t[f];i[s.id]=f,o.push(s.x),o.push(s.y),o.push(0),o.push(0),a.push([])}for(f=0;f<n.length;f++){var l=n[f],m=(0,C.getEdgeTerminal)(l,"source"),h=(0,C.getEdgeTerminal)(l,"target");a[i[m]].push(i[h]),a[i[m]].push(r(l)),a[i[m]].push(e(l)),a[i[m]].push(0),a[i[h]].push(i[m]),a[i[h]].push(r(l)),a[i[h]].push(e(l)),a[i[h]].push(0)}var d=0;for(f=0;f<t.length;f++){var v=o.length,y=a[f],_=y.length;o[f*4+2]=v+1048576*_/4,o[f*4+3]=0,d=Math.max(d,_/4);for(var D=0;D<_;++D){var b=y[D];o.push(+b)}}for(;o.length%4!==0;)o.push(0);return{maxEdgePerVetex:d,array:new Float32Array(o)}};w.buildTextureDataWithTwoEdgeAttr=E;var p=function(t,n){var r=[],e=t.length,o={};return n.forEach(function(a){t.forEach(function(i,f){if(o[a[i]]===void 0&&(o[a[i]]=Object.keys(o).length),r.push(o[a[i]]),f===e-1)for(;r.length%4!==0;)r.push(0)})}),{array:new Float32Array(r),count:Object.keys(o).length}};w.attributesToTextureData=p;var u=function(t){for(var n=[],r=t.length,e=t[0].length,o=function(i){t.forEach(function(f,s){if(n.push(f[i]),s===r-1)for(;n.length%4!==0;)n.push(0)})},a=0;a<e;a++)o(a);return new Float32Array(n)};w.arrayToTextureData=u},45612:function(Je,w,G){"use strict";var T=this&&this.__createBinding||(Object.create?function(x,M,E,p){p===void 0&&(p=E);var u=Object.getOwnPropertyDescriptor(M,E);(!u||("get"in u?!M.__esModule:u.writable||u.configurable))&&(u={enumerable:!0,get:function(){return M[E]}}),Object.defineProperty(x,p,u)}:function(x,M,E,p){p===void 0&&(p=E),x[p]=M[E]}),C=this&&this.__exportStar||function(x,M){for(var E in x)E!=="default"&&!Object.prototype.hasOwnProperty.call(M,E)&&T(M,x,E)};Object.defineProperty(w,"__esModule",{value:!0}),C(G(26544),w),C(G(20108),w),C(G(10047),w),C(G(79039),w),C(G(22283),w),C(G(41416),w)},79039:function(Je,w,G){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.getCoreNodeAndRelativeLeafNodes=w.getAvgNodePosition=w.getLayoutBBox=w.traverseTreeUp=w.scaleMatrix=w.getAdjMatrix=w.floydWarshall=w.getDegreeMap=w.getDegree=w.getEdgeTerminal=void 0;var T=G(20108),C=G(10047),x=G(22283),M=function(m,h){var d=m[h];return(0,x.isObject)(d)?d.cell:d};w.getEdgeTerminal=M;var E=function(m,h,d){for(var v=[],y=0;y<m;y++)v[y]={in:0,out:0,all:0};return d&&d.forEach(function(_){var D=(0,w.getEdgeTerminal)(_,"source"),b=(0,w.getEdgeTerminal)(_,"target");D&&v[h[D]]&&(v[h[D]].out+=1,v[h[D]].all+=1),b&&v[h[b]]&&(v[h[b]].in+=1,v[h[b]].all+=1)}),v};w.getDegree=E;var p=function(m,h){var d={};return m.forEach(function(v){d[v.id]={in:0,out:0,all:0}}),h&&h.forEach(function(v){var y=(0,w.getEdgeTerminal)(v,"source"),_=(0,w.getEdgeTerminal)(v,"target");y&&(d[y].out+=1,d[y].all+=1),_&&(d[_].in+=1,d[_].all+=1)}),d};w.getDegreeMap=p;var u=function(m){for(var h=[],d=m.length,v=0;v<d;v+=1){h[v]=[];for(var y=0;y<d;y+=1)v===y?h[v][y]=0:m[v][y]===0||!m[v][y]?h[v][y]=1/0:h[v][y]=m[v][y]}for(var _=0;_<d;_+=1)for(var v=0;v<d;v+=1)for(var y=0;y<d;y+=1)h[v][y]>h[v][_]+h[_][y]&&(h[v][y]=h[v][_]+h[_][y]);return h};w.floydWarshall=u;var t=function(m,h){var d=m.nodes,v=m.edges,y=[],_={};if(!d)throw new Error("invalid nodes data!");return d&&d.forEach(function(D,b){_[D.id]=b;var S=[];y.push(S)}),v==null||v.forEach(function(D){var b=(0,w.getEdgeTerminal)(D,"source"),S=(0,w.getEdgeTerminal)(D,"target"),k=_[b],A=_[S];k===void 0||A===void 0||(y[k][A]=1,h||(y[A][k]=1))}),y};w.getAdjMatrix=t;var n=function(m,h){var d=[];return m.forEach(function(v){var y=[];v.forEach(function(_){y.push(_*h)}),d.push(y)}),d};w.scaleMatrix=n;var r=function(m,h){if(m&&m.children){for(var d=m.children.length-1;d>=0;d--)if(!r(m.children[d],h))return}return!!h(m)},e=function(m,h){typeof h=="function"&&r(m,h)};w.traverseTreeUp=e;var o=function(m){var h=1/0,d=1/0,v=-1/0,y=-1/0;return m.forEach(function(_){var D=_.size;(0,T.isArray)(D)?D.length===1&&(D=[D[0],D[0]]):(0,C.isNumber)(D)?D=[D,D]:(D===void 0||isNaN(D))&&(D=[30,30]);var b=[D[0]/2,D[1]/2],S=_.x-b[0],k=_.x+b[0],A=_.y-b[1],K=_.y+b[1];h>S&&(h=S),d>A&&(d=A),v<k&&(v=k),y<K&&(y=K)}),{minX:h,minY:d,maxX:v,maxY:y}};w.getLayoutBBox=o;var a=function(m){var h={x:0,y:0};m.forEach(function(v){h.x+=v.x||0,h.y+=v.y||0});var d=m.length||1;return{x:h.x/d,y:h.y/d}};w.getAvgNodePosition=a;var i=function(m,h,d){var v,y;return m==="source"?((v=d==null?void 0:d.find(function(_){return _.target===h.id}))===null||v===void 0?void 0:v.source)||{}:((y=d==null?void 0:d.find(function(_){return _.source===h.id}))===null||y===void 0?void 0:y.target)||{}},f=function(m,h,d){var v=[];switch(m){case"source":v=d==null?void 0:d.filter(function(_){return _.source===h.id}).map(function(_){return _.target});break;case"target":v=d==null?void 0:d.filter(function(_){return _.target===h.id}).map(function(_){return _.source});break;case"both":v=d==null?void 0:d.filter(function(_){return _.source===h.id}).map(function(_){return _.target}).concat(d==null?void 0:d.filter(function(_){return _.target===h.id}).map(function(_){return _.source}));break;default:break}var y=new Set(v);return Array.from(y)},s=function(m,h,d,v,y){var _=d[h]||"",D=(v==null?void 0:v.filter(function(b){return b[h]===_}))||[];return m==="leaf"&&(D=D.filter(function(b){var S,k;return((S=y[b.id])===null||S===void 0?void 0:S.in)===0||((k=y[b.id])===null||k===void 0?void 0:k.out)===0})),D},l=function(m,h,d,v,y,_){var D=y[h.id],b=D.in,S=D.out,k=h,A=[];b===0?(k=i("source",h,d),A=f("both",k,d).map(function(R){return _[R]})):S===0&&(k=i("target",h,d),A=f("both",k,d).map(function(R){return _[R]})),A=A.filter(function(R){return y[R.id]&&(y[R.id].in===0||y[R.id].out===0)});var K=s(m,v,h,A,y);return{coreNode:k,relativeLeafNodes:A,sameTypeLeafNodes:K}};w.getCoreNodeAndRelativeLeafNodes=l},10047:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.toNumber=w.isNaN=w.isNumber=void 0;var G=function(x){return typeof x=="number"};w.isNumber=G;var T=function(x){return Number.isNaN(Number(x))};w.isNaN=T;var C=function(x){var M=parseFloat(x);return(0,w.isNaN)(M)?x:M};w.toNumber=C},22283:function(Je,w){"use strict";var G=this&&this.__assign||function(){return G=Object.assign||function(x){for(var M,E=1,p=arguments.length;E<p;E++){M=arguments[E];for(var u in M)Object.prototype.hasOwnProperty.call(M,u)&&(x[u]=M[u])}return x},G.apply(this,arguments)};Object.defineProperty(w,"__esModule",{value:!0}),w.clone=w.isObject=void 0;var T=function(x){return x!==null&&typeof x=="object"};w.isObject=T;var C=function(x){if(x===null)return x;if(x instanceof Date)return new Date(x.getTime());if(x instanceof Array){var M=[];return x.forEach(function(p){M.push(p)}),M.map(function(p){return(0,w.clone)(p)})}if(typeof x=="object"&&Object.keys(x).length){var E=G({},x);return Object.keys(E).forEach(function(p){E[p]=(0,w.clone)(E[p])}),E}return x};w.clone=C},26544:function(Je,w){"use strict";Object.defineProperty(w,"__esModule",{value:!0}),w.camelize=w.isString=void 0;var G=function(x){return typeof x=="string"};w.isString=G;var T=function(x){var M=Object.create(null);return function(E){var p=M[E];return p||(M[E]=x(E))}},C=/-(\w)/g;w.camelize=T(function(x){return x.replace(C,function(M,E){return E?E.toUpperCase():""})})},82867:function(Je,w,G){"use strict";G.d(w,{Dg:function(){return n},lh:function(){return E},m$:function(){return x},vs:function(){return u},zu:function(){return M}});var T=G(96661),C=G(69341);function x(e,o,a){var i=[0,0,0,0,0,0,0,0,0];return T.fromTranslation(i,a),T.multiply(e,i,o)}function M(e,o,a){var i=[0,0,0,0,0,0,0,0,0];return T.fromRotation(i,a),T.multiply(e,i,o)}function E(e,o,a){var i=[0,0,0,0,0,0,0,0,0];return T.fromScaling(i,a),T.multiply(e,i,o)}function p(e,o,a){return T.multiply(e,a,o)}function u(e,o){for(var a=e?[].concat(e):[1,0,0,0,1,0,0,0,1],i=0,f=o.length;i<f;i++){var s=o[i];switch(s[0]){case"t":x(a,a,[s[1],s[2]]);break;case"s":E(a,a,[s[1],s[2]]);break;case"r":M(a,a,s[1]);break;case"m":p(a,a,s[1]);break;default:break}}return a}function t(e,o){return e[0]*o[1]-o[0]*e[1]}function n(e,o,a){var i=C.angle(e,o),f=t(e,o)>=0;return a?f?Math.PI*2-i:i:f?i:Math.PI*2-i}function r(e,o,a){return a?(e[0]=o[1],e[1]=-1*o[0]):(e[0]=-1*o[1],e[1]=o[0]),e}},69534:function(Je,w,G){"use strict";var T;T={value:!0};var C=G(22077);function x(e,o,a){var i=[0,0,0,0,0,0,0,0,0];return C.mat3.fromTranslation(i,a),C.mat3.multiply(e,i,o)}T=x;function M(e,o,a){var i=[0,0,0,0,0,0,0,0,0];return C.mat3.fromRotation(i,a),C.mat3.multiply(e,i,o)}T=M;function E(e,o,a){var i=[0,0,0,0,0,0,0,0,0];return C.mat3.fromScaling(i,a),C.mat3.multiply(e,i,o)}T=E;function p(e,o,a){return C.mat3.multiply(e,a,o)}function u(e,o){for(var a=e?[].concat(e):[1,0,0,0,1,0,0,0,1],i=0,f=o.length;i<f;i++){var s=o[i];switch(s[0]){case"t":x(a,a,[s[1],s[2]]);break;case"s":E(a,a,[s[1],s[2]]);break;case"r":M(a,a,s[1]);break;case"m":p(a,a,s[1]);break;default:break}}return a}w.vs=u;function t(e,o){return e[0]*o[1]-o[0]*e[1]}T=t;function n(e,o,a){var i=C.vec2.angle(e,o),f=t(e,o)>=0;return a?f?Math.PI*2-i:i:f?i:Math.PI*2-i}T=n;function r(e,o,a){return a?(e[0]=o[1],e[1]=-1*o[0]):(e[0]=-1*o[1],e[1]=o[0]),e}T=r},72461:function(Je,w,G){"use strict";G.d(w,{e9:function(){return n},Wq:function(){return Jt},tr:function(){return s},wb:function(){return h},zx:function(){return De}});var T=G(98667),C=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/ig,x=/[^\s\,]+/ig;function M(Z){var J=Z||[];if((0,T.isArray)(J))return J;if((0,T.isString)(J))return J=J.match(C),(0,T.each)(J,function(ie,me){if(ie=ie.match(x),ie[0].length>1){var Oe=ie[0].charAt(0);ie.splice(1,0,ie[0].substr(1)),ie[0]=Oe}(0,T.each)(ie,function(Ce,Ne){isNaN(Ce)||(ie[Ne]=+Ce)}),J[me]=ie}),J}var E=M,p=G(69341);function u(Z,J,ie,me){var Oe=[],Ce=!!me,Ne,Y,$,Ie,_e,Ae,Be;if(Ce){$=me[0],Ie=me[1];for(var ze=0,Ye=Z.length;ze<Ye;ze+=1){var Ve=Z[ze];$=p.min([0,0],$,Ve),Ie=p.max([0,0],Ie,Ve)}}for(var ze=0,qe=Z.length;ze<qe;ze+=1){var Ve=Z[ze];if(ze===0&&!ie)Be=Ve;else if(ze===qe-1&&!ie)Ae=Ve,Oe.push(Be),Oe.push(Ae);else{var je=[ze?ze-1:qe-1,ze-1][ie?0:1];Ne=Z[je],Y=Z[ie?(ze+1)%qe:ze+1];var at=[0,0];at=p.sub(at,Y,Ne),at=p.scale(at,at,J);var vt=p.distance(Ve,Ne),ct=p.distance(Ve,Y),st=vt+ct;st!==0&&(vt/=st,ct/=st);var gt=p.scale([0,0],at,-vt),Et=p.scale([0,0],at,ct);Ae=p.add([0,0],Ve,gt),_e=p.add([0,0],Ve,Et),_e=p.min([0,0],_e,p.max([0,0],Y,Ve)),_e=p.max([0,0],_e,p.min([0,0],Y,Ve)),gt=p.sub([0,0],_e,Ve),gt=p.scale([0,0],gt,-vt/ct),Ae=p.add([0,0],Ve,gt),Ae=p.min([0,0],Ae,p.max([0,0],Ne,Ve)),Ae=p.max([0,0],Ae,p.min([0,0],Ne,Ve)),Et=p.sub([0,0],Ve,Ae),Et=p.scale([0,0],Et,ct/vt),_e=p.add([0,0],Ve,Et),Ce&&(Ae=p.max([0,0],Ae,$),Ae=p.min([0,0],Ae,Ie),_e=p.max([0,0],_e,$),_e=p.min([0,0],_e,Ie)),Oe.push(Be),Oe.push(Ae),Be=_e}}return ie&&Oe.push(Oe.shift()),Oe}function t(Z,J,ie){J===void 0&&(J=!1),ie===void 0&&(ie=[[0,0],[1,1]]);for(var me=!!J,Oe=[],Ce=0,Ne=Z.length;Ce<Ne;Ce+=2)Oe.push([Z[Ce],Z[Ce+1]]);for(var Y=u(Oe,.4,me,ie),$=Oe.length,Ie=[],_e,Ae,Be,Ce=0;Ce<$-1;Ce+=1)_e=Y[Ce*2],Ae=Y[Ce*2+1],Be=Oe[Ce+1],Ie.push(["C",_e[0],_e[1],Ae[0],Ae[1],Be[0],Be[1]]);return me&&(_e=Y[$],Ae=Y[$+1],Be=Oe[0],Ie.push(["C",_e[0],_e[1],Ae[0],Ae[1],Be[0],Be[1]])),Ie}var n=t;function r(Z,J,ie){var me=null,Oe=ie;return J<Oe&&(Oe=J,me="add"),Z<Oe&&(Oe=Z,me="del"),{type:me,min:Oe}}var e=function(Z,J){var ie=Z.length,me=J.length,Oe,Ce,Ne=0;if(ie===0||me===0)return null;for(var Y=[],$=0;$<=ie;$++)Y[$]=[],Y[$][0]={min:$};for(var Ie=0;Ie<=me;Ie++)Y[0][Ie]={min:Ie};for(var $=1;$<=ie;$++){Oe=Z[$-1];for(var Ie=1;Ie<=me;Ie++){Ce=J[Ie-1],isEqual(Oe,Ce)?Ne=0:Ne=1;var _e=Y[$-1][Ie].min+1,Ae=Y[$][Ie-1].min+1,Be=Y[$-1][Ie-1].min+Ne;Y[$][Ie]=r(_e,Ae,Be)}}return Y};function o(Z,J){var ie=e(Z,J),me=Z.length,Oe=J.length,Ce=[],Ne=1,Y=1;if(ie[me][Oe]!==me){for(var $=1;$<=me;$++){var Ie=ie[$][$].min;Y=$;for(var _e=Ne;_e<=Oe;_e++)ie[$][_e].min<Ie&&(Ie=ie[$][_e].min,Y=_e);Ne=Y,ie[$][Ne].type&&Ce.push({index:$-1,type:ie[$][Ne].type})}for(var $=Ce.length-1;$>=0;$--)Ne=Ce[$].index,Ce[$].type==="add"?Z.splice(Ne,0,[].concat(Z[Ne])):Z.splice(Ne,1)}if(me=Z.length,me<Oe)for(var $=0;$<Oe-me;$++)Z[me-1][0]==="z"||Z[me-1][0]==="Z"?Z.splice(me-2,0,Z[me-2]):Z.push(Z[me-1]);return Z}var a=`	
\v\f\r \xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029`,i=new RegExp("([a-z])["+a+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+a+"]*,?["+a+"]*)+)","ig"),f=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+a+"]*,?["+a+"]*","ig");function s(Z){if(!Z)return null;if((0,T.isArray)(Z))return Z;var J={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},ie=[];return String(Z).replace(i,function(me,Oe,Ce){var Ne=[],Y=Oe.toLowerCase();if(Ce.replace(f,function($,Ie){Ie&&Ne.push(+Ie)}),Y==="m"&&Ne.length>2&&(ie.push([Oe].concat(Ne.splice(0,2))),Y="l",Oe=Oe==="m"?"l":"L"),Y==="o"&&Ne.length===1&&ie.push([Oe,Ne[0]]),Y==="r")ie.push([Oe].concat(Ne));else for(;Ne.length>=J[Y]&&(ie.push([Oe].concat(Ne.splice(0,J[Y]))),!!J[Y]););return""}),ie}var l=/[a-z]/;function m(Z,J){return[J[0]+(J[0]-Z[0]),J[1]+(J[1]-Z[1])]}function h(Z){var J=s(Z);if(!J||!J.length)return[["M",0,0]];for(var ie=!1,me=0;me<J.length;me++){var Oe=J[me][0];if(l.test(Oe)||["V","H","T","S"].indexOf(Oe)>=0){ie=!0;break}}if(!ie)return J;var Ce=[],Ne=0,Y=0,$=0,Ie=0,_e=0,Ae,Be,ze=J[0];(ze[0]==="M"||ze[0]==="m")&&(Ne=+ze[1],Y=+ze[2],$=Ne,Ie=Y,_e++,Ce[0]=["M",Ne,Y]);for(var me=_e,Ye=J.length;me<Ye;me++){var Ve=J[me],qe=Ce[me-1],je=[],Oe=Ve[0],at=Oe.toUpperCase();if(Oe!==at)switch(je[0]=at,at){case"A":je[1]=Ve[1],je[2]=Ve[2],je[3]=Ve[3],je[4]=Ve[4],je[5]=Ve[5],je[6]=+Ve[6]+Ne,je[7]=+Ve[7]+Y;break;case"V":je[1]=+Ve[1]+Y;break;case"H":je[1]=+Ve[1]+Ne;break;case"M":$=+Ve[1]+Ne,Ie=+Ve[2]+Y,je[1]=$,je[2]=Ie;break;default:for(var vt=1,ct=Ve.length;vt<ct;vt++)je[vt]=+Ve[vt]+(vt%2?Ne:Y)}else je=J[me];switch(at){case"Z":Ne=+$,Y=+Ie;break;case"H":Ne=je[1],je=["L",Ne,Y];break;case"V":Y=je[1],je=["L",Ne,Y];break;case"T":Ne=je[1],Y=je[2];var st=m([qe[1],qe[2]],[qe[3],qe[4]]);je=["Q",st[0],st[1],Ne,Y];break;case"S":Ne=je[je.length-2],Y=je[je.length-1];var gt=qe.length,Et=m([qe[gt-4],qe[gt-3]],[qe[gt-2],qe[gt-1]]);je=["C",Et[0],Et[1],je[1],je[2],Ne,Y];break;case"M":$=je[je.length-2],Ie=je[je.length-1];break;default:Ne=je[je.length-2],Y=je[je.length-1]}Ce.push(je)}return Ce}var d=Math.PI*2,v=function(Z,J,ie,me,Oe,Ce,Ne){var Y=Z.x,$=Z.y;Y*=J,$*=ie;var Ie=me*Y-Oe*$,_e=Oe*Y+me*$;return{x:Ie+Ce,y:_e+Ne}},y=function(Z,J){var ie=J===1.5707963267948966?.551915024494:J===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(J/4),me=Math.cos(Z),Oe=Math.sin(Z),Ce=Math.cos(Z+J),Ne=Math.sin(Z+J);return[{x:me-Oe*ie,y:Oe+me*ie},{x:Ce+Ne*ie,y:Ne-Ce*ie},{x:Ce,y:Ne}]},_=function(Z,J,ie,me){var Oe=Z*me-J*ie<0?-1:1,Ce=Z*ie+J*me;return Ce>1&&(Ce=1),Ce<-1&&(Ce=-1),Oe*Math.acos(Ce)},D=function(Z,J,ie,me,Oe,Ce,Ne,Y,$,Ie,_e,Ae){var Be=Math.pow(Oe,2),ze=Math.pow(Ce,2),Ye=Math.pow(_e,2),Ve=Math.pow(Ae,2),qe=Be*ze-Be*Ve-ze*Ye;qe<0&&(qe=0),qe/=Be*Ve+ze*Ye,qe=Math.sqrt(qe)*(Ne===Y?-1:1);var je=qe*Oe/Ce*Ae,at=qe*-Ce/Oe*_e,vt=Ie*je-$*at+(Z+ie)/2,ct=$*je+Ie*at+(J+me)/2,st=(_e-je)/Oe,gt=(Ae-at)/Ce,Et=(-_e-je)/Oe,Vt=(-Ae-at)/Ce,qt=_(1,0,st,gt),Kt=_(st,gt,Et,Vt);return Y===0&&Kt>0&&(Kt-=d),Y===1&&Kt<0&&(Kt+=d),[vt,ct,qt,Kt]},b=function(Z){var J=Z.px,ie=Z.py,me=Z.cx,Oe=Z.cy,Ce=Z.rx,Ne=Z.ry,Y=Z.xAxisRotation,$=Y===void 0?0:Y,Ie=Z.largeArcFlag,_e=Ie===void 0?0:Ie,Ae=Z.sweepFlag,Be=Ae===void 0?0:Ae,ze=[];if(Ce===0||Ne===0)return[{x1:0,y1:0,x2:0,y2:0,x:me,y:Oe}];var Ye=Math.sin($*d/360),Ve=Math.cos($*d/360),qe=Ve*(J-me)/2+Ye*(ie-Oe)/2,je=-Ye*(J-me)/2+Ve*(ie-Oe)/2;if(qe===0&&je===0)return[{x1:0,y1:0,x2:0,y2:0,x:me,y:Oe}];Ce=Math.abs(Ce),Ne=Math.abs(Ne);var at=Math.pow(qe,2)/Math.pow(Ce,2)+Math.pow(je,2)/Math.pow(Ne,2);at>1&&(Ce*=Math.sqrt(at),Ne*=Math.sqrt(at));var vt=D(J,ie,me,Oe,Ce,Ne,_e,Be,Ye,Ve,qe,je),ct=vt[0],st=vt[1],gt=vt[2],Et=vt[3],Vt=Math.abs(Et)/(d/4);Math.abs(1-Vt)<1e-7&&(Vt=1);var qt=Math.max(Math.ceil(Vt),1);Et/=qt;for(var Kt=0;Kt<qt;Kt++)ze.push(y(gt,Et)),gt+=Et;return ze.map(function($t){var or=v($t[0],Ce,Ne,Ve,Ye,ct,st),Zt=or.x,Rt=or.y,cr=v($t[1],Ce,Ne,Ve,Ye,ct,st),er=cr.x,fr=cr.y,W=v($t[2],Ce,Ne,Ve,Ye,ct,st),H=W.x,X=W.y;return{x1:Zt,y1:Rt,x2:er,y2:fr,x:H,y:X}})};function S(Z,J,ie,me,Oe,Ce,Ne,Y,$){var Ie=b({px:Z,py:J,cx:Y,cy:$,rx:ie,ry:me,xAxisRotation:Oe,largeArcFlag:Ce,sweepFlag:Ne});return Ie.reduce(function(_e,Ae){var Be=Ae.x1,ze=Ae.y1,Ye=Ae.x2,Ve=Ae.y2,qe=Ae.x,je=Ae.y;return _e.push(Be,ze,Ye,Ve,qe,je),_e},[])}function k(Z,J){"TQ".indexOf(Z[0])<0&&(J.qx=null,J.qy=null);var ie=Z.slice(1),me=ie[0],Oe=ie[1];switch(Z[0]){case"M":return J.x=me,J.y=Oe,Z;case"A":return["C"].concat(arcToCubic.apply(0,[J.x1,J.y1].concat(Z.slice(1))));case"Q":return J.qx=me,J.qy=Oe,["C"].concat(quadToCubic.apply(0,[J.x1,J.y1].concat(Z.slice(1))));case"L":return["C"].concat(lineToCubic(J.x1,J.y1,Z[1],Z[2]));case"H":return["C"].concat(lineToCubic(J.x1,J.y1,Z[1],J.y1));case"V":return["C"].concat(lineToCubic(J.x1,J.y1,J.x1,Z[1]));case"Z":return["C"].concat(lineToCubic(J.x1,J.y1,J.x,J.y));default:}return Z}function A(Z,J){J===void 0&&(J=!1);for(var ie=path2Absolute(Z),me={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null},Oe=[],Ce="",Ne=ie.length,Y,$,Ie=[],_e=0;_e<Ne;_e+=1)ie[_e]&&(Ce=ie[_e][0]),Oe[_e]=Ce,ie[_e]=segmentToCubic(ie[_e],me),K(ie,Oe,_e),Ne=ie.length,Ce==="Z"&&Ie.push(_e),Y=ie[_e],$=Y.length,me.x1=+Y[$-2],me.y1=+Y[$-1],me.x2=+Y[$-4]||me.x1,me.y2=+Y[$-3]||me.y1;return J?[ie,Ie]:ie}function K(Z,J,ie){if(Z[ie].length>7){Z[ie].shift();for(var me=Z[ie],Oe=ie;me.length;)J[ie]="A",Z.splice(Oe+=1,0,["C"].concat(me.splice(0,6)));Z.splice(ie,1)}}var R=function(Z,J,ie,me,Oe){var Ce=-3*J+9*ie-9*me+3*Oe,Ne=Z*Ce+6*J-12*ie+6*me;return Z*Ne-3*J+3*ie},P=function(Z,J,ie,me,Oe,Ce,Ne,Y,$){$===null&&($=1),$=$>1?1:$<0?0:$;for(var Ie=$/2,_e=12,Ae=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],Be=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],ze=0,Ye=0;Ye<_e;Ye++){var Ve=Ie*Ae[Ye]+Ie,qe=R(Ve,Z,ie,Oe,Ne),je=R(Ve,J,me,Ce,Y),at=qe*qe+je*je;ze+=Be[Ye]*Math.sqrt(at)}return Ie*ze},N=function(Z,J,ie,me,Oe,Ce,Ne,Y){for(var $=[],Ie=[[],[]],_e,Ae,Be,ze,Ye=0;Ye<2;++Ye){if(Ye===0?(Ae=6*Z-12*ie+6*Oe,_e=-3*Z+9*ie-9*Oe+3*Ne,Be=3*ie-3*Z):(Ae=6*J-12*me+6*Ce,_e=-3*J+9*me-9*Ce+3*Y,Be=3*me-3*J),Math.abs(_e)<1e-12){if(Math.abs(Ae)<1e-12)continue;ze=-Be/Ae,ze>0&&ze<1&&$.push(ze);continue}var Ve=Ae*Ae-4*Be*_e,qe=Math.sqrt(Ve);if(!(Ve<0)){var je=(-Ae+qe)/(2*_e);je>0&&je<1&&$.push(je);var at=(-Ae-qe)/(2*_e);at>0&&at<1&&$.push(at)}}for(var vt=$.length,ct=vt,st;vt--;)ze=$[vt],st=1-ze,Ie[0][vt]=st*st*st*Z+3*st*st*ze*ie+3*st*ze*ze*Oe+ze*ze*ze*Ne,Ie[1][vt]=st*st*st*J+3*st*st*ze*me+3*st*ze*ze*Ce+ze*ze*ze*Y;return Ie[0][ct]=Z,Ie[1][ct]=J,Ie[0][ct+1]=Ne,Ie[1][ct+1]=Y,Ie[0].length=Ie[1].length=ct+2,{min:{x:Math.min.apply(0,Ie[0]),y:Math.min.apply(0,Ie[1])},max:{x:Math.max.apply(0,Ie[0]),y:Math.max.apply(0,Ie[1])}}},U=function(Z,J,ie,me,Oe,Ce,Ne,Y){if(!(Math.max(Z,ie)<Math.min(Oe,Ne)||Math.min(Z,ie)>Math.max(Oe,Ne)||Math.max(J,me)<Math.min(Ce,Y)||Math.min(J,me)>Math.max(Ce,Y))){var $=(Z*me-J*ie)*(Oe-Ne)-(Z-ie)*(Oe*Y-Ce*Ne),Ie=(Z*me-J*ie)*(Ce-Y)-(J-me)*(Oe*Y-Ce*Ne),_e=(Z-ie)*(Ce-Y)-(J-me)*(Oe-Ne);if(_e){var Ae=$/_e,Be=Ie/_e,ze=+Ae.toFixed(2),Ye=+Be.toFixed(2);if(!(ze<+Math.min(Z,ie).toFixed(2)||ze>+Math.max(Z,ie).toFixed(2)||ze<+Math.min(Oe,Ne).toFixed(2)||ze>+Math.max(Oe,Ne).toFixed(2)||Ye<+Math.min(J,me).toFixed(2)||Ye>+Math.max(J,me).toFixed(2)||Ye<+Math.min(Ce,Y).toFixed(2)||Ye>+Math.max(Ce,Y).toFixed(2)))return{x:Ae,y:Be}}}},B=function(Z,J,ie){return J>=Z.x&&J<=Z.x+Z.width&&ie>=Z.y&&ie<=Z.y+Z.height},ue=function(Z,J,ie,me){return Z===null&&(Z=J=ie=me=0),J===null&&(J=Z.y,ie=Z.width,me=Z.height,Z=Z.x),{x:Z,y:J,width:ie,w:ie,height:me,h:me,x2:Z+ie,y2:J+me,cx:Z+ie/2,cy:J+me/2,r1:Math.min(ie,me)/2,r2:Math.max(ie,me)/2,r0:Math.sqrt(ie*ie+me*me)/2,path:rectPath(Z,J,ie,me),vb:[Z,J,ie,me].join(" ")}},ae=function(Z,J){return Z=ue(Z),J=ue(J),B(J,Z.x,Z.y)||B(J,Z.x2,Z.y)||B(J,Z.x,Z.y2)||B(J,Z.x2,Z.y2)||B(Z,J.x,J.y)||B(Z,J.x2,J.y)||B(Z,J.x,J.y2)||B(Z,J.x2,J.y2)||(Z.x<J.x2&&Z.x>J.x||J.x<Z.x2&&J.x>Z.x)&&(Z.y<J.y2&&Z.y>J.y||J.y<Z.y2&&J.y>Z.y)},de=function(Z,J,ie,me,Oe,Ce,Ne,Y){isArray(Z)||(Z=[Z,J,ie,me,Oe,Ce,Ne,Y]);var $=N.apply(null,Z);return ue($.min.x,$.min.y,$.max.x-$.min.x,$.max.y-$.min.y)},ve=function(Z,J,ie,me,Oe,Ce,Ne,Y,$){var Ie=1-$,_e=Math.pow(Ie,3),Ae=Math.pow(Ie,2),Be=$*$,ze=Be*$,Ye=_e*Z+Ae*3*$*ie+Ie*3*$*$*Oe+ze*Ne,Ve=_e*J+Ae*3*$*me+Ie*3*$*$*Ce+ze*Y,qe=Z+2*$*(ie-Z)+Be*(Oe-2*ie+Z),je=J+2*$*(me-J)+Be*(Ce-2*me+J),at=ie+2*$*(Oe-ie)+Be*(Ne-2*Oe+ie),vt=me+2*$*(Ce-me)+Be*(Y-2*Ce+me),ct=Ie*Z+$*ie,st=Ie*J+$*me,gt=Ie*Oe+$*Ne,Et=Ie*Ce+$*Y,Vt=90-Math.atan2(qe-at,je-vt)*180/Math.PI;return{x:Ye,y:Ve,m:{x:qe,y:je},n:{x:at,y:vt},start:{x:ct,y:st},end:{x:gt,y:Et},alpha:Vt}},we=function(Z,J,ie){var me=de(Z),Oe=de(J);if(!ae(me,Oe))return ie?0:[];for(var Ce=P.apply(0,Z),Ne=P.apply(0,J),Y=~~(Ce/8),$=~~(Ne/8),Ie=[],_e=[],Ae={},Be=ie?0:[],ze=0;ze<Y+1;ze++){var Ye=ve.apply(0,Z.concat(ze/Y));Ie.push({x:Ye.x,y:Ye.y,t:ze/Y})}for(var ze=0;ze<$+1;ze++){var Ye=ve.apply(0,J.concat(ze/$));_e.push({x:Ye.x,y:Ye.y,t:ze/$})}for(var ze=0;ze<Y;ze++)for(var Ve=0;Ve<$;Ve++){var qe=Ie[ze],je=Ie[ze+1],at=_e[Ve],vt=_e[Ve+1],ct=Math.abs(je.x-qe.x)<.001?"y":"x",st=Math.abs(vt.x-at.x)<.001?"y":"x",gt=U(qe.x,qe.y,je.x,je.y,at.x,at.y,vt.x,vt.y);if(gt){if(Ae[gt.x.toFixed(4)]===gt.y.toFixed(4))continue;Ae[gt.x.toFixed(4)]=gt.y.toFixed(4);var Et=qe.t+Math.abs((gt[ct]-qe[ct])/(je[ct]-qe[ct]))*(je.t-qe.t),Vt=at.t+Math.abs((gt[st]-at[st])/(vt[st]-at[st]))*(vt.t-at.t);Et>=0&&Et<=1&&Vt>=0&&Vt<=1&&(ie?Be++:Be.push({x:gt.x,y:gt.y,t1:Et,t2:Vt}))}}return Be},Se=function(Z,J,ie){Z=path2Curve(Z),J=path2Curve(J);for(var me,Oe,Ce,Ne,Y,$,Ie,_e,Ae,Be,ze=ie?0:[],Ye=0,Ve=Z.length;Ye<Ve;Ye++){var qe=Z[Ye];if(qe[0]==="M")me=Y=qe[1],Oe=$=qe[2];else{qe[0]==="C"?(Ae=[me,Oe].concat(qe.slice(1)),me=Ae[6],Oe=Ae[7]):(Ae=[me,Oe,me,Oe,Y,$,Y,$],me=Y,Oe=$);for(var je=0,at=J.length;je<at;je++){var vt=J[je];if(vt[0]==="M")Ce=Ie=vt[1],Ne=_e=vt[2];else{vt[0]==="C"?(Be=[Ce,Ne].concat(vt.slice(1)),Ce=Be[6],Ne=Be[7]):(Be=[Ce,Ne,Ce,Ne,Ie,_e,Ie,_e],Ce=Ie,Ne=_e);var ct=we(Ae,Be,ie);if(ie)ze+=ct;else{for(var st=0,gt=ct.length;st<gt;st++)ct[st].segment1=Ye,ct[st].segment2=je,ct[st].bez1=Ae,ct[st].bez2=Be;ze=ze.concat(ct)}}}}}return ze};function Le(Z,J){return Se(Z,J)}function q(Z){return Math.sqrt(Z[0]*Z[0]+Z[1]*Z[1])}function se(Z,J){return q(Z)*q(J)?(Z[0]*J[0]+Z[1]*J[1])/(q(Z)*q(J)):1}function ne(Z,J){return(Z[0]*J[1]<Z[1]*J[0]?-1:1)*Math.acos(se(Z,J))}function pe(Z,J){return Z[0]===J[0]&&Z[1]===J[1]}function Me(Z,J){var ie=J[1],me=J[2],Oe=(0,T.mod)((0,T.toRadian)(J[3]),Math.PI*2),Ce=J[4],Ne=J[5],Y=Z[0],$=Z[1],Ie=J[6],_e=J[7],Ae=Math.cos(Oe)*(Y-Ie)/2+Math.sin(Oe)*($-_e)/2,Be=-1*Math.sin(Oe)*(Y-Ie)/2+Math.cos(Oe)*($-_e)/2,ze=Ae*Ae/(ie*ie)+Be*Be/(me*me);ze>1&&(ie*=Math.sqrt(ze),me*=Math.sqrt(ze));var Ye=ie*ie*(Be*Be)+me*me*(Ae*Ae),Ve=Ye?Math.sqrt((ie*ie*(me*me)-Ye)/Ye):1;Ce===Ne&&(Ve*=-1),isNaN(Ve)&&(Ve=0);var qe=me?Ve*ie*Be/me:0,je=ie?Ve*-me*Ae/ie:0,at=(Y+Ie)/2+Math.cos(Oe)*qe-Math.sin(Oe)*je,vt=($+_e)/2+Math.sin(Oe)*qe+Math.cos(Oe)*je,ct=[(Ae-qe)/ie,(Be-je)/me],st=[(-1*Ae-qe)/ie,(-1*Be-je)/me],gt=ne([1,0],ct),Et=ne(ct,st);return se(ct,st)<=-1&&(Et=Math.PI),se(ct,st)>=1&&(Et=0),Ne===0&&Et>0&&(Et=Et-2*Math.PI),Ne===1&&Et<0&&(Et=Et+2*Math.PI),{cx:at,cy:vt,rx:pe(Z,[Ie,_e])?0:ie,ry:pe(Z,[Ie,_e])?0:me,startAngle:gt,endAngle:gt+Et,xRotation:Oe,arcFlag:Ce,sweepFlag:Ne}}function he(Z,J){return[J[0]+(J[0]-Z[0]),J[1]+(J[1]-Z[1])]}function De(Z){Z=E(Z);for(var J=[],ie=null,me=null,Oe=null,Ce=0,Ne=Z.length,Y=0;Y<Ne;Y++){var $=Z[Y];me=Z[Y+1];var Ie=$[0],_e={command:Ie,prePoint:ie,params:$,startTangent:null,endTangent:null};switch(Ie){case"M":Oe=[$[1],$[2]],Ce=Y;break;case"A":var Ae=Me(ie,$);_e.arcParams=Ae;break;default:break}if(Ie==="Z")ie=Oe,me=Z[Ce+1];else{var Be=$.length;ie=[$[Be-2],$[Be-1]]}me&&me[0]==="Z"&&(me=Z[Ce],J[Ce]&&(J[Ce].prePoint=ie)),_e.currentPoint=ie,J[Ce]&&pe(ie,J[Ce].currentPoint)&&(J[Ce].prePoint=_e.prePoint);var ze=me?[me[me.length-2],me[me.length-1]]:null;_e.nextPoint=ze;var Ye=_e.prePoint;if(["L","H","V"].includes(Ie))_e.startTangent=[Ye[0]-ie[0],Ye[1]-ie[1]],_e.endTangent=[ie[0]-Ye[0],ie[1]-Ye[1]];else if(Ie==="Q"){var Ve=[$[1],$[2]];_e.startTangent=[Ye[0]-Ve[0],Ye[1]-Ve[1]],_e.endTangent=[ie[0]-Ve[0],ie[1]-Ve[1]]}else if(Ie==="T"){var qe=J[Y-1],Ve=he(qe.currentPoint,Ye);qe.command==="Q"?(_e.command="Q",_e.startTangent=[Ye[0]-Ve[0],Ye[1]-Ve[1]],_e.endTangent=[ie[0]-Ve[0],ie[1]-Ve[1]]):(_e.command="TL",_e.startTangent=[Ye[0]-ie[0],Ye[1]-ie[1]],_e.endTangent=[ie[0]-Ye[0],ie[1]-Ye[1]])}else if(Ie==="C"){var je=[$[1],$[2]],at=[$[3],$[4]];_e.startTangent=[Ye[0]-je[0],Ye[1]-je[1]],_e.endTangent=[ie[0]-at[0],ie[1]-at[1]],_e.startTangent[0]===0&&_e.startTangent[1]===0&&(_e.startTangent=[je[0]-at[0],je[1]-at[1]]),_e.endTangent[0]===0&&_e.endTangent[1]===0&&(_e.endTangent=[at[0]-je[0],at[1]-je[1]])}else if(Ie==="S"){var qe=J[Y-1],je=he(qe.currentPoint,Ye),at=[$[1],$[2]];qe.command==="C"?(_e.command="C",_e.startTangent=[Ye[0]-je[0],Ye[1]-je[1]],_e.endTangent=[ie[0]-at[0],ie[1]-at[1]]):(_e.command="SQ",_e.startTangent=[Ye[0]-at[0],Ye[1]-at[1]],_e.endTangent=[ie[0]-at[0],ie[1]-at[1]])}else if(Ie==="A"){var vt=.001,ct=_e.arcParams||{},st=ct.cx,gt=st===void 0?0:st,Et=ct.cy,Vt=Et===void 0?0:Et,qt=ct.rx,Kt=qt===void 0?0:qt,$t=ct.ry,or=$t===void 0?0:$t,Zt=ct.sweepFlag,Rt=Zt===void 0?0:Zt,cr=ct.startAngle,er=cr===void 0?0:cr,fr=ct.endAngle,W=fr===void 0?0:fr;Rt===0&&(vt*=-1);var H=Kt*Math.cos(er-vt)+gt,X=or*Math.sin(er-vt)+Vt;_e.startTangent=[H-Oe[0],X-Oe[1]];var le=Kt*Math.cos(er+W+vt)+gt,ye=or*Math.sin(er+W-vt)+Vt;_e.endTangent=[Ye[0]-le,Ye[1]-ye]}J.push(_e)}return J}var Pe=1e-6;function He(Z){return Math.abs(Z)<Pe?0:Z<0?-1:1}function Ze(Z,J,ie){return(ie[0]-Z[0])*(J[1]-Z[1])===(J[0]-Z[0])*(ie[1]-Z[1])&&Math.min(Z[0],J[0])<=ie[0]&&ie[0]<=Math.max(Z[0],J[0])&&Math.min(Z[1],J[1])<=ie[1]&&ie[1]<=Math.max(Z[1],J[1])}function Ue(Z,J,ie){var me=!1,Oe=Z.length;if(Oe<=2)return!1;for(var Ce=0;Ce<Oe;Ce++){var Ne=Z[Ce],Y=Z[(Ce+1)%Oe];if(Ze(Ne,Y,[J,ie]))return!0;He(Ne[1]-ie)>0!=He(Y[1]-ie)>0&&He(J-(ie-Ne[1])*(Ne[0]-Y[0])/(Ne[1]-Y[1])-Ne[0])<0&&(me=!me)}return me}var tt=function(Z,J,ie){return Z>=J&&Z<=ie};function pt(Z,J,ie,me){var Oe=.001,Ce={x:ie.x-Z.x,y:ie.y-Z.y},Ne={x:J.x-Z.x,y:J.y-Z.y},Y={x:me.x-ie.x,y:me.y-ie.y},$=Ne.x*Y.y-Ne.y*Y.x,Ie=$*$,_e=Ne.x*Ne.x+Ne.y*Ne.y,Ae=Y.x*Y.x+Y.y*Y.y,Be=null;if(Ie>Oe*_e*Ae){var ze=(Ce.x*Y.y-Ce.y*Y.x)/$,Ye=(Ce.x*Ne.y-Ce.y*Ne.x)/$;tt(ze,0,1)&&tt(Ye,0,1)&&(Be={x:Z.x+ze*Ne.x,y:Z.y+ze*Ne.y})}return Be}function Mt(Z){for(var J=[],ie=Z.length,me=0;me<ie-1;me++){var Oe=Z[me],Ce=Z[me+1];J.push({from:{x:Oe[0],y:Oe[1]},to:{x:Ce[0],y:Ce[1]}})}if(J.length>1){var Ne=Z[0],Y=Z[ie-1];J.push({from:{x:Y[0],y:Y[1]},to:{x:Ne[0],y:Ne[1]}})}return J}function jt(Z,J){var ie=!1;return(0,T.each)(Z,function(me){if(pt(me.from,me.to,J.from,J.to))return ie=!0,!1}),ie}function Ct(Z){var J=Z.map(function(me){return me[0]}),ie=Z.map(function(me){return me[1]});return{minX:Math.min.apply(null,J),maxX:Math.max.apply(null,J),minY:Math.min.apply(null,ie),maxY:Math.max.apply(null,ie)}}function Xt(Z,J){return!(J.minX>Z.maxX||J.maxX<Z.minX||J.minY>Z.maxY||J.maxY<Z.minY)}function Jt(Z,J){if(Z.length<2||J.length<2)return!1;var ie=Ct(Z),me=Ct(J);if(!Xt(ie,me))return!1;var Oe=!1;if((0,T.each)(J,function($){if(Ue(Z,$[0],$[1]))return Oe=!0,!1}),Oe||((0,T.each)(Z,function($){if(Ue(J,$[0],$[1]))return Oe=!0,!1}),Oe))return!0;var Ce=Mt(Z),Ne=Mt(J),Y=!1;return(0,T.each)(Ne,function($){if(jt(Ce,$))return Y=!0,!1}),Y}},53487:function(Je,w,G){"use strict";G.d(w,{tr:function(){return E}});var T=G(83788),C=`	
\v\f\r \xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029`,x=new RegExp("([a-z])["+C+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+C+"]*,?["+C+"]*)+)","ig"),M=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+C+"]*,?["+C+"]*","ig");function E(P){if(!P)return null;if((0,T.Z)(P))return P;var N={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},U=[];return String(P).replace(x,function(B,ue,ae){var de=[],ve=ue.toLowerCase();if(ae.replace(M,function(we,Se){Se&&de.push(+Se)}),ve==="m"&&de.length>2&&(U.push([ue].concat(de.splice(0,2))),ve="l",ue=ue==="m"?"l":"L"),ve==="o"&&de.length===1&&U.push([ue,de[0]]),ve==="r")U.push([ue].concat(de));else for(;de.length>=N[ve]&&(U.push([ue].concat(de.splice(0,N[ve]))),!!N[ve]););return""}),U}var p=/[a-z]/;function u(P,N){return[N[0]+(N[0]-P[0]),N[1]+(N[1]-P[1])]}function t(P){var N=parsePathString(P);if(!N||!N.length)return[["M",0,0]];for(var U=!1,B=0;B<N.length;B++){var ue=N[B][0];if(p.test(ue)||["V","H","T","S"].indexOf(ue)>=0){U=!0;break}}if(!U)return N;var ae=[],de=0,ve=0,we=0,Se=0,Le=0,q,se,ne=N[0];(ne[0]==="M"||ne[0]==="m")&&(de=+ne[1],ve=+ne[2],we=de,Se=ve,Le++,ae[0]=["M",de,ve]);for(var B=Le,pe=N.length;B<pe;B++){var Me=N[B],he=ae[B-1],De=[],ue=Me[0],Pe=ue.toUpperCase();if(ue!==Pe)switch(De[0]=Pe,Pe){case"A":De[1]=Me[1],De[2]=Me[2],De[3]=Me[3],De[4]=Me[4],De[5]=Me[5],De[6]=+Me[6]+de,De[7]=+Me[7]+ve;break;case"V":De[1]=+Me[1]+ve;break;case"H":De[1]=+Me[1]+de;break;case"M":we=+Me[1]+de,Se=+Me[2]+ve,De[1]=we,De[2]=Se;break;default:for(var He=1,Ze=Me.length;He<Ze;He++)De[He]=+Me[He]+(He%2?de:ve)}else De=N[B];switch(Pe){case"Z":de=+we,ve=+Se;break;case"H":de=De[1],De=["L",de,ve];break;case"V":ve=De[1],De=["L",de,ve];break;case"T":de=De[1],ve=De[2];var Ue=u([he[1],he[2]],[he[3],he[4]]);De=["Q",Ue[0],Ue[1],de,ve];break;case"S":de=De[De.length-2],ve=De[De.length-1];var tt=he.length,pt=u([he[tt-4],he[tt-3]],[he[tt-2],he[tt-1]]);De=["C",pt[0],pt[1],De[1],De[2],de,ve];break;case"M":we=De[De.length-2],Se=De[De.length-1];break;default:de=De[De.length-2],ve=De[De.length-1]}ae.push(De)}return ae}var n=Math.PI*2,r=function(P,N,U,B,ue,ae,de){var ve=P.x,we=P.y;ve*=N,we*=U;var Se=B*ve-ue*we,Le=ue*ve+B*we;return{x:Se+ae,y:Le+de}},e=function(P,N){var U=N===1.5707963267948966?.551915024494:N===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(N/4),B=Math.cos(P),ue=Math.sin(P),ae=Math.cos(P+N),de=Math.sin(P+N);return[{x:B-ue*U,y:ue+B*U},{x:ae+de*U,y:de-ae*U},{x:ae,y:de}]},o=function(P,N,U,B){var ue=P*B-N*U<0?-1:1,ae=P*U+N*B;return ae>1&&(ae=1),ae<-1&&(ae=-1),ue*Math.acos(ae)},a=function(P,N,U,B,ue,ae,de,ve,we,Se,Le,q){var se=Math.pow(ue,2),ne=Math.pow(ae,2),pe=Math.pow(Le,2),Me=Math.pow(q,2),he=se*ne-se*Me-ne*pe;he<0&&(he=0),he/=se*Me+ne*pe,he=Math.sqrt(he)*(de===ve?-1:1);var De=he*ue/ae*q,Pe=he*-ae/ue*Le,He=Se*De-we*Pe+(P+U)/2,Ze=we*De+Se*Pe+(N+B)/2,Ue=(Le-De)/ue,tt=(q-Pe)/ae,pt=(-Le-De)/ue,Mt=(-q-Pe)/ae,jt=o(1,0,Ue,tt),Ct=o(Ue,tt,pt,Mt);return ve===0&&Ct>0&&(Ct-=n),ve===1&&Ct<0&&(Ct+=n),[He,Ze,jt,Ct]},i=function(P){var N=P.px,U=P.py,B=P.cx,ue=P.cy,ae=P.rx,de=P.ry,ve=P.xAxisRotation,we=ve===void 0?0:ve,Se=P.largeArcFlag,Le=Se===void 0?0:Se,q=P.sweepFlag,se=q===void 0?0:q,ne=[];if(ae===0||de===0)return[{x1:0,y1:0,x2:0,y2:0,x:B,y:ue}];var pe=Math.sin(we*n/360),Me=Math.cos(we*n/360),he=Me*(N-B)/2+pe*(U-ue)/2,De=-pe*(N-B)/2+Me*(U-ue)/2;if(he===0&&De===0)return[{x1:0,y1:0,x2:0,y2:0,x:B,y:ue}];ae=Math.abs(ae),de=Math.abs(de);var Pe=Math.pow(he,2)/Math.pow(ae,2)+Math.pow(De,2)/Math.pow(de,2);Pe>1&&(ae*=Math.sqrt(Pe),de*=Math.sqrt(Pe));var He=a(N,U,B,ue,ae,de,Le,se,pe,Me,he,De),Ze=He[0],Ue=He[1],tt=He[2],pt=He[3],Mt=Math.abs(pt)/(n/4);Math.abs(1-Mt)<1e-7&&(Mt=1);var jt=Math.max(Math.ceil(Mt),1);pt/=jt;for(var Ct=0;Ct<jt;Ct++)ne.push(e(tt,pt)),tt+=pt;return ne.map(function(Xt){var Jt=r(Xt[0],ae,de,Me,pe,Ze,Ue),Z=Jt.x,J=Jt.y,ie=r(Xt[1],ae,de,Me,pe,Ze,Ue),me=ie.x,Oe=ie.y,Ce=r(Xt[2],ae,de,Me,pe,Ze,Ue),Ne=Ce.x,Y=Ce.y;return{x1:Z,y1:J,x2:me,y2:Oe,x:Ne,y:Y}})};function f(P,N,U,B,ue,ae,de,ve,we){var Se=i({px:P,py:N,cx:ve,cy:we,rx:U,ry:B,xAxisRotation:ue,largeArcFlag:ae,sweepFlag:de});return Se.reduce(function(Le,q){var se=q.x1,ne=q.y1,pe=q.x2,Me=q.y2,he=q.x,De=q.y;return Le.push(se,ne,pe,Me,he,De),Le},[])}function s(P,N){"TQ".indexOf(P[0])<0&&(N.qx=null,N.qy=null);var U=P.slice(1),B=U[0],ue=U[1];switch(P[0]){case"M":return N.x=B,N.y=ue,P;case"A":return["C"].concat(arcToCubic.apply(0,[N.x1,N.y1].concat(P.slice(1))));case"Q":return N.qx=B,N.qy=ue,["C"].concat(quadToCubic.apply(0,[N.x1,N.y1].concat(P.slice(1))));case"L":return["C"].concat(lineToCubic(N.x1,N.y1,P[1],P[2]));case"H":return["C"].concat(lineToCubic(N.x1,N.y1,P[1],N.y1));case"V":return["C"].concat(lineToCubic(N.x1,N.y1,N.x1,P[1]));case"Z":return["C"].concat(lineToCubic(N.x1,N.y1,N.x,N.y));default:}return P}function l(P,N){N===void 0&&(N=!1);for(var U=path2Absolute(P),B={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null},ue=[],ae="",de=U.length,ve,we,Se=[],Le=0;Le<de;Le+=1)U[Le]&&(ae=U[Le][0]),ue[Le]=ae,U[Le]=segmentToCubic(U[Le],B),m(U,ue,Le),de=U.length,ae==="Z"&&Se.push(Le),ve=U[Le],we=ve.length,B.x1=+ve[we-2],B.y1=+ve[we-1],B.x2=+ve[we-4]||B.x1,B.y2=+ve[we-3]||B.y1;return N?[U,Se]:U}function m(P,N,U){if(P[U].length>7){P[U].shift();for(var B=P[U],ue=U;B.length;)N[U]="A",P.splice(ue+=1,0,["C"].concat(B.splice(0,6)));P.splice(U,1)}}var h=function(P,N,U,B,ue){var ae=-3*N+9*U-9*B+3*ue,de=P*ae+6*N-12*U+6*B;return P*de-3*N+3*U},d=function(P,N,U,B,ue,ae,de,ve,we){we===null&&(we=1),we=we>1?1:we<0?0:we;for(var Se=we/2,Le=12,q=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],se=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],ne=0,pe=0;pe<Le;pe++){var Me=Se*q[pe]+Se,he=h(Me,P,U,ue,de),De=h(Me,N,B,ae,ve),Pe=he*he+De*De;ne+=se[pe]*Math.sqrt(Pe)}return Se*ne},v=function(P,N,U,B,ue,ae,de,ve){for(var we=[],Se=[[],[]],Le,q,se,ne,pe=0;pe<2;++pe){if(pe===0?(q=6*P-12*U+6*ue,Le=-3*P+9*U-9*ue+3*de,se=3*U-3*P):(q=6*N-12*B+6*ae,Le=-3*N+9*B-9*ae+3*ve,se=3*B-3*N),Math.abs(Le)<1e-12){if(Math.abs(q)<1e-12)continue;ne=-se/q,ne>0&&ne<1&&we.push(ne);continue}var Me=q*q-4*se*Le,he=Math.sqrt(Me);if(!(Me<0)){var De=(-q+he)/(2*Le);De>0&&De<1&&we.push(De);var Pe=(-q-he)/(2*Le);Pe>0&&Pe<1&&we.push(Pe)}}for(var He=we.length,Ze=He,Ue;He--;)ne=we[He],Ue=1-ne,Se[0][He]=Ue*Ue*Ue*P+3*Ue*Ue*ne*U+3*Ue*ne*ne*ue+ne*ne*ne*de,Se[1][He]=Ue*Ue*Ue*N+3*Ue*Ue*ne*B+3*Ue*ne*ne*ae+ne*ne*ne*ve;return Se[0][Ze]=P,Se[1][Ze]=N,Se[0][Ze+1]=de,Se[1][Ze+1]=ve,Se[0].length=Se[1].length=Ze+2,{min:{x:Math.min.apply(0,Se[0]),y:Math.min.apply(0,Se[1])},max:{x:Math.max.apply(0,Se[0]),y:Math.max.apply(0,Se[1])}}},y=function(P,N,U,B,ue,ae,de,ve){if(!(Math.max(P,U)<Math.min(ue,de)||Math.min(P,U)>Math.max(ue,de)||Math.max(N,B)<Math.min(ae,ve)||Math.min(N,B)>Math.max(ae,ve))){var we=(P*B-N*U)*(ue-de)-(P-U)*(ue*ve-ae*de),Se=(P*B-N*U)*(ae-ve)-(N-B)*(ue*ve-ae*de),Le=(P-U)*(ae-ve)-(N-B)*(ue-de);if(Le){var q=we/Le,se=Se/Le,ne=+q.toFixed(2),pe=+se.toFixed(2);if(!(ne<+Math.min(P,U).toFixed(2)||ne>+Math.max(P,U).toFixed(2)||ne<+Math.min(ue,de).toFixed(2)||ne>+Math.max(ue,de).toFixed(2)||pe<+Math.min(N,B).toFixed(2)||pe>+Math.max(N,B).toFixed(2)||pe<+Math.min(ae,ve).toFixed(2)||pe>+Math.max(ae,ve).toFixed(2)))return{x:q,y:se}}}},_=function(P,N,U){return N>=P.x&&N<=P.x+P.width&&U>=P.y&&U<=P.y+P.height},D=function(P,N,U,B){return P===null&&(P=N=U=B=0),N===null&&(N=P.y,U=P.width,B=P.height,P=P.x),{x:P,y:N,width:U,w:U,height:B,h:B,x2:P+U,y2:N+B,cx:P+U/2,cy:N+B/2,r1:Math.min(U,B)/2,r2:Math.max(U,B)/2,r0:Math.sqrt(U*U+B*B)/2,path:rectPath(P,N,U,B),vb:[P,N,U,B].join(" ")}},b=function(P,N){return P=D(P),N=D(N),_(N,P.x,P.y)||_(N,P.x2,P.y)||_(N,P.x,P.y2)||_(N,P.x2,P.y2)||_(P,N.x,N.y)||_(P,N.x2,N.y)||_(P,N.x,N.y2)||_(P,N.x2,N.y2)||(P.x<N.x2&&P.x>N.x||N.x<P.x2&&N.x>P.x)&&(P.y<N.y2&&P.y>N.y||N.y<P.y2&&N.y>P.y)},S=function(P,N,U,B,ue,ae,de,ve){isArray(P)||(P=[P,N,U,B,ue,ae,de,ve]);var we=v.apply(null,P);return D(we.min.x,we.min.y,we.max.x-we.min.x,we.max.y-we.min.y)},k=function(P,N,U,B,ue,ae,de,ve,we){var Se=1-we,Le=Math.pow(Se,3),q=Math.pow(Se,2),se=we*we,ne=se*we,pe=Le*P+q*3*we*U+Se*3*we*we*ue+ne*de,Me=Le*N+q*3*we*B+Se*3*we*we*ae+ne*ve,he=P+2*we*(U-P)+se*(ue-2*U+P),De=N+2*we*(B-N)+se*(ae-2*B+N),Pe=U+2*we*(ue-U)+se*(de-2*ue+U),He=B+2*we*(ae-B)+se*(ve-2*ae+B),Ze=Se*P+we*U,Ue=Se*N+we*B,tt=Se*ue+we*de,pt=Se*ae+we*ve,Mt=90-Math.atan2(he-Pe,De-He)*180/Math.PI;return{x:pe,y:Me,m:{x:he,y:De},n:{x:Pe,y:He},start:{x:Ze,y:Ue},end:{x:tt,y:pt},alpha:Mt}},A=function(P,N,U){var B=S(P),ue=S(N);if(!b(B,ue))return U?0:[];for(var ae=d.apply(0,P),de=d.apply(0,N),ve=~~(ae/8),we=~~(de/8),Se=[],Le=[],q={},se=U?0:[],ne=0;ne<ve+1;ne++){var pe=k.apply(0,P.concat(ne/ve));Se.push({x:pe.x,y:pe.y,t:ne/ve})}for(var ne=0;ne<we+1;ne++){var pe=k.apply(0,N.concat(ne/we));Le.push({x:pe.x,y:pe.y,t:ne/we})}for(var ne=0;ne<ve;ne++)for(var Me=0;Me<we;Me++){var he=Se[ne],De=Se[ne+1],Pe=Le[Me],He=Le[Me+1],Ze=Math.abs(De.x-he.x)<.001?"y":"x",Ue=Math.abs(He.x-Pe.x)<.001?"y":"x",tt=y(he.x,he.y,De.x,De.y,Pe.x,Pe.y,He.x,He.y);if(tt){if(q[tt.x.toFixed(4)]===tt.y.toFixed(4))continue;q[tt.x.toFixed(4)]=tt.y.toFixed(4);var pt=he.t+Math.abs((tt[Ze]-he[Ze])/(De[Ze]-he[Ze]))*(De.t-he.t),Mt=Pe.t+Math.abs((tt[Ue]-Pe[Ue])/(He[Ue]-Pe[Ue]))*(He.t-Pe.t);pt>=0&&pt<=1&&Mt>=0&&Mt<=1&&(U?se++:se.push({x:tt.x,y:tt.y,t1:pt,t2:Mt}))}}return se},K=function(P,N,U){P=path2Curve(P),N=path2Curve(N);for(var B,ue,ae,de,ve,we,Se,Le,q,se,ne=U?0:[],pe=0,Me=P.length;pe<Me;pe++){var he=P[pe];if(he[0]==="M")B=ve=he[1],ue=we=he[2];else{he[0]==="C"?(q=[B,ue].concat(he.slice(1)),B=q[6],ue=q[7]):(q=[B,ue,B,ue,ve,we,ve,we],B=ve,ue=we);for(var De=0,Pe=N.length;De<Pe;De++){var He=N[De];if(He[0]==="M")ae=Se=He[1],de=Le=He[2];else{He[0]==="C"?(se=[ae,de].concat(He.slice(1)),ae=se[6],de=se[7]):(se=[ae,de,ae,de,Se,Le,Se,Le],ae=Se,de=Le);var Ze=A(q,se,U);if(U)ne+=Ze;else{for(var Ue=0,tt=Ze.length;Ue<tt;Ue++)Ze[Ue].segment1=pe,Ze[Ue].segment2=De,Ze[Ue].bez1=q,Ze[Ue].bez2=se;ne=ne.concat(Ze)}}}}}return ne};function R(P,N){return K(P,N)}},31091:function(Je,w,G){"use strict";G.d(w,{WD:function(){return n},bJ:function(){return A},yZ:function(){return pe}});var T=G(98667),C={};function x(W){return C[W]}function M(W,H){C[W]=H}var E=function(){function W(H){this.type="base",this.isCategory=!1,this.isLinear=!1,this.isContinuous=!1,this.isIdentity=!1,this.values=[],this.range=[0,1],this.ticks=[],this.__cfg__=H,this.initCfg(),this.init()}return W.prototype.translate=function(H){return H},W.prototype.change=function(H){(0,T.assign)(this.__cfg__,H),this.init()},W.prototype.clone=function(){return this.constructor(this.__cfg__)},W.prototype.getTicks=function(){var H=this;return(0,T.map)(this.ticks,function(X,le){return(0,T.isObject)(X)?X:{text:H.getText(X,le),tickValue:X,value:H.scale(X)}})},W.prototype.getText=function(H,X){var le=this.formatter,ye=le?le(H,X):H;return(0,T.isNil)(ye)||!(0,T.isFunction)(ye.toString)?"":ye.toString()},W.prototype.getConfig=function(H){return this.__cfg__[H]},W.prototype.init=function(){(0,T.assign)(this,this.__cfg__),this.setDomain(),(0,T.isEmpty)(this.getConfig("ticks"))&&(this.ticks=this.calculateTicks())},W.prototype.initCfg=function(){},W.prototype.setDomain=function(){},W.prototype.calculateTicks=function(){var H=this.tickMethod,X=[];if((0,T.isString)(H)){var le=x(H);if(!le)throw new Error("There is no method to to calculate ticks!");X=le(this)}else(0,T.isFunction)(H)&&(X=H(this));return X},W.prototype.rangeMin=function(){return this.range[0]},W.prototype.rangeMax=function(){return this.range[1]},W.prototype.calcPercent=function(H,X,le){return(0,T.isNumber)(H)?(H-X)/(le-X):NaN},W.prototype.calcValue=function(H,X,le){return X+H*(le-X)},W}(),p=E,u=G(92336),t=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="cat",X.isCategory=!0,X}return H.prototype.buildIndexMap=function(){if(!this.translateIndexMap){this.translateIndexMap=new Map;for(var X=0;X<this.values.length;X++)this.translateIndexMap.set(this.values[X],X)}},H.prototype.translate=function(X){this.buildIndexMap();var le=this.translateIndexMap.get(X);return le===void 0&&(le=(0,T.isNumber)(X)?X:NaN),le},H.prototype.scale=function(X){var le=this.translate(X),ye=this.calcPercent(le,this.min,this.max);return this.calcValue(ye,this.rangeMin(),this.rangeMax())},H.prototype.invert=function(X){var le=this.max-this.min,ye=this.calcPercent(X,this.rangeMin(),this.rangeMax()),xe=Math.round(le*ye)+this.min;return xe<this.min||xe>this.max?NaN:this.values[xe]},H.prototype.getText=function(X){for(var le=[],ye=1;ye<arguments.length;ye++)le[ye-1]=arguments[ye];var xe=X;return(0,T.isNumber)(X)&&!this.values.includes(X)&&(xe=this.values[xe]),W.prototype.getText.apply(this,(0,u.__spreadArrays)([xe],le))},H.prototype.initCfg=function(){this.tickMethod="cat"},H.prototype.setDomain=function(){if((0,T.isNil)(this.getConfig("min"))&&(this.min=0),(0,T.isNil)(this.getConfig("max"))){var X=this.values.length;this.max=X>1?X-1:X}this.translateIndexMap&&(this.translateIndexMap=void 0)},H}(p),n=t,r=G(79129);function e(W){return function(H,X,le,ye){for(var xe=(0,T.isNil)(le)?0:le,Fe=(0,T.isNil)(ye)?H.length:ye;xe<Fe;){var We=xe+Fe>>>1;W(H[We])>X?Fe=We:xe=We+1}return xe}}var o="format";function a(W,H){var X=r[o]||r.default[o];return X(W,H)}function i(W){return(0,T.isString)(W)&&(W.indexOf("T")>0?W=new Date(W).getTime():W=new Date(W.replace(/-/gi,"/")).getTime()),(0,T.isDate)(W)&&(W=W.getTime()),W}var f=1e3,s=60*f,l=60*s,m=24*l,h=m*31,d=m*365,v=[["HH:mm:ss",f],["HH:mm:ss",f*10],["HH:mm:ss",f*30],["HH:mm",s],["HH:mm",s*10],["HH:mm",s*30],["HH",l],["HH",l*6],["HH",l*12],["YYYY-MM-DD",m],["YYYY-MM-DD",m*4],["YYYY-WW",m*7],["YYYY-MM",h],["YYYY-MM",h*4],["YYYY-MM",h*6],["YYYY",m*380]];function y(W,H,X){var le=(H-W)/X,ye=e(function(Fe){return Fe[1]})(v,le)-1,xe=v[ye];return ye<0?xe=v[0]:ye>=v.length&&(xe=(0,T.last)(v)),xe}var _=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="timeCat",X}return H.prototype.translate=function(X){X=i(X);var le=this.values.indexOf(X);return le===-1&&((0,T.isNumber)(X)&&X<this.values.length?le=X:le=NaN),le},H.prototype.getText=function(X,le){var ye=this.translate(X);if(ye>-1){var xe=this.values[ye],Fe=this.formatter;return xe=Fe?Fe(xe,le):a(xe,this.mask),xe}return X},H.prototype.initCfg=function(){this.tickMethod="time-cat",this.mask="YYYY-MM-DD",this.tickCount=7},H.prototype.setDomain=function(){var X=this.values;(0,T.each)(X,function(le,ye){X[ye]=i(le)}),X.sort(function(le,ye){return le-ye}),W.prototype.setDomain.call(this)},H}(n),D=_,b=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.isContinuous=!0,X}return H.prototype.scale=function(X){if((0,T.isNil)(X))return NaN;var le=this.rangeMin(),ye=this.rangeMax(),xe=this.max,Fe=this.min;if(xe===Fe)return le;var We=this.getScalePercent(X);return le+We*(ye-le)},H.prototype.init=function(){W.prototype.init.call(this);var X=this.ticks,le=(0,T.head)(X),ye=(0,T.last)(X);le<this.min&&(this.min=le),ye>this.max&&(this.max=ye),(0,T.isNil)(this.minLimit)||(this.min=le),(0,T.isNil)(this.maxLimit)||(this.max=ye)},H.prototype.setDomain=function(){var X=(0,T.getRange)(this.values),le=X.min,ye=X.max;(0,T.isNil)(this.min)&&(this.min=le),(0,T.isNil)(this.max)&&(this.max=ye),this.min>this.max&&(this.min=le,this.max=ye)},H.prototype.calculateTicks=function(){var X=this,le=W.prototype.calculateTicks.call(this);return this.nice||(le=(0,T.filter)(le,function(ye){return ye>=X.min&&ye<=X.max})),le},H.prototype.getScalePercent=function(X){var le=this.max,ye=this.min;return(X-ye)/(le-ye)},H.prototype.getInvertPercent=function(X){return(X-this.rangeMin())/(this.rangeMax()-this.rangeMin())},H}(p),S=b,k=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="linear",X.isLinear=!0,X}return H.prototype.invert=function(X){var le=this.getInvertPercent(X);return this.min+le*(this.max-this.min)},H.prototype.initCfg=function(){this.tickMethod="wilkinson-extended",this.nice=!1},H}(S),A=k;function K(W,H){var X=Math.E,le;return H>=0?le=Math.pow(X,Math.log(H)/W):le=Math.pow(X,Math.log(-H)/W)*-1,le}function R(W,H){return W===1?1:Math.log(H)/Math.log(W)}function P(W,H,X){(0,T.isNil)(X)&&(X=Math.max.apply(null,W));var le=X;return(0,T.each)(W,function(ye){ye>0&&ye<le&&(le=ye)}),le===X&&(le=X/H),le>1&&(le=1),le}function N(W){var H=W.toString().split(/[eE]/),X=(H[0].split(".")[1]||"").length-+(H[1]||0);return X>0?X:0}function U(W,H){var X=N(W),le=N(H),ye=Math.pow(10,Math.max(X,le));return(W*ye+H*ye)/ye}var B=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="log",X}return H.prototype.invert=function(X){var le=this.base,ye=R(le,this.max),xe=this.rangeMin(),Fe=this.rangeMax()-xe,We,it=this.positiveMin;if(it){if(X===0)return 0;We=R(le,it/le);var Qe=1/(ye-We)*Fe;if(X<Qe)return X/Qe*it}else We=R(le,this.min);var rt=(X-xe)/Fe,kt=rt*(ye-We)+We;return Math.pow(le,kt)},H.prototype.initCfg=function(){this.tickMethod="log",this.base=10,this.tickCount=6,this.nice=!0},H.prototype.setDomain=function(){W.prototype.setDomain.call(this);var X=this.min;if(X<0)throw new Error("When you use log scale, the minimum value must be greater than zero!");X===0&&(this.positiveMin=P(this.values,this.base,this.max))},H.prototype.getScalePercent=function(X){var le=this.max,ye=this.min;if(le===ye||X<=0)return 0;var xe=this.base,Fe=this.positiveMin;Fe&&(ye=Fe*1/xe);var We;return X<Fe?We=X/Fe/(R(xe,le)-R(xe,ye)):We=(R(xe,X)-R(xe,ye))/(R(xe,le)-R(xe,ye)),We},H}(S),ue=B,ae=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="pow",X}return H.prototype.invert=function(X){var le=this.getInvertPercent(X),ye=this.exponent,xe=K(ye,this.max),Fe=K(ye,this.min),We=le*(xe-Fe)+Fe,it=We>=0?1:-1;return Math.pow(We,ye)*it},H.prototype.initCfg=function(){this.tickMethod="pow",this.exponent=2,this.tickCount=5,this.nice=!0},H.prototype.getScalePercent=function(X){var le=this.max,ye=this.min;if(le===ye)return 0;var xe=this.exponent,Fe=(K(xe,X)-K(xe,ye))/(K(xe,le)-K(xe,ye));return Fe},H}(S),de=ae,ve=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="time",X}return H.prototype.getText=function(X,le){var ye=this.translate(X),xe=this.formatter;return xe?xe(ye,le):a(ye,this.mask)},H.prototype.scale=function(X){var le=X;return((0,T.isString)(le)||(0,T.isDate)(le))&&(le=this.translate(le)),W.prototype.scale.call(this,le)},H.prototype.translate=function(X){return i(X)},H.prototype.initCfg=function(){this.tickMethod="time-pretty",this.mask="YYYY-MM-DD",this.tickCount=7,this.nice=!1},H.prototype.setDomain=function(){var X=this.values,le=this.getConfig("min"),ye=this.getConfig("max");if((!(0,T.isNil)(le)||!(0,T.isNumber)(le))&&(this.min=this.translate(this.min)),(!(0,T.isNil)(ye)||!(0,T.isNumber)(ye))&&(this.max=this.translate(this.max)),X&&X.length){var xe=[],Fe=1/0,We=Fe,it=0;(0,T.each)(X,function(Qe){var rt=i(Qe);if(isNaN(rt))throw new TypeError("Invalid Time: "+Qe+" in time scale!");Fe>rt?(We=Fe,Fe=rt):We>rt&&(We=rt),it<rt&&(it=rt),xe.push(rt)}),X.length>1&&(this.minTickInterval=We-Fe),(0,T.isNil)(le)&&(this.min=Fe),(0,T.isNil)(ye)&&(this.max=it)}},H}(A),we=ve,Se=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="quantize",X}return H.prototype.invert=function(X){var le=this.ticks,ye=le.length,xe=this.getInvertPercent(X),Fe=Math.floor(xe*(ye-1));if(Fe>=ye-1)return(0,T.last)(le);if(Fe<0)return(0,T.head)(le);var We=le[Fe],it=le[Fe+1],Qe=Fe/(ye-1),rt=(Fe+1)/(ye-1);return We+(xe-Qe)/(rt-Qe)*(it-We)},H.prototype.initCfg=function(){this.tickMethod="r-pretty",this.tickCount=5,this.nice=!0},H.prototype.calculateTicks=function(){var X=W.prototype.calculateTicks.call(this);return this.nice||((0,T.last)(X)!==this.max&&X.push(this.max),(0,T.head)(X)!==this.min&&X.unshift(this.min)),X},H.prototype.getScalePercent=function(X){var le=this.ticks;if(X<(0,T.head)(le))return 0;if(X>(0,T.last)(le))return 1;var ye=0;return(0,T.each)(le,function(xe,Fe){if(X>=xe)ye=Fe;else return!1}),ye/(le.length-1)},H}(S),Le=Se,q=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="quantile",X}return H.prototype.initCfg=function(){this.tickMethod="quantile",this.tickCount=5,this.nice=!0},H}(Le),se=q,ne={};function pe(W){return ne[W]}function Me(W,H){if(pe(W))throw new Error("type '"+W+"' existed.");ne[W]=H}var he=function(W){(0,u.__extends)(H,W);function H(){var X=W!==null&&W.apply(this,arguments)||this;return X.type="identity",X.isIdentity=!0,X}return H.prototype.calculateTicks=function(){return this.values},H.prototype.scale=function(X){return this.values[0]!==X&&(0,T.isNumber)(X)?X:this.range[0]},H.prototype.invert=function(X){var le=this.range;return X<le[0]||X>le[1]?NaN:this.values[0]},H}(p),De=he;function Pe(W){var H=W.values,X=W.tickInterval,le=W.tickCount,ye=W.showLast;if((0,T.isNumber)(X)){var xe=(0,T.filter)(H,function(Ot,tr){return tr%X===0}),Fe=(0,T.last)(H);return ye&&(0,T.last)(xe)!==Fe&&xe.push(Fe),xe}var We=H.length,it=W.min,Qe=W.max;if((0,T.isNil)(it)&&(it=0),(0,T.isNil)(Qe)&&(Qe=H.length-1),!(0,T.isNumber)(le)||le>=We)return H.slice(it,Qe+1);if(le<=0||Qe<=0)return[];for(var rt=le===1?We:Math.floor(We/(le-1)),kt=[],Gt=it,Wt=0;Wt<le&&!(Gt>=Qe);Wt++)Gt=Math.min(it+Wt*rt,Qe),Wt===le-1&&ye?kt.push(H[Qe]):kt.push(H[Gt]);return kt}function He(W){var H=W.min,X=W.max,le=W.nice,ye=W.tickCount,xe=new Mt;return xe.domain([H,X]),le&&xe.nice(ye),xe.ticks(ye)}var Ze=5,Ue=Math.sqrt(50),tt=Math.sqrt(10),pt=Math.sqrt(2),Mt=function(){function W(){this._domain=[0,1]}return W.prototype.domain=function(H){return H?(this._domain=Array.from(H,Number),this):this._domain.slice()},W.prototype.nice=function(H){var X,le;H===void 0&&(H=Ze);var ye=this._domain.slice(),xe=0,Fe=this._domain.length-1,We=this._domain[xe],it=this._domain[Fe],Qe;return it<We&&(X=[it,We],We=X[0],it=X[1],le=[Fe,xe],xe=le[0],Fe=le[1]),Qe=Ct(We,it,H),Qe>0?(We=Math.floor(We/Qe)*Qe,it=Math.ceil(it/Qe)*Qe,Qe=Ct(We,it,H)):Qe<0&&(We=Math.ceil(We*Qe)/Qe,it=Math.floor(it*Qe)/Qe,Qe=Ct(We,it,H)),Qe>0?(ye[xe]=Math.floor(We/Qe)*Qe,ye[Fe]=Math.ceil(it/Qe)*Qe,this.domain(ye)):Qe<0&&(ye[xe]=Math.ceil(We*Qe)/Qe,ye[Fe]=Math.floor(it*Qe)/Qe,this.domain(ye)),this},W.prototype.ticks=function(H){return H===void 0&&(H=Ze),jt(this._domain[0],this._domain[this._domain.length-1],H||Ze)},W}();function jt(W,H,X){var le,ye=-1,xe,Fe,We;if(H=+H,W=+W,X=+X,W===H&&X>0)return[W];if((le=H<W)&&(xe=W,W=H,H=xe),(We=Ct(W,H,X))===0||!isFinite(We))return[];if(We>0)for(W=Math.ceil(W/We),H=Math.floor(H/We),Fe=new Array(xe=Math.ceil(H-W+1));++ye<xe;)Fe[ye]=(W+ye)*We;else for(W=Math.floor(W*We),H=Math.ceil(H*We),Fe=new Array(xe=Math.ceil(W-H+1));++ye<xe;)Fe[ye]=(W-ye)/We;return le&&Fe.reverse(),Fe}function Ct(W,H,X){var le=(H-W)/Math.max(0,X),ye=Math.floor(Math.log(le)/Math.LN10),xe=le/Math.pow(10,ye);return ye>=0?(xe>=Ue?10:xe>=tt?5:xe>=pt?2:1)*Math.pow(10,ye):-Math.pow(10,-ye)/(xe>=Ue?10:xe>=tt?5:xe>=pt?2:1)}function Xt(W,H,X){var le;return X==="ceil"?le=Math.ceil(W/H):X==="floor"?le=Math.floor(W/H):le=Math.round(W/H),le*H}function Jt(W,H,X){var le=Xt(W,X,"floor"),ye=Xt(H,X,"ceil");le=(0,T.fixedBase)(le,X),ye=(0,T.fixedBase)(ye,X);for(var xe=[],Fe=Math.max((ye-le)/(Math.pow(2,12)-1),X),We=le;We<=ye;We=We+Fe){var it=(0,T.fixedBase)(We,Fe);xe.push(it)}return{min:le,max:ye,ticks:xe}}function Z(W,H,X){var le,ye=W.minLimit,xe=W.maxLimit,Fe=W.min,We=W.max,it=W.tickCount,Qe=it===void 0?5:it,rt=(0,T.isNil)(ye)?(0,T.isNil)(H)?Fe:H:ye,kt=(0,T.isNil)(xe)?(0,T.isNil)(X)?We:X:xe;if(rt>kt&&(le=[rt,kt],kt=le[0],rt=le[1]),Qe<=2)return[rt,kt];for(var Gt=(kt-rt)/(Qe-1),Wt=[],Ot=0;Ot<Qe;Ot++)Wt.push(rt+Gt*Ot);return Wt}function J(W){var H=W.min,X=W.max,le=W.tickInterval,ye=W.minLimit,xe=W.maxLimit,Fe=He(W);return!(0,T.isNil)(ye)||!(0,T.isNil)(xe)?Z(W,(0,T.head)(Fe),(0,T.last)(Fe)):le?Jt(H,X,le).ticks:Fe}function ie(W){return Math.abs(W)<1e-15?W:parseFloat(W.toFixed(15))}var me=[1,5,2,2.5,4,3],Oe=null,Ce=Number.EPSILON*100;function Ne(W,H){return(W%H+H)%H}function Y(W){return Math.round(W*1e12)/1e12}function $(W,H,X,le,ye,xe){var Fe=(0,T.size)(H),We=(0,T.indexOf)(H,W),it=0,Qe=Ne(le,xe);return(Qe<Ce||xe-Qe<Ce)&&le<=0&&ye>=0&&(it=1),1-We/(Fe-1)-X+it}function Ie(W,H,X){var le=(0,T.size)(H),ye=(0,T.indexOf)(H,W),xe=1;return 1-ye/(le-1)-X+xe}function _e(W,H,X,le,ye,xe){var Fe=(W-1)/(xe-ye),We=(H-1)/(Math.max(xe,le)-Math.min(X,ye));return 2-Math.max(Fe/We,We/Fe)}function Ae(W,H){return W>=H?2-(W-1)/(H-1):1}function Be(W,H,X,le){var ye=H-W;return 1-.5*(Math.pow(H-le,2)+Math.pow(W-X,2))/Math.pow(.1*ye,2)}function ze(W,H,X){var le=H-W;if(X>le){var ye=(X-le)/2;return 1-Math.pow(ye,2)/Math.pow(.1*le,2)}return 1}function Ye(){return 1}function Ve(W,H,X,le,ye,xe){X===void 0&&(X=5),le===void 0&&(le=!0),ye===void 0&&(ye=me),xe===void 0&&(xe=[.25,.2,.5,.05]);var Fe=X<0?0:Math.round(X);if(Number.isNaN(W)||Number.isNaN(H)||typeof W!="number"||typeof H!="number"||!Fe)return{min:0,max:0,ticks:[]};if(H-W<1e-15||Fe===1)return{min:W,max:H,ticks:[W]};if(H-W>1e148){var We=X||5,it=(H-W)/We;return{min:W,max:H,ticks:Array(We).fill(null).map(function(Or,br){return ie(W+it*br)})}}for(var Qe={score:-2,lmin:0,lmax:0,lstep:0},rt=1;rt<1/0;){for(var kt=0;kt<ye.length;kt+=1){var Gt=ye[kt],Wt=Ie(Gt,ye,rt);if(xe[0]*Wt+xe[1]+xe[2]+xe[3]<Qe.score){rt=1/0;break}for(var Ot=2;Ot<1/0;){var tr=Ae(Ot,Fe);if(xe[0]*Wt+xe[1]+xe[2]*tr+xe[3]<Qe.score)break;for(var nr=(H-W)/(Ot+1)/rt/Gt,ar=Math.ceil(Math.log10(nr));ar<1/0;){var rr=rt*Gt*Math.pow(10,ar),ee=ze(W,H,rr*(Ot-1));if(xe[0]*Wt+xe[1]*ee+xe[2]*tr+xe[3]<Qe.score)break;var L=Math.floor(H/rr)*rt-(Ot-1)*rt,j=Math.ceil(W/rr)*rt;if(L<=j)for(var We=j-L,te=0;te<=We;te+=1){var Q=L+te,oe=Q*(rr/rt),Ee=oe+rr*(Ot-1),Re=rr,Xe=$(Gt,ye,rt,oe,Ee,Re),ft=Be(W,H,oe,Ee),dt=_e(Ot,Fe,W,H,oe,Ee),wt=Ye(),It=xe[0]*Xe+xe[1]*ft+xe[2]*dt+xe[3]*wt;It>Qe.score&&(!le||oe<=W&&Ee>=H)&&(Qe.lmin=oe,Qe.lmax=Ee,Qe.lstep=Re,Qe.score=It)}ar+=1}Ot+=1}}rt+=1}var At=ie(Qe.lmax),Ut=ie(Qe.lmin),Bt=ie(Qe.lstep),Qt=Math.floor(Y((At-Ut)/Bt))+1,vr=new Array(Qt);vr[0]=ie(Ut);for(var kt=1;kt<Qt;kt++)vr[kt]=ie(vr[kt-1]+Bt);return{min:Math.min(W,(0,T.head)(vr)),max:Math.max(H,(0,T.last)(vr)),ticks:vr}}function qe(W){var H=W.min,X=W.max,le=W.tickCount,ye=W.nice,xe=W.tickInterval,Fe=W.minLimit,We=W.maxLimit,it=Ve(H,X,le,ye).ticks;return!(0,T.isNil)(Fe)||!(0,T.isNil)(We)?Z(W,(0,T.head)(it),(0,T.last)(it)):xe?Jt(H,X,xe).ticks:it}function je(W){var H=W.base,X=W.tickCount,le=W.min,ye=W.max,xe=W.values,Fe,We=R(H,ye);if(le>0)Fe=Math.floor(R(H,le));else{var it=P(xe,H,ye);Fe=Math.floor(R(H,it))}for(var Qe=We-Fe,rt=Math.ceil(Qe/X),kt=[],Gt=Fe;Gt<We+rt;Gt=Gt+rt)kt.push(Math.pow(H,Gt));return le<=0&&kt.unshift(0),kt}function at(W,H,X){if(X===void 0&&(X=5),W===H)return{max:H,min:W,ticks:[W]};var le=X<0?0:Math.round(X);if(le===0)return{max:H,min:W,ticks:[]};var ye=1.5,xe=.5+1.5*ye,Fe=H-W,We=Fe/le,it=Math.pow(10,Math.floor(Math.log10(We))),Qe=it;2*it-We<ye*(We-Qe)&&(Qe=2*it,5*it-We<xe*(We-Qe)&&(Qe=5*it,10*it-We<ye*(We-Qe)&&(Qe=10*it)));for(var rt=Math.ceil(H/Qe),kt=Math.floor(W/Qe),Gt=Math.max(rt*Qe,H),Wt=Math.min(kt*Qe,W),Ot=Math.floor((Gt-Wt)/Qe)+1,tr=new Array(Ot),nr=0;nr<Ot;nr++)tr[nr]=ie(Wt+nr*Qe);return{min:Wt,max:Gt,ticks:tr}}function vt(W){var H=W.exponent,X=W.tickCount,le=Math.ceil(K(H,W.max)),ye=Math.floor(K(H,W.min)),xe=at(ye,le,X).ticks;return xe.map(function(Fe){var We=Fe>=0?1:-1;return Math.pow(Fe,H)*We})}function ct(W,H){var X=W.length*H;return H===1?W[W.length-1]:H===0?W[0]:X%1!==0?W[Math.ceil(X)-1]:W.length%2===0?(W[X-1]+W[X])/2:W[X]}function st(W){var H=W.tickCount,X=W.values;if(!X||!X.length)return[];for(var le=X.slice().sort(function(We,it){return We-it}),ye=[],xe=0;xe<H;xe++){var Fe=xe/(H-1);ye.push(ct(le,Fe))}return ye}function gt(W){var H=W.min,X=W.max,le=W.tickCount,ye=W.tickInterval,xe=W.minLimit,Fe=W.maxLimit,We=at(H,X,le).ticks;return!(0,T.isNil)(xe)||!(0,T.isNil)(Fe)?Z(W,(0,T.head)(We),(0,T.last)(We)):ye?Jt(H,X,ye).ticks:We}function Et(W){var H=W.min,X=W.max,le=W.minTickInterval,ye=W.tickInterval,xe=W.tickCount;if(ye)xe=Math.ceil((X-H)/ye);else{ye=y(H,X,xe)[1];var Fe=(X-H)/ye,We=Fe/xe;We>1&&(ye=ye*Math.ceil(We)),le&&ye<le&&(ye=le)}ye=Math.max(Math.floor((X-H)/(Math.pow(2,12)-1)),ye);for(var it=[],Qe=H;Qe<X+ye;Qe+=ye)it.push(Qe);return it}function Vt(W){var H=Pe((0,u.__assign)({showLast:!0},W));return H}function qt(W){return new Date(W).getFullYear()}function Kt(W){return new Date(W,0,1).getTime()}function $t(W){return new Date(W).getMonth()}function or(W,H){var X=qt(W),le=qt(H),ye=$t(W),xe=$t(H);return(le-X)*12+(xe-ye)%12}function Zt(W,H){return new Date(W,H,1).getTime()}function Rt(W,H){return Math.ceil((H-W)/m)}function cr(W,H){return Math.ceil((H-W)/l)}function er(W,H){return Math.ceil((H-W)/(60*1e3))}function fr(W){var H=W.min,X=W.max,le=W.minTickInterval,ye=W.tickCount,xe=W.tickInterval,Fe=[];xe||(xe=(X-H)/ye,le&&xe<le&&(xe=le)),xe=Math.max(Math.floor((X-H)/(Math.pow(2,12)-1)),xe);var We=qt(H);if(xe>d)for(var it=qt(X),Qe=Math.ceil(xe/d),rt=We;rt<=it+Qe;rt=rt+Qe)Fe.push(Kt(rt));else if(xe>h)for(var kt=Math.ceil(xe/h),Gt=$t(H),Wt=or(H,X),rt=0;rt<=Wt+kt;rt=rt+kt)Fe.push(Zt(We,rt+Gt));else if(xe>m)for(var Ot=new Date(H),tr=Ot.getFullYear(),nr=Ot.getMonth(),ar=Ot.getDate(),rr=Math.ceil(xe/m),ee=Rt(H,X),rt=0;rt<ee+rr;rt=rt+rr)Fe.push(new Date(tr,nr,ar+rt).getTime());else if(xe>l)for(var Ot=new Date(H),tr=Ot.getFullYear(),nr=Ot.getMonth(),rr=Ot.getDate(),L=Ot.getHours(),j=Math.ceil(xe/l),te=cr(H,X),rt=0;rt<=te+j;rt=rt+j)Fe.push(new Date(tr,nr,rr,L+rt).getTime());else if(xe>s)for(var Q=er(H,X),oe=Math.ceil(xe/s),rt=0;rt<=Q+oe;rt=rt+oe)Fe.push(H+rt*s);else{var Ee=xe;Ee<f&&(Ee=f);for(var Re=Math.floor(H/f)*f,Xe=Math.ceil((X-H)/f),ft=Math.ceil(Ee/f),rt=0;rt<Xe+ft;rt=rt+ft)Fe.push(Re+rt*f)}return Fe.length>=512&&console.warn("Notice: current ticks length("+Fe.length+') >= 512, may cause performance issues, even out of memory. Because of the configure "tickInterval"(in milliseconds, current is '+xe+") is too small, increase the value to solve the problem!"),Fe}M("cat",Pe),M("time-cat",Vt),M("wilkinson-extended",qe),M("r-pretty",gt),M("time",Et),M("time-pretty",fr),M("log",je),M("pow",vt),M("quantile",st),M("d3-linear",J),Me("cat",n),Me("category",n),Me("identity",De),Me("linear",A),Me("log",ue),Me("pow",de),Me("time",we),Me("timeCat",D),Me("quantize",Le),Me("quantile",se)},98667:function(Je,w,G){"use strict";G.r(w),G.d(w,{Cache:function(){return Rn},assign:function(){return hr},augment:function(){return Ar},clamp:function(){return qt},clearAnimationFrame:function(){return fn},clone:function(){return sn},contains:function(){return M},debounce:function(){return vn},deepMix:function(){return hn},difference:function(){return t},each:function(){return m},endsWith:function(){return Ce},every:function(){return Y},extend:function(){return gn},filter:function(){return p},find:function(){return A},findIndex:function(){return R},firstValue:function(){return N},fixedBase:function(){return $t},flatten:function(){return B},flattenDeep:function(){return ae},forIn:function(){return j},get:function(){return In},getEllipsisText:function(){return Fn},getRange:function(){return Se},getType:function(){return Nr},getWrapBehavior:function(){return qe},group:function(){return Ye},groupBy:function(){return Be},groupToMap:function(){return ze},has:function(){return te},hasKey:function(){return Q},hasValue:function(){return Re},head:function(){return Z},identity:function(){return An},includes:function(){return M},indexOf:function(){return mn},isArguments:function(){return Yr},isArray:function(){return f},isArrayLike:function(){return C},isBoolean:function(){return Xr},isDate:function(){return dr},isDecimal:function(){return cr},isElement:function(){return un},isEmpty:function(){return wn},isEqual:function(){return Rr},isEqualWith:function(){return _n},isError:function(){return Qr},isEven:function(){return fr},isFinite:function(){return Tr},isFunction:function(){return o},isInteger:function(){return H},isMatch:function(){return y},isNegative:function(){return le},isNil:function(){return i},isNull:function(){return qr},isNumber:function(){return Zt},isNumberEqual:function(){return xe},isObject:function(){return s},isObjectLike:function(){return D},isOdd:function(){return We},isPlainObject:function(){return S},isPositive:function(){return Qe},isPrototype:function(){return Lr},isRegExp:function(){return rn},isString:function(){return tt},isType:function(){return e},isUndefined:function(){return an},keys:function(){return d},last:function(){return J},lowerCase:function(){return dt},lowerFirst:function(){return It},map:function(){return Mn},mapValues:function(){return Dn},max:function(){return de},maxBy:function(){return rt},measureTextWidth:function(){return mr},memoize:function(){return Pr},min:function(){return ve},minBy:function(){return kt},mix:function(){return hr},mod:function(){return Wt},noop:function(){return zn},number2color:function(){return st},omit:function(){return Cn},parseRadius:function(){return Et},pick:function(){return Nn},pull:function(){return pe},pullAt:function(){return De},reduce:function(){return He},remove:function(){return Ue},requestAnimationFrame:function(){return cn},set:function(){return Sn},size:function(){return Pn},some:function(){return Ie},sortBy:function(){return Mt},startsWith:function(){return me},substitute:function(){return Ut},throttle:function(){return kn},toArray:function(){return Tn},toDegree:function(){return nr},toInteger:function(){return ar},toRadian:function(){return L},toString:function(){return Xe},union:function(){return Xt},uniq:function(){return jt},uniqueId:function(){return Ln},upperCase:function(){return Qt},upperFirst:function(){return Or},values:function(){return Ee},valuesOfKey:function(){return Jt},wrapBehavior:function(){return at}});var T=function(O){return O!==null&&typeof O!="function"&&isFinite(O.length)},C=T,x=function(O,V){return C(O)?O.indexOf(V)>-1:!1},M=x,E=function(O,V){if(!C(O))return O;for(var ce=[],ge=0;ge<O.length;ge++){var ke=O[ge];V(ke,ge)&&ce.push(ke)}return ce},p=E,u=function(O,V){return V===void 0&&(V=[]),p(O,function(ce){return!M(V,ce)})},t=u,n={}.toString,r=function(O,V){return n.call(O)==="[object "+V+"]"},e=r,o=function(O){return e(O,"Function")},a=function(O){return O==null},i=a,f=function(O){return Array.isArray?Array.isArray(O):e(O,"Array")},s=function(O){var V=typeof O;return O!==null&&V==="object"||V==="function"};function l(O,V){if(O){var ce;if(f(O))for(var ge=0,ke=O.length;ge<ke&&(ce=V(O[ge],ge),ce!==!1);ge++);else if(s(O)){for(var Ke in O)if(O.hasOwnProperty(Ke)&&(ce=V(O[Ke],Ke),ce===!1))break}}}var m=l,h=Object.keys?function(O){return Object.keys(O)}:function(O){var V=[];return m(O,function(ce,ge){o(O)&&ge==="prototype"||V.push(ge)}),V},d=h;function v(O,V){var ce=d(V),ge=ce.length;if(i(O))return!ge;for(var ke=0;ke<ge;ke+=1){var Ke=ce[ke];if(V[Ke]!==O[Ke]||!(Ke in O))return!1}return!0}var y=v,_=function(O){return typeof O=="object"&&O!==null},D=_,b=function(O){if(!D(O)||!e(O,"Object"))return!1;if(Object.getPrototypeOf(O)===null)return!0;for(var V=O;Object.getPrototypeOf(V)!==null;)V=Object.getPrototypeOf(V);return Object.getPrototypeOf(O)===V},S=b;function k(O,V){if(!f(O))return null;var ce;if(o(V)&&(ce=V),S(V)&&(ce=function(ke){return y(ke,V)}),ce){for(var ge=0;ge<O.length;ge+=1)if(ce(O[ge]))return O[ge]}return null}var A=k;function K(O,V,ce){ce===void 0&&(ce=0);for(var ge=ce;ge<O.length;ge++)if(V(O[ge],ge))return ge;return-1}var R=K,P=function(O,V){for(var ce=null,ge=0;ge<O.length;ge++){var ke=O[ge],Ke=ke[V];if(!i(Ke)){f(Ke)?ce=Ke[0]:ce=Ke;break}}return ce},N=P,U=function(O){if(!f(O))return[];for(var V=[],ce=0;ce<O.length;ce++)V=V.concat(O[ce]);return V},B=U,ue=function(O,V){if(V===void 0&&(V=[]),!f(O))V.push(O);else for(var ce=0;ce<O.length;ce+=1)ue(O[ce],V);return V},ae=ue,de=function(O){if(f(O))return O.reduce(function(V,ce){return Math.max(V,ce)},O[0])},ve=function(O){if(f(O))return O.reduce(function(V,ce){return Math.min(V,ce)},O[0])},we=function(O){var V=O.filter(function(lt){return!isNaN(lt)});if(!V.length)return{min:0,max:0};if(f(O[0])){for(var ce=[],ge=0;ge<O.length;ge++)ce=ce.concat(O[ge]);V=ce}var ke=de(V),Ke=ve(V);return{min:Ke,max:ke}},Se=we,Le=Array.prototype,q=Le.splice,se=Le.indexOf,ne=function(O){for(var V=[],ce=1;ce<arguments.length;ce++)V[ce-1]=arguments[ce];for(var ge=0;ge<V.length;ge++)for(var ke=V[ge],Ke=-1;(Ke=se.call(O,ke))>-1;)q.call(O,Ke,1);return O},pe=ne,Me=Array.prototype.splice,he=function(V,ce){if(!C(V))return[];for(var ge=V?ce.length:0,ke=ge-1;ge--;){var Ke=void 0,lt=ce[ge];(ge===ke||lt!==Ke)&&(Ke=lt,Me.call(V,lt,1))}return V},De=he,Pe=function(O,V,ce){if(!f(O)&&!S(O))return O;var ge=ce;return m(O,function(ke,Ke){ge=V(ge,ke,Ke)}),ge},He=Pe,Ze=function(O,V){var ce=[];if(!C(O))return ce;for(var ge=-1,ke=[],Ke=O.length;++ge<Ke;){var lt=O[ge];V(lt,ge,O)&&(ce.push(lt),ke.push(ge))}return De(O,ke),ce},Ue=Ze,tt=function(O){return e(O,"String")};function pt(O,V){var ce;if(o(V))ce=function(ke,Ke){return V(ke)-V(Ke)};else{var ge=[];tt(V)?ge.push(V):f(V)&&(ge=V),ce=function(ke,Ke){for(var lt=0;lt<ge.length;lt+=1){var Tt=ge[lt];if(ke[Tt]>Ke[Tt])return 1;if(ke[Tt]<Ke[Tt])return-1}return 0}}return O.sort(ce),O}var Mt=pt;function jt(O,V){V===void 0&&(V=new Map);var ce=[];if(Array.isArray(O))for(var ge=0,ke=O.length;ge<ke;ge++){var Ke=O[ge];V.has(Ke)||(ce.push(Ke),V.set(Ke,!0))}return ce}var Ct=function(){for(var O=[],V=0;V<arguments.length;V++)O[V]=arguments[V];return jt([].concat.apply([],O))},Xt=Ct,Jt=function(O,V){for(var ce=[],ge={},ke=0;ke<O.length;ke++){var Ke=O[ke],lt=Ke[V];if(!i(lt)){f(lt)||(lt=[lt]);for(var Tt=0;Tt<lt.length;Tt++){var ir=lt[Tt];ge[ir]||(ce.push(ir),ge[ir]=!0)}}}return ce};function Z(O){if(C(O))return O[0]}function J(O){if(C(O)){var V=O;return V[V.length-1]}}function ie(O,V){return f(O)||tt(O)?O[0]===V:!1}var me=ie;function Oe(O,V){return f(O)||tt(O)?O[O.length-1]===V:!1}var Ce=Oe,Ne=function(O,V){for(var ce=0;ce<O.length;ce++)if(!V(O[ce],ce))return!1;return!0},Y=Ne,$=function(O,V){for(var ce=0;ce<O.length;ce++)if(V(O[ce],ce))return!0;return!1},Ie=$,_e=Object.prototype.hasOwnProperty;function Ae(O,V){if(!V||!f(O))return{};for(var ce={},ge=o(V)?V:function(Tt){return Tt[V]},ke,Ke=0;Ke<O.length;Ke++){var lt=O[Ke];ke=ge(lt),_e.call(ce,ke)?ce[ke].push(lt):ce[ke]=[lt]}return ce}var Be=Ae;function ze(O,V){if(!V)return{0:O};if(!o(V)){var ce=f(V)?V:V.replace(/\s+/g,"").split("*");V=function(ge){for(var ke="_",Ke=0,lt=ce.length;Ke<lt;Ke++)ke+=ge[ce[Ke]]&&ge[ce[Ke]].toString();return ke}}return Be(O,V)}var Ye=function(O,V){if(!V)return[O];var ce=ze(O,V),ge=[];for(var ke in ce)ge.push(ce[ke]);return ge};function Ve(O,V){return O["_wrap_"+V]}var qe=Ve;function je(O,V){if(O["_wrap_"+V])return O["_wrap_"+V];var ce=function(ge){O[V](ge)};return O["_wrap_"+V]=ce,ce}var at=je,vt={};function ct(O){var V=vt[O];if(!V){for(var ce=O.toString(16),ge=ce.length;ge<6;ge++)ce="0"+ce;V="#"+ce,vt[O]=V}return V}var st=ct;function gt(O){var V=0,ce=0,ge=0,ke=0;return f(O)?O.length===1?V=ce=ge=ke=O[0]:O.length===2?(V=ge=O[0],ce=ke=O[1]):O.length===3?(V=O[0],ce=ke=O[1],ge=O[2]):(V=O[0],ce=O[1],ge=O[2],ke=O[3]):V=ce=ge=ke=O,{r1:V,r2:ce,r3:ge,r4:ke}}var Et=gt,Vt=function(O,V,ce){return O<V?V:O>ce?ce:O},qt=Vt,Kt=function(O,V){var ce=V.toString(),ge=ce.indexOf(".");if(ge===-1)return Math.round(O);var ke=ce.substr(ge+1).length;return ke>20&&(ke=20),parseFloat(O.toFixed(ke))},$t=Kt,or=function(O){return e(O,"Number")},Zt=or,Rt=function(O){return Zt(O)&&O%1!==0},cr=Rt,er=function(O){return Zt(O)&&O%2===0},fr=er,W=Number.isInteger?Number.isInteger:function(O){return Zt(O)&&O%1===0},H=W,X=function(O){return Zt(O)&&O<0},le=X,ye=1e-5;function xe(O,V,ce){return ce===void 0&&(ce=ye),Math.abs(O-V)<ce}var Fe=function(O){return Zt(O)&&O%2!==0},We=Fe,it=function(O){return Zt(O)&&O>0},Qe=it,rt=function(O,V){if(f(O)){for(var ce,ge=-1/0,ke=0;ke<O.length;ke++){var Ke=O[ke],lt=o(V)?V(Ke):Ke[V];lt>ge&&(ce=Ke,ge=lt)}return ce}},kt=function(O,V){if(f(O)){for(var ce,ge=1/0,ke=0;ke<O.length;ke++){var Ke=O[ke],lt=o(V)?V(Ke):Ke[V];lt<ge&&(ce=Ke,ge=lt)}return ce}},Gt=function(O,V){return(O%V+V)%V},Wt=Gt,Ot=180/Math.PI,tr=function(O){return Ot*O},nr=tr,ar=parseInt,rr=Math.PI/180,ee=function(O){return rr*O},L=ee,j=m,te=function(O,V){return O.hasOwnProperty(V)},Q=te,oe=Object.values?function(O){return Object.values(O)}:function(O){var V=[];return m(O,function(ce,ge){o(O)&&ge==="prototype"||V.push(ce)}),V},Ee=oe,Re=function(O,V){return M(Ee(O),V)},Xe=function(O){return i(O)?"":O.toString()},ft=function(O){return Xe(O).toLowerCase()},dt=ft,wt=function(O){var V=Xe(O);return V.charAt(0).toLowerCase()+V.substring(1)},It=wt;function At(O,V){return!O||!V?O:O.replace(/\\?\{([^{}]+)\}/g,function(ce,ge){return ce.charAt(0)==="\\"?ce.slice(1):V[ge]===void 0?"":V[ge]})}var Ut=At,Bt=function(O){return Xe(O).toUpperCase()},Qt=Bt,vr=function(O){var V=Xe(O);return V.charAt(0).toUpperCase()+V.substring(1)},Or=vr,br={}.toString,Hr=function(O){return br.call(O).replace(/^\[object /,"").replace(/]$/,"")},Nr=Hr,Cr=function(O){return e(O,"Arguments")},Yr=Cr,kr=function(O){return e(O,"Boolean")},Xr=kr,Zr=function(O){return e(O,"Date")},dr=Zr,Kr=function(O){return e(O,"Error")},Qr=Kr;function Tr(O){return Zt(O)&&isFinite(O)}var Jr=function(O){return O===null},qr=Jr,$r=Object.prototype,en=function(O){var V=O&&O.constructor,ce=typeof V=="function"&&V.prototype||$r;return O===ce},Lr=en,tn=function(O){return e(O,"RegExp")},rn=tn,nn=function(O){return O===void 0},an=nn,on=function(O){return O instanceof Element||O instanceof HTMLDocument},un=on;function cn(O){var V=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(ce){return setTimeout(ce,16)};return V(O)}function fn(O){var V=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.msCancelAnimationFrame||clearTimeout;V(O)}function Mr(O,V){for(var ce in V)V.hasOwnProperty(ce)&&ce!=="constructor"&&V[ce]!==void 0&&(O[ce]=V[ce])}function hr(O,V,ce,ge){return V&&Mr(O,V),ce&&Mr(O,ce),ge&&Mr(O,ge),O}var zr=function(){for(var O=[],V=0;V<arguments.length;V++)O[V]=arguments[V];for(var ce=O[0],ge=1;ge<O.length;ge++){var ke=O[ge];o(ke)&&(ke=ke.prototype),hr(ce.prototype,ke)}},Ar=zr,Er=function(O){if(typeof O!="object"||O===null)return O;var V;if(f(O)){V=[];for(var ce=0,ge=O.length;ce<ge;ce++)typeof O[ce]=="object"&&O[ce]!=null?V[ce]=Er(O[ce]):V[ce]=O[ce]}else{V={};for(var ke in O)typeof O[ke]=="object"&&O[ke]!=null?V[ke]=Er(O[ke]):V[ke]=O[ke]}return V},sn=Er;function ln(O,V,ce){var ge;return function(){var ke=this,Ke=arguments,lt=function(){ge=null,ce||O.apply(ke,Ke)},Tt=ce&&!ge;clearTimeout(ge),ge=setTimeout(lt,V),Tt&&O.apply(ke,Ke)}}var vn=ln,Pr=function(O,V){if(!o(O))throw new TypeError("Expected a function");var ce=function(){for(var ge=[],ke=0;ke<arguments.length;ke++)ge[ke]=arguments[ke];var Ke=V?V.apply(this,ge):ge[0],lt=ce.cache;if(lt.has(Ke))return lt.get(Ke);var Tt=O.apply(this,ge);return lt.set(Ke,Tt),Tt};return ce.cache=new Map,ce},Gr=5;function Fr(O,V,ce,ge){ce=ce||0,ge=ge||Gr;for(var ke in V)if(V.hasOwnProperty(ke)){var Ke=V[ke];Ke!==null&&S(Ke)?(S(O[ke])||(O[ke]={}),ce<ge?Fr(O[ke],Ke,ce+1,ge):O[ke]=V[ke]):f(Ke)?(O[ke]=[],O[ke]=O[ke].concat(Ke)):Ke!==void 0&&(O[ke]=Ke)}}var dn=function(O){for(var V=[],ce=1;ce<arguments.length;ce++)V[ce-1]=arguments[ce];for(var ge=0;ge<V.length;ge+=1)Fr(O,V[ge]);return O},hn=dn,pn=function(O,V,ce,ge){o(V)||(ce=V,V=O,O=function(){});var ke=Object.create?function(lt,Tt){return Object.create(lt,{constructor:{value:Tt}})}:function(lt,Tt){function ir(){}ir.prototype=lt;var ur=new ir;return ur.constructor=Tt,ur},Ke=ke(V.prototype,O);return O.prototype=hr(Ke,O.prototype),O.superclass=ke(V.prototype,V),hr(Ke,ce),hr(O,ge),O},gn=pn,yn=function(O,V){if(!C(O))return-1;var ce=Array.prototype.indexOf;if(ce)return ce.call(O,V);for(var ge=-1,ke=0;ke<O.length;ke++)if(O[ke]===V){ge=ke;break}return ge},mn=yn,xn=Object.prototype.hasOwnProperty;function lr(O){if(i(O))return!0;if(C(O))return!O.length;var V=Nr(O);if(V==="Map"||V==="Set")return!O.size;if(Lr(O))return!Object.keys(O).length;for(var ce in O)if(xn.call(O,ce))return!1;return!0}var wn=lr,Dr=function(O,V){if(O===V)return!0;if(!O||!V||tt(O)||tt(V))return!1;if(C(O)||C(V)){if(O.length!==V.length)return!1;for(var ce=!0,ge=0;ge<O.length&&(ce=Dr(O[ge],V[ge]),!!ce);ge++);return ce}if(D(O)||D(V)){var ke=Object.keys(O),Ke=Object.keys(V);if(ke.length!==Ke.length)return!1;for(var ce=!0,ge=0;ge<ke.length&&(ce=Dr(O[ke[ge]],V[ke[ge]]),!!ce);ge++);return ce}return!1},Rr=Dr,_n=function(O,V,ce){return o(ce)?!!ce(O,V):Rr(O,V)},bn=function(O,V){if(!C(O))return O;for(var ce=[],ge=0;ge<O.length;ge++){var ke=O[ge];ce.push(V(ke,ge))}return ce},Mn=bn,En=function(O){return O},Dn=function(O,V){V===void 0&&(V=En);var ce={};return s(O)&&!i(O)&&Object.keys(O).forEach(function(ge){ce[ge]=V(O[ge],ge)}),ce},In=function(O,V,ce){for(var ge=0,ke=tt(V)?V.split("."):V;O&&ge<ke.length;)O=O[ke[ge++]];return O===void 0||ge<ke.length?ce:O},Sn=function(O,V,ce){var ge=O,ke=tt(V)?V.split("."):V;return ke.forEach(function(Ke,lt){lt<ke.length-1?(s(ge[Ke])||(ge[Ke]=Zt(ke[lt+1])?[]:{}),ge=ge[Ke]):ge[Ke]=ce}),O},On=Object.prototype.hasOwnProperty,Nn=function(O,V){if(O===null||!S(O))return{};var ce={};return m(V,function(ge){On.call(O,ge)&&(ce[ge]=O[ge])}),ce},Cn=function(O,V){return He(O,function(ce,ge,ke){return V.includes(ke)||(ce[ke]=ge),ce},{})},kn=function(O,V,ce){var ge,ke,Ke,lt,Tt=0;ce||(ce={});var ir=function(){Tt=ce.leading===!1?0:Date.now(),ge=null,lt=O.apply(ke,Ke),ge||(ke=Ke=null)},ur=function(){var sr=Date.now();!Tt&&ce.leading===!1&&(Tt=sr);var Ir=V-(sr-Tt);return ke=this,Ke=arguments,Ir<=0||Ir>V?(ge&&(clearTimeout(ge),ge=null),Tt=sr,lt=O.apply(ke,Ke),ge||(ke=Ke=null)):!ge&&ce.trailing!==!1&&(ge=setTimeout(ir,Ir)),lt};return ur.cancel=function(){clearTimeout(ge),Tt=0,ge=ke=Ke=null},ur},Tn=function(O){return C(O)?Array.prototype.slice.call(O):[]},xr={},Ln=function(O){return O=O||"g",xr[O]?xr[O]+=1:xr[O]=1,O+xr[O]},zn=function(){},An=function(O){return O};function Pn(O){return i(O)?0:C(O)?O.length:Object.keys(O).length}var Gn=G(92336),yr,mr=Pr(function(O,V){V===void 0&&(V={});var ce=V.fontSize,ge=V.fontFamily,ke=V.fontWeight,Ke=V.fontStyle,lt=V.fontVariant;return yr||(yr=document.createElement("canvas").getContext("2d")),yr.font=[Ke,lt,ke,ce+"px",ge].join(" "),yr.measureText(tt(O)?O:"").width},function(O,V){return V===void 0&&(V={}),(0,Gn.__spreadArrays)([O],Ee(V)).join("")}),Fn=function(O,V,ce,ge){ge===void 0&&(ge="...");var ke=16,Ke=mr(ge,ce),lt=tt(O)?O:Xe(O),Tt=V,ir=[],ur,sr;if(mr(O,ce)<=V)return O;for(;ur=lt.substr(0,ke),sr=mr(ur,ce),!(sr+Ke>Tt&&sr>Tt);)if(ir.push(ur),Tt-=sr,lt=lt.substr(ke),!lt)return ir.join("");for(;ur=lt.substr(0,1),sr=mr(ur,ce),!(sr+Ke>Tt);)if(ir.push(ur),Tt-=sr,lt=lt.substr(1),!lt)return ir.join("");return""+ir.join("")+ge},Br=function(){function O(){this.map={}}return O.prototype.has=function(V){return this.map[V]!==void 0},O.prototype.get=function(V,ce){var ge=this.map[V];return ge===void 0?ce:ge},O.prototype.set=function(V,ce){this.map[V]=ce},O.prototype.clear=function(){this.map={}},O.prototype.delete=function(V){delete this.map[V]},O.prototype.size=function(){return Object.keys(this.map).length},O}(),Rn=Br},39694:function(Je,w,G){"use strict";G.r(w),G.d(w,{Cache:function(){return ii},angleTo:function(){return k},arcToCubic:function(){return Mt},assign:function(){return wr},augment:function(){return Sa},clamp:function(){return yn},clearAnimationFrame:function(){return Da},clone:function(){return Oa},clonePath:function(){return ie},contains:function(){return er},createDOM:function(){return vi},debounce:function(){return Ca},deepMix:function(){return za},difference:function(){return X},direction:function(){return S},distanceSquareRoot:function(){return Oe},each:function(){return it},endsWith:function(){return an},equalizeSegments:function(){return or},every:function(){return un},extend:function(){return Pa},filter:function(){return W},find:function(){return j},findIndex:function(){return Q},firstValue:function(){return Ee},fixedBase:function(){return xn},flatten:function(){return Xe},flattenDeep:function(){return dt},forIn:function(){return Gn},get:function(){return Xa},getDrawDirection:function(){return st},getPathArea:function(){return ct},getPathBBox:function(){return Ye},getPathBBoxTotalLength:function(){return qe},getPointAtLength:function(){return gt},getRange:function(){return Ut},getRotatedCurve:function(){return at},getTotalLength:function(){return Ve},getType:function(){return Xn},getWrapBehavior:function(){return ln},gradient:function(){return a},group:function(){return Er},groupBy:function(){return zr},groupToMap:function(){return Ar},has:function(){return yr},hasKey:function(){return mr},hasValue:function(){return Rn},head:function(){return en},identity:function(){return ri},includes:function(){return er},indexOf:function(){return Fa},isArguments:function(){return fa},isArray:function(){return xe},isArrayLike:function(){return Rt},isBoolean:function(){return la},isDate:function(){return va},isDecimal:function(){return wn},isElement:function(){return Ma},isEmpty:function(){return ja},isEqual:function(){return Qn},isEqualWith:function(){return Wa},isError:function(){return ha},isEven:function(){return Dr},isFinite:function(){return pa},isFunction:function(){return le},isInteger:function(){return Rr},isMatch:function(){return Gt},isNegative:function(){return _n},isNil:function(){return ye},isNull:function(){return ga},isNumber:function(){return lr},isNumberEqual:function(){return Mn},isObject:function(){return Fe},isObjectLike:function(){return Ot},isOdd:function(){return En},isPlainObject:function(){return ee},isPointInPolygon:function(){return Vn},isPointInStroke:function(){return qt},isPolygonsIntersect:function(){return li},isPositive:function(){return In},isPrototype:function(){return Zn},isRegExp:function(){return wa},isString:function(){return dr},isType:function(){return ar},isUndefined:function(){return ba},keys:function(){return rt},last:function(){return Lr},lowerCase:function(){return ce},lowerFirst:function(){return ke},map:function(){return Va},mapValues:function(){return Ya},max:function(){return wt},maxBy:function(){return Sn},memoize:function(){return p},min:function(){return It},minBy:function(){return On},mix:function(){return wr},mod:function(){return Cn},modifyCSS:function(){return di},noop:function(){return ti},normalizePath:function(){return Ue},number2color:function(){return dn},omit:function(){return Ja},parseRadius:function(){return pn},path2Absolute:function(){return He},path2Array:function(){return J},path2Curve:function(){return Z},path2String:function(){return R},pick:function(){return Qa},pull:function(){return br},pullAt:function(){return Cr},reduce:function(){return kr},remove:function(){return Zr},requestAnimationFrame:function(){return Ea},reverseCurve:function(){return me},rgb2arr:function(){return T},set:function(){return Za},size:function(){return ni},some:function(){return fn},sortBy:function(){return Qr},startsWith:function(){return rn},substitute:function(){return lt},throttle:function(){return qa},toArray:function(){return $a},toCSSGradient:function(){return m},toDegree:function(){return xr},toInteger:function(){return Ln},toRGB:function(){return r},toRadian:function(){return Pn},toString:function(){return O},transform:function(){return D},union:function(){return qr},uniq:function(){return Tr},uniqueId:function(){return ei},upperCase:function(){return ir},upperFirst:function(){return sr},values:function(){return Br},valuesOfKey:function(){return $r},vertical:function(){return A},wrapBehavior:function(){return Pr}});function T(c){return[parseInt(c.substr(1,2),16),parseInt(c.substr(3,2),16),parseInt(c.substr(5,2),16)]}function C(c){var g=Math.round(c).toString(16);return g.length===1?"0".concat(g):g}function x(c){return"#".concat(C(c[0])).concat(C(c[1])).concat(C(c[2]))}function M(c){var g,I,z,F=c||1;function re(be,Te){++g>F&&(z=I,fe(1),++g),I[be]=Te}function fe(be){g=0,I=Object.create(null),be||(z=Object.create(null))}return fe(),{clear:fe,has:function(be){return I[be]!==void 0||z[be]!==void 0},get:function(be){var Te=I[be];if(Te!==void 0)return Te;if((Te=z[be])!==void 0)return re(be,Te),Te},set:function(be,Te){I[be]!==void 0?I[be]=Te:re(be,Te)}}}var E=new Map;function p(c,g,I){I===void 0&&(I=128);var z=function(){for(var F=[],re=0;re<arguments.length;re++)F[re]=arguments[re];var fe=g?g.apply(this,F):F[0];E.has(c)||E.set(c,M(I));var be=E.get(c);if(be.has(fe))return be.get(fe);var Te=c.apply(this,F);return be.set(fe,Te),Te};return z}var u=/rgba?\(([\s.,0-9]+)\)/;function t(){var c=document.getElementById("antv-web-colour-picker");return c||(c=document.createElement("i"),c.id="antv-web-colour-picker",c.title="Web Colour Picker",c.style.display="none",document.body.appendChild(c),c)}function n(c){if(c[0]==="#"&&c.length===7)return c;var g=t();g.style.color=c;var I=document.defaultView.getComputedStyle(g,"").getPropertyValue("color"),z=u.exec(I),F=z[1].split(/\s*,\s*/).map(function(re){return Number(re)});return I=x(F),I}var r=p(n,function(c){return c},256);function e(c,g,I,z){return c[z]+(g[z]-c[z])*I}function o(c,g){var I=isNaN(Number(g))||g<0?0:g>1?1:Number(g),z=c.length-1,F=Math.floor(z*I),re=z*I-F,fe=c[F],be=F===z?fe:c[F+1];return x([e(fe,be,re,0),e(fe,be,re,1),e(fe,be,re,2)])}function a(c){var g=typeof c=="string"?c.split("-"):c,I=g.map(function(z){return T(z.indexOf("#")===-1?r(z):z)});return function(z){return o(I,z)}}var i=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,f=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,s=/[\d.]+:(#[^\s]+|[^)]+\))/gi;function l(c){return/^[r,R,L,l]{1}[\s]*\(/.test(c)}function m(c){if(l(c)){var g,I=void 0;if(c[0]==="l"){var z=i.exec(c),F=+z[1]+90;I=z[2],g="linear-gradient(".concat(F,"deg, ")}else if(c[0]==="r"){g="radial-gradient(";var z=f.exec(c);I=z[4]}var re=I.match(s);return re.forEach(function(fe,be){var Te=fe.split(":");g+="".concat(Te[1]," ").concat(Number(Te[0])*100,"%"),be!==re.length-1&&(g+=", ")}),g+=")",g}return c}var h=G(96661);function d(c,g,I){var z=[0,0,0,0,0,0,0,0,0];return h.fromTranslation(z,I),h.multiply(c,z,g)}function v(c,g,I){var z=[0,0,0,0,0,0,0,0,0];return h.fromRotation(z,I),h.multiply(c,z,g)}function y(c,g,I){var z=[0,0,0,0,0,0,0,0,0];return h.fromScaling(z,I),h.multiply(c,z,g)}function _(c,g,I){return h.multiply(c,I,g)}function D(c,g){for(var I=c?[].concat(c):[1,0,0,0,1,0,0,0,1],z=0,F=g.length;z<F;z++){var re=g[z];switch(re[0]){case"t":d(I,I,[re[1],re[2]]);break;case"s":y(I,I,[re[1],re[2]]);break;case"r":v(I,I,re[1]);break;case"m":_(I,I,re[1]);break;default:break}}return I}var b=G(69341);function S(c,g){return c[0]*g[1]-g[0]*c[1]}function k(c,g,I){var z=b.angle(c,g),F=S(c,g)>=0;return I?F?Math.PI*2-z:z:F?z:Math.PI*2-z}function A(c,g,I){return I?(c[0]=g[1],c[1]=-1*g[0]):(c[0]=-1*g[1],c[1]=g[0]),c}function K(c,g){if(g==="off")return[].concat(c);var I=typeof g=="number"&&g>=1?Math.pow(10,g):1;return c.map(function(z){var F=z.slice(1).map(Number).map(function(re){return g?Math.round(re*I)/I:Math.round(re)});return[z[0]].concat(F)})}function R(c,g){return g===void 0&&(g="off"),K(c,g).map(function(I){return I[0]+I.slice(1).join(" ")}).join("")}var P=G(92336),N={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function U(c,g,I){if(c[I].length>7){c[I].shift();for(var z=c[I],F=I;z.length;)g[I]="A",c.splice(F+=1,0,["C"].concat(z.splice(0,6)));c.splice(I,1)}}var B={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function ue(c){return Array.isArray(c)&&c.every(function(g){var I=g[0].toLowerCase();return B[I]===g.length-1&&"achlmqstvz".includes(I)})}function ae(c){return ue(c)&&c.every(function(g){var I=g[0];return I===I.toUpperCase()})}function de(c){return ae(c)&&c.every(function(g){var I=g[0];return"ACLMQZ".includes(I)})}function ve(c){for(var g=c.pathValue[c.segmentStart],I=g.toLowerCase(),z=c.data;z.length>=B[I]&&(I==="m"&&z.length>2?(c.segments.push([g].concat(z.splice(0,2))),I="l",g=g==="m"?"l":"L"):c.segments.push([g].concat(z.splice(0,B[I]))),!!B[I]););}function we(c){var g=c.index,I=c.pathValue,z=I.charCodeAt(g);if(z===48){c.param=0,c.index+=1;return}if(z===49){c.param=1,c.index+=1;return}c.err='[path-util]: invalid Arc flag "'.concat(I[g],'", expecting 0 or 1 at index ').concat(g)}function Se(c){return c>=48&&c<=57||c===43||c===45||c===46}function Le(c){return c>=48&&c<=57}function q(c){var g=c.max,I=c.pathValue,z=c.index,F=z,re=!1,fe=!1,be=!1,Te=!1,Ge;if(F>=g){c.err="[path-util]: Invalid path value at index ".concat(F,', "pathValue" is missing param');return}if(Ge=I.charCodeAt(F),(Ge===43||Ge===45)&&(F+=1,Ge=I.charCodeAt(F)),!Le(Ge)&&Ge!==46){c.err="[path-util]: Invalid path value at index ".concat(F,', "').concat(I[F],'" is not a number');return}if(Ge!==46){if(re=Ge===48,F+=1,Ge=I.charCodeAt(F),re&&F<g&&Ge&&Le(Ge)){c.err="[path-util]: Invalid path value at index ".concat(z,', "').concat(I[z],'" illegal number');return}for(;F<g&&Le(I.charCodeAt(F));)F+=1,fe=!0;Ge=I.charCodeAt(F)}if(Ge===46){for(Te=!0,F+=1;Le(I.charCodeAt(F));)F+=1,be=!0;Ge=I.charCodeAt(F)}if(Ge===101||Ge===69){if(Te&&!fe&&!be){c.err="[path-util]: Invalid path value at index ".concat(F,', "').concat(I[F],'" invalid float exponent');return}if(F+=1,Ge=I.charCodeAt(F),(Ge===43||Ge===45)&&(F+=1),F<g&&Le(I.charCodeAt(F)))for(;F<g&&Le(I.charCodeAt(F));)F+=1;else{c.err="[path-util]: Invalid path value at index ".concat(F,', "').concat(I[F],'" invalid integer exponent');return}}c.index=F,c.param=+c.pathValue.slice(z,F)}function se(c){var g=[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279];return c===10||c===13||c===8232||c===8233||c===32||c===9||c===11||c===12||c===160||c>=5760&&g.includes(c)}function ne(c){for(var g=c.pathValue,I=c.max;c.index<I&&se(g.charCodeAt(c.index));)c.index+=1}function pe(c){switch(c|32){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return!0;default:return!1}}function Me(c){return(c|32)===97}function he(c){var g=c.max,I=c.pathValue,z=c.index,F=I.charCodeAt(z),re=B[I[z].toLowerCase()];if(c.segmentStart=z,!pe(F)){c.err='[path-util]: Invalid path value "'.concat(I[z],'" is not a path command');return}if(c.index+=1,ne(c),c.data=[],!re){ve(c);return}for(;;){for(var fe=re;fe>0;fe-=1){if(Me(F)&&(fe===3||fe===4)?we(c):q(c),c.err.length)return;c.data.push(c.param),ne(c),c.index<g&&I.charCodeAt(c.index)===44&&(c.index+=1,ne(c))}if(c.index>=c.max||!Se(I.charCodeAt(c.index)))break}ve(c)}var De=function(){function c(g){this.pathValue=g,this.segments=[],this.max=g.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=""}return c}();function Pe(c){if(ue(c))return[].concat(c);var g=new De(c);for(ne(g);g.index<g.max&&!g.err.length;)he(g);return g.err?g.err:g.segments}function He(c){if(ae(c))return[].concat(c);var g=Pe(c),I=0,z=0,F=0,re=0;return g.map(function(fe){var be=fe.slice(1).map(Number),Te=fe[0],Ge=Te.toUpperCase();if(Te==="M")return I=be[0],z=be[1],F=I,re=z,["M",I,z];var $e;if(Te!==Ge)switch(Ge){case"A":$e=[Ge,be[0],be[1],be[2],be[3],be[4],be[5]+I,be[6]+z];break;case"V":$e=[Ge,be[0]+z];break;case"H":$e=[Ge,be[0]+I];break;default:{var nt=be.map(function(yt,_t){return yt+(_t%2?z:I)});$e=[Ge].concat(nt)}}else $e=[Ge].concat(be);var et=$e.length;switch(Ge){case"Z":I=F,z=re;break;case"H":I=$e[1];break;case"V":z=$e[1];break;default:I=$e[et-2],z=$e[et-1],Ge==="M"&&(F=I,re=z)}return $e})}function Ze(c,g){var I=c[0],z=g.x1,F=g.y1,re=g.x2,fe=g.y2,be=c.slice(1).map(Number),Te=c;if("TQ".includes(I)||(g.qx=null,g.qy=null),I==="H")Te=["L",c[1],F];else if(I==="V")Te=["L",z,c[1]];else if(I==="S"){var Ge=z*2-re,$e=F*2-fe;g.x1=Ge,g.y1=$e,Te=["C",Ge,$e].concat(be)}else if(I==="T"){var nt=z*2-g.qx,et=F*2-g.qy;g.qx=nt,g.qy=et,Te=["Q",nt,et].concat(be)}else if(I==="Q"){var yt=be[0],_t=be[1];g.qx=yt,g.qy=_t}return Te}function Ue(c){if(de(c))return[].concat(c);for(var g=He(c),I=(0,P.__assign)({},N),z=0;z<g.length;z+=1){g[z]=Ze(g[z],I);var F=g[z],re=F.length;I.x1=+F[re-2],I.y1=+F[re-1],I.x2=+F[re-4]||I.x1,I.y2=+F[re-3]||I.y1}return g}function tt(c){return de(c)&&c.every(function(g){var I=g[0];return"MC".includes(I)})}function pt(c,g,I){var z=c*Math.cos(I)-g*Math.sin(I),F=c*Math.sin(I)+g*Math.cos(I);return{x:z,y:F}}function Mt(c,g,I,z,F,re,fe,be,Te,Ge){var $e=c,nt=g,et=I,yt=z,_t=be,xt=Te,ht=Math.PI*120/180,St=Math.PI/180*(+F||0),ot=[],bt,mt,ut,Dt,Lt;if(Ge)mt=Ge[0],ut=Ge[1],Dt=Ge[2],Lt=Ge[3];else{bt=pt($e,nt,-St),$e=bt.x,nt=bt.y,bt=pt(_t,xt,-St),_t=bt.x,xt=bt.y;var Pt=($e-_t)/2,zt=(nt-xt)/2,Nt=Pt*Pt/(et*et)+zt*zt/(yt*yt);Nt>1&&(Nt=Math.sqrt(Nt),et*=Nt,yt*=Nt);var Ft=et*et,Ht=yt*yt,Yt=(re===fe?-1:1)*Math.sqrt(Math.abs((Ft*Ht-Ft*zt*zt-Ht*Pt*Pt)/(Ft*zt*zt+Ht*Pt*Pt)));Dt=Yt*et*zt/yt+($e+_t)/2,Lt=Yt*-yt*Pt/et+(nt+xt)/2,mt=Math.asin(((nt-Lt)/yt*Math.pow(10,9)>>0)/Math.pow(10,9)),ut=Math.asin(((xt-Lt)/yt*Math.pow(10,9)>>0)/Math.pow(10,9)),mt=$e<Dt?Math.PI-mt:mt,ut=_t<Dt?Math.PI-ut:ut,mt<0&&(mt=Math.PI*2+mt),ut<0&&(ut=Math.PI*2+ut),fe&&mt>ut&&(mt-=Math.PI*2),!fe&&ut>mt&&(ut-=Math.PI*2)}var Sr=ut-mt;if(Math.abs(Sr)>ht){var Hn=ut,pr=_t,Wr=xt;ut=mt+ht*(fe&&ut>mt?1:-1),_t=Dt+et*Math.cos(ut),xt=Lt+yt*Math.sin(ut),ot=Mt(_t,xt,et,yt,F,0,fe,pr,Wr,[ut,Hn,Dt,Lt])}Sr=ut-mt;var Ur=Math.cos(mt),Vr=Math.sin(mt),Yn=Math.cos(ut),hi=Math.sin(ut),ea=Math.tan(Sr/4),ta=4/3*et*ea,ra=4/3*yt*ea,na=[$e,nt],_r=[$e+ta*Vr,nt-ra*Ur],aa=[_t+ta*hi,xt-ra*Yn],ia=[_t,xt];if(_r[0]=2*na[0]-_r[0],_r[1]=2*na[1]-_r[1],Ge)return _r.concat(aa,ia,ot);ot=_r.concat(aa,ia,ot);for(var oa=[],gr=0,pi=ot.length;gr<pi;gr+=1)oa[gr]=gr%2?pt(ot[gr-1],ot[gr],St).y:pt(ot[gr],ot[gr+1],St).x;return oa}function jt(c,g,I,z,F,re){var fe=.3333333333333333,be=2/3;return[fe*c+be*I,fe*g+be*z,fe*F+be*I,fe*re+be*z,F,re]}function Ct(c,g,I){var z=c[0],F=c[1],re=g[0],fe=g[1];return[z+(re-z)*I,F+(fe-F)*I]}var Xt=function(c,g,I,z){var F=.5,re=Ct([c,g],[I,z],F);return(0,P.__spreadArray)((0,P.__spreadArray)([],re,!0),[I,z,I,z],!1)};function Jt(c,g){var I=c[0],z=c.slice(1).map(Number),F=z[0],re=z[1],fe,be=g.x1,Te=g.y1,Ge=g.x,$e=g.y;switch("TQ".includes(I)||(g.qx=null,g.qy=null),I){case"M":return g.x=F,g.y=re,c;case"A":return fe=[be,Te].concat(z),["C"].concat(Mt(fe[0],fe[1],fe[2],fe[3],fe[4],fe[5],fe[6],fe[7],fe[8],fe[9]));case"Q":return g.qx=F,g.qy=re,fe=[be,Te].concat(z),["C"].concat(jt(fe[0],fe[1],fe[2],fe[3],fe[4],fe[5]));case"L":return["C"].concat(Xt(be,Te,F,re));case"Z":return be===Ge&&Te===$e?["C",be,Te,Ge,$e,Ge,$e]:["C"].concat(Xt(be,Te,Ge,$e));default:}return c}function Z(c,g){if(g===void 0&&(g=!1),tt(c)){var I=[].concat(c);return g?[I,[]]:I}for(var z=Ue(c),F=(0,P.__assign)({},N),re=[],fe="",be=z.length,Te,Ge,$e=[],nt=0;nt<be;nt+=1){z[nt]&&(fe=z[nt][0]),re[nt]=fe;var et=Jt(z[nt],F);z[nt]=et,U(z,re,nt),be=z.length,fe==="Z"&&$e.push(nt),Te=z[nt],Ge=Te.length,F.x1=+Te[Ge-2],F.y1=+Te[Ge-1],F.x2=+Te[Ge-4]||F.x1,F.y2=+Te[Ge-3]||F.y1}return g?[z,$e]:z}function J(c){return Pe(c)}function ie(c){return c.map(function(g){return Array.isArray(g)?[].concat(g):g})}function me(c){var g=c.slice(1).map(function(I,z,F){return z?F[z-1].slice(-2).concat(I.slice(1)):c[0].slice(1).concat(I.slice(1))}).map(function(I){return I.map(function(z,F){return I[I.length-F-2*(1-F%2)]})}).reverse();return[["M"].concat(g[0].slice(0,2))].concat(g.map(function(I){return["C"].concat(I.slice(2))}))}function Oe(c,g){return Math.sqrt((c[0]-g[0])*(c[0]-g[0])+(c[1]-g[1])*(c[1]-g[1]))}function Ce(c,g,I,z,F){var re=Oe([c,g],[I,z]),fe={x:0,y:0};if(typeof F=="number")if(F<=0)fe={x:c,y:g};else if(F>=re)fe={x:I,y:z};else{var be=Ct([c,g],[I,z],F/re),Te=be[0],Ge=be[1];fe={x:Te,y:Ge}}return{length:re,point:fe,min:{x:Math.min(c,I),y:Math.min(g,z)},max:{x:Math.max(c,I),y:Math.max(g,z)}}}function Ne(c,g){var I=c.x,z=c.y,F=g.x,re=g.y,fe=I*F+z*re,be=Math.sqrt((Math.pow(I,2)+Math.pow(z,2))*(Math.pow(F,2)+Math.pow(re,2))),Te=I*re-z*F<0?-1:1,Ge=Te*Math.acos(fe/be);return Ge}function Y(c,g,I,z,F,re,fe,be,Te,Ge){var $e=Math.abs,nt=Math.sin,et=Math.cos,yt=Math.sqrt,_t=Math.PI,xt=$e(I),ht=$e(z),St=(F%360+360)%360,ot=St*(_t/180);if(c===be&&g===Te)return{x:c,y:g};if(xt===0||ht===0)return Ce(c,g,be,Te,Ge).point;var bt=(c-be)/2,mt=(g-Te)/2,ut={x:et(ot)*bt+nt(ot)*mt,y:-nt(ot)*bt+et(ot)*mt},Dt=Math.pow(ut.x,2)/Math.pow(xt,2)+Math.pow(ut.y,2)/Math.pow(ht,2);Dt>1&&(xt*=yt(Dt),ht*=yt(Dt));var Lt=Math.pow(xt,2)*Math.pow(ht,2)-Math.pow(xt,2)*Math.pow(ut.y,2)-Math.pow(ht,2)*Math.pow(ut.x,2),Pt=Math.pow(xt,2)*Math.pow(ut.y,2)+Math.pow(ht,2)*Math.pow(ut.x,2),zt=Lt/Pt;zt=zt<0?0:zt;var Nt=(re!==fe?1:-1)*yt(zt),Ft={x:Nt*(xt*ut.y/ht),y:Nt*(-(ht*ut.x)/xt)},Ht={x:et(ot)*Ft.x-nt(ot)*Ft.y+(c+be)/2,y:nt(ot)*Ft.x+et(ot)*Ft.y+(g+Te)/2},Yt={x:(ut.x-Ft.x)/xt,y:(ut.y-Ft.y)/ht},Sr=Ne({x:1,y:0},Yt),Hn={x:(-ut.x-Ft.x)/xt,y:(-ut.y-Ft.y)/ht},pr=Ne(Yt,Hn);!fe&&pr>0?pr-=2*_t:fe&&pr<0&&(pr+=2*_t),pr%=2*_t;var Wr=Sr+pr*Ge,Ur=xt*et(Wr),Vr=ht*nt(Wr),Yn={x:et(ot)*Ur-nt(ot)*Vr+Ht.x,y:nt(ot)*Ur+et(ot)*Vr+Ht.y};return Yn}function $(c,g,I,z,F,re,fe,be,Te,Ge,$e){var nt,et=$e.bbox,yt=et===void 0?!0:et,_t=$e.length,xt=_t===void 0?!0:_t,ht=$e.sampleSize,St=ht===void 0?30:ht,ot=typeof Ge=="number",bt=c,mt=g,ut=0,Dt=[bt,mt,ut],Lt=[bt,mt],Pt=0,zt={x:0,y:0},Nt=[{x:bt,y:mt}];ot&&Ge<=0&&(zt={x:bt,y:mt});for(var Ft=0;Ft<=St;Ft+=1){if(Pt=Ft/St,nt=Y(c,g,I,z,F,re,fe,be,Te,Pt),bt=nt.x,mt=nt.y,yt&&Nt.push({x:bt,y:mt}),xt&&(ut+=Oe(Lt,[bt,mt])),Lt=[bt,mt],ot&&ut>=Ge&&Ge>Dt[2]){var Ht=(ut-Ge)/(ut-Dt[2]);zt={x:Lt[0]*(1-Ht)+Dt[0]*Ht,y:Lt[1]*(1-Ht)+Dt[1]*Ht}}Dt=[bt,mt,ut]}return ot&&Ge>=ut&&(zt={x:be,y:Te}),{length:ut,point:zt,min:{x:Math.min.apply(null,Nt.map(function(Yt){return Yt.x})),y:Math.min.apply(null,Nt.map(function(Yt){return Yt.y}))},max:{x:Math.max.apply(null,Nt.map(function(Yt){return Yt.x})),y:Math.max.apply(null,Nt.map(function(Yt){return Yt.y}))}}}function Ie(c,g,I,z,F,re,fe,be,Te){var Ge=1-Te;return{x:Math.pow(Ge,3)*c+3*Math.pow(Ge,2)*Te*I+3*Ge*Math.pow(Te,2)*F+Math.pow(Te,3)*fe,y:Math.pow(Ge,3)*g+3*Math.pow(Ge,2)*Te*z+3*Ge*Math.pow(Te,2)*re+Math.pow(Te,3)*be}}function _e(c,g,I,z,F,re,fe,be,Te,Ge){var $e,nt=Ge.bbox,et=nt===void 0?!0:nt,yt=Ge.length,_t=yt===void 0?!0:yt,xt=Ge.sampleSize,ht=xt===void 0?10:xt,St=typeof Te=="number",ot=c,bt=g,mt=0,ut=[ot,bt,mt],Dt=[ot,bt],Lt=0,Pt={x:0,y:0},zt=[{x:ot,y:bt}];St&&Te<=0&&(Pt={x:ot,y:bt});for(var Nt=0;Nt<=ht;Nt+=1){if(Lt=Nt/ht,$e=Ie(c,g,I,z,F,re,fe,be,Lt),ot=$e.x,bt=$e.y,et&&zt.push({x:ot,y:bt}),_t&&(mt+=Oe(Dt,[ot,bt])),Dt=[ot,bt],St&&mt>=Te&&Te>ut[2]){var Ft=(mt-Te)/(mt-ut[2]);Pt={x:Dt[0]*(1-Ft)+ut[0]*Ft,y:Dt[1]*(1-Ft)+ut[1]*Ft}}ut=[ot,bt,mt]}return St&&Te>=mt&&(Pt={x:fe,y:be}),{length:mt,point:Pt,min:{x:Math.min.apply(null,zt.map(function(Ht){return Ht.x})),y:Math.min.apply(null,zt.map(function(Ht){return Ht.y}))},max:{x:Math.max.apply(null,zt.map(function(Ht){return Ht.x})),y:Math.max.apply(null,zt.map(function(Ht){return Ht.y}))}}}function Ae(c,g,I,z,F,re,fe){var be=1-fe;return{x:Math.pow(be,2)*c+2*be*fe*I+Math.pow(fe,2)*F,y:Math.pow(be,2)*g+2*be*fe*z+Math.pow(fe,2)*re}}function Be(c,g,I,z,F,re,fe,be){var Te,Ge=be.bbox,$e=Ge===void 0?!0:Ge,nt=be.length,et=nt===void 0?!0:nt,yt=be.sampleSize,_t=yt===void 0?10:yt,xt=typeof fe=="number",ht=c,St=g,ot=0,bt=[ht,St,ot],mt=[ht,St],ut=0,Dt={x:0,y:0},Lt=[{x:ht,y:St}];xt&&fe<=0&&(Dt={x:ht,y:St});for(var Pt=0;Pt<=_t;Pt+=1){if(ut=Pt/_t,Te=Ae(c,g,I,z,F,re,ut),ht=Te.x,St=Te.y,$e&&Lt.push({x:ht,y:St}),et&&(ot+=Oe(mt,[ht,St])),mt=[ht,St],xt&&ot>=fe&&fe>bt[2]){var zt=(ot-fe)/(ot-bt[2]);Dt={x:mt[0]*(1-zt)+bt[0]*zt,y:mt[1]*(1-zt)+bt[1]*zt}}bt=[ht,St,ot]}return xt&&fe>=ot&&(Dt={x:F,y:re}),{length:ot,point:Dt,min:{x:Math.min.apply(null,Lt.map(function(Nt){return Nt.x})),y:Math.min.apply(null,Lt.map(function(Nt){return Nt.y}))},max:{x:Math.max.apply(null,Lt.map(function(Nt){return Nt.x})),y:Math.max.apply(null,Lt.map(function(Nt){return Nt.y}))}}}function ze(c,g,I){for(var z,F,re,fe,be,Te,Ge=Ue(c),$e=typeof g=="number",nt,et=[],yt,_t=0,xt=0,ht=0,St=0,ot,bt=[],mt=[],ut=0,Dt={x:0,y:0},Lt=Dt,Pt=Dt,zt=Dt,Nt=0,Ft=0,Ht=Ge.length;Ft<Ht;Ft+=1)ot=Ge[Ft],yt=ot[0],nt=yt==="M",et=nt?et:[_t,xt].concat(ot.slice(1)),nt?(ht=ot[1],St=ot[2],Dt={x:ht,y:St},Lt=Dt,ut=0,$e&&g<.001&&(zt=Dt)):yt==="L"?(z=Ce(et[0],et[1],et[2],et[3],(g||0)-Nt),ut=z.length,Dt=z.min,Lt=z.max,Pt=z.point):yt==="A"?(F=$(et[0],et[1],et[2],et[3],et[4],et[5],et[6],et[7],et[8],(g||0)-Nt,I||{}),ut=F.length,Dt=F.min,Lt=F.max,Pt=F.point):yt==="C"?(re=_e(et[0],et[1],et[2],et[3],et[4],et[5],et[6],et[7],(g||0)-Nt,I||{}),ut=re.length,Dt=re.min,Lt=re.max,Pt=re.point):yt==="Q"?(fe=Be(et[0],et[1],et[2],et[3],et[4],et[5],(g||0)-Nt,I||{}),ut=fe.length,Dt=fe.min,Lt=fe.max,Pt=fe.point):yt==="Z"&&(et=[_t,xt,ht,St],be=Ce(et[0],et[1],et[2],et[3],(g||0)-Nt),ut=be.length,Dt=be.min,Lt=be.max,Pt=be.point),$e&&Nt<g&&Nt+ut>=g&&(zt=Pt),mt.push(Lt),bt.push(Dt),Nt+=ut,Te=yt!=="Z"?ot.slice(-2):[ht,St],_t=Te[0],xt=Te[1];return $e&&g>=Nt&&(zt={x:_t,y:xt}),{length:Nt,point:zt,min:{x:Math.min.apply(null,bt.map(function(Yt){return Yt.x})),y:Math.min.apply(null,bt.map(function(Yt){return Yt.y}))},max:{x:Math.max.apply(null,mt.map(function(Yt){return Yt.x})),y:Math.max.apply(null,mt.map(function(Yt){return Yt.y}))}}}function Ye(c,g){if(!c)return{x:0,y:0,width:0,height:0,x2:0,y2:0,cx:0,cy:0,cz:0};var I=ze(c,void 0,(0,P.__assign)((0,P.__assign)({},g),{length:!1})),z=I.min,F=z.x,re=z.y,fe=I.max,be=fe.x,Te=fe.y,Ge=be-F,$e=Te-re;return{width:Ge,height:$e,x:F,y:re,x2:be,y2:Te,cx:F+Ge/2,cy:re+$e/2,cz:Math.max(Ge,$e)+Math.min(Ge,$e)/2}}function Ve(c,g){return ze(c,void 0,(0,P.__assign)((0,P.__assign)({},g),{bbox:!1,length:!0})).length}function qe(c,g){if(!c)return{length:0,x:0,y:0,width:0,height:0,x2:0,y2:0,cx:0,cy:0,cz:0};var I=ze(c,void 0,(0,P.__assign)((0,P.__assign)({},g),{bbox:!0,length:!0})),z=I.length,F=I.min,re=F.x,fe=F.y,be=I.max,Te=be.x,Ge=be.y,$e=Te-re,nt=Ge-fe;return{length:z,width:$e,height:nt,x:re,y:fe,x2:Te,y2:Ge,cx:re+$e/2,cy:fe+nt/2,cz:Math.max($e,nt)+Math.min($e,nt)/2}}function je(c){var g=c.length,I=g-1;return c.map(function(z,F){return c.map(function(re,fe){var be=F+fe,Te;return fe===0||c[be]&&c[be][0]==="M"?(Te=c[be],["M"].concat(Te.slice(-2))):(be>=g&&(be-=I),c[be])})})}function at(c,g){var I=c.length-1,z=[],F=0,re=0,fe=je(c);return fe.forEach(function(be,Te){c.slice(1).forEach(function(Ge,$e){re+=Oe(c[(Te+$e)%I].slice(-2),g[$e%I].slice(-2))}),z[Te]=re,re=0}),F=z.indexOf(Math.min.apply(null,z)),fe[F]}function vt(c,g,I,z,F,re,fe,be){return 3*((be-g)*(I+F)-(fe-c)*(z+re)+z*(c-F)-I*(g-re)+be*(F+c/3)-fe*(re+g/3))/20}function ct(c){var g=0,I=0,z=0;return Z(c).map(function(F){var re;switch(F[0]){case"M":return g=F[1],I=F[2],0;default:var fe=F.slice(1),be=fe[0],Te=fe[1],Ge=fe[2],$e=fe[3],nt=fe[4],et=fe[5];return z=vt(g,I,be,Te,Ge,$e,nt,et),re=F.slice(-2),g=re[0],I=re[1],z}}).reduce(function(F,re){return F+re},0)}function st(c){return ct(c)>=0}function gt(c,g,I){return ze(c,g,(0,P.__assign)((0,P.__assign)({},I),{bbox:!1,length:!0})).point}function Et(c,g){var I=Pe(c);if(typeof I=="string")throw TypeError(I);var z=I.slice(),F=Ve(z),re=z.length-1,fe=0,be=0,Te=I[0],Ge=Te.slice(-2),$e=Ge[0],nt=Ge[1],et={x:$e,y:nt};if(re<=0||!g||!Number.isFinite(g))return{segment:Te,index:0,length:be,point:et,lengthAtSegment:fe};if(g>=F)return z=I.slice(0,-1),fe=Ve(z),be=F-fe,{segment:I[re],index:re,length:be,lengthAtSegment:fe};for(var yt=[];re>0;)Te=z[re],z=z.slice(0,-1),fe=Ve(z),be=F-fe,F=fe,yt.push({segment:Te,index:re,length:be,lengthAtSegment:fe}),re-=1;return yt.find(function(_t){var xt=_t.lengthAtSegment;return xt<=g})}function Vt(c,g){for(var I=Pe(c),z=Ue(I),F=Ve(I),re=function(ut){var Dt=ut.x-g.x,Lt=ut.y-g.y;return Dt*Dt+Lt*Lt},fe=8,be,Te=0,Ge,$e=0,nt=1/0,et=0;et<=F;et+=fe)be=gt(z,et),Te=re(be),Te<nt&&(Ge=be,$e=et,nt=Te);fe/=2;for(var yt,_t,xt=0,ht=0,St=0,ot=0;fe>.5;)xt=$e-fe,yt=gt(z,xt),St=re(yt),ht=$e+fe,_t=gt(z,ht),ot=re(_t),xt>=0&&St<nt?(Ge=yt,$e=xt,nt=St):ht<=F&&ot<nt?(Ge=_t,$e=ht,nt=ot):fe/=2;var bt=Et(I,$e),mt=Math.sqrt(nt);return{closest:Ge,distance:mt,segment:bt}}function qt(c,g){var I=Vt(c,g).distance;return Math.abs(I)<.001}function Kt(c,g){g===void 0&&(g=.5);var I=c.slice(0,2),z=c.slice(2,4),F=c.slice(4,6),re=c.slice(6,8),fe=Ct(I,z,g),be=Ct(z,F,g),Te=Ct(F,re,g),Ge=Ct(fe,be,g),$e=Ct(be,Te,g),nt=Ct(Ge,$e,g);return[["C"].concat(fe,Ge,nt),["C"].concat($e,Te,re)]}function $t(c){return c.map(function(g,I,z){var F=I&&z[I-1].slice(-2).concat(g.slice(1)),re=I?_e(F[0],F[1],F[2],F[3],F[4],F[5],F[6],F[7],F[8],{bbox:!1}).length:0,fe;return I?fe=re?Kt(F):[g,g]:fe=[g],{s:g,ss:fe,l:re}})}function or(c,g,I){var z=$t(c),F=$t(g),re=z.length,fe=F.length,be=z.filter(function(ht){return ht.l}).length,Te=F.filter(function(ht){return ht.l}).length,Ge=z.filter(function(ht){return ht.l}).reduce(function(ht,St){var ot=St.l;return ht+ot},0)/be||0,$e=F.filter(function(ht){return ht.l}).reduce(function(ht,St){var ot=St.l;return ht+ot},0)/Te||0,nt=I||Math.max(re,fe),et=[Ge,$e],yt=[nt-re,nt-fe],_t=0,xt=[z,F].map(function(ht,St){return ht.l===nt?ht.map(function(ot){return ot.s}):ht.map(function(ot,bt){return _t=bt&&yt[St]&&ot.l>=et[St],yt[St]-=_t?1:0,_t?ot.ss:[ot.s]}).flat()});return xt[0].length===xt[1].length?xt:or(xt[0],xt[1],nt)}var Zt=function(c){return c!==null&&typeof c!="function"&&isFinite(c.length)},Rt=Zt,cr=function(c,g){return Rt(c)?c.indexOf(g)>-1:!1},er=cr,fr=function(c,g){if(!Rt(c))return c;for(var I=[],z=0;z<c.length;z++){var F=c[z];g(F,z)&&I.push(F)}return I},W=fr,H=function(c,g){return g===void 0&&(g=[]),W(c,function(I){return!er(g,I)})},X=H;function le(c){return typeof c=="function"}function ye(c){return c==null}function xe(c){return Array.isArray(c)}var Fe=function(c){var g=typeof c;return c!==null&&g==="object"||g==="function"};function We(c,g){if(c){var I;if(xe(c))for(var z=0,F=c.length;z<F&&(I=g(c[z],z),I!==!1);z++);else if(Fe(c)){for(var re in c)if(c.hasOwnProperty(re)&&(I=g(c[re],re),I===!1))break}}}var it=We,Qe=Object.keys?function(c){return Object.keys(c)}:function(c){var g=[];return it(c,function(I,z){le(c)&&z==="prototype"||g.push(z)}),g},rt=Qe;function kt(c,g){var I=rt(g),z=I.length;if(ye(c))return!z;for(var F=0;F<z;F+=1){var re=I[F];if(g[re]!==c[re]||!(re in c))return!1}return!0}var Gt=kt,Wt=function(c){return typeof c=="object"&&c!==null},Ot=Wt,tr={}.toString,nr=function(c,g){return tr.call(c)==="[object "+g+"]"},ar=nr,rr=function(c){if(!Ot(c)||!ar(c,"Object"))return!1;if(Object.getPrototypeOf(c)===null)return!0;for(var g=c;Object.getPrototypeOf(g)!==null;)g=Object.getPrototypeOf(g);return Object.getPrototypeOf(c)===g},ee=rr;function L(c,g){if(!xe(c))return null;var I;if(le(g)&&(I=g),ee(g)&&(I=function(F){return Gt(F,g)}),I){for(var z=0;z<c.length;z+=1)if(I(c[z]))return c[z]}return null}var j=L;function te(c,g,I){I===void 0&&(I=0);for(var z=I;z<c.length;z++)if(g(c[z],z))return z;return-1}var Q=te,oe=function(c,g){for(var I=null,z=0;z<c.length;z++){var F=c[z],re=F[g];if(!ye(re)){xe(re)?I=re[0]:I=re;break}}return I},Ee=oe,Re=function(c){if(!xe(c))return[];for(var g=[],I=0;I<c.length;I++)g=g.concat(c[I]);return g},Xe=Re,ft=function(c,g){if(g===void 0&&(g=[]),!xe(c))g.push(c);else for(var I=0;I<c.length;I+=1)ft(c[I],g);return g},dt=ft;function wt(c){if(!Array.isArray(c))return-1/0;var g=c.length;if(!g)return-1/0;for(var I=c[0],z=1;z<g;z++)I=Math.max(I,c[z]);return I}var It=function(c){if(xe(c))return c.reduce(function(g,I){return Math.min(g,I)},c[0])},At=function(c){var g=c.filter(function(fe){return!isNaN(fe)});if(!g.length)return{min:0,max:0};if(xe(c[0])){for(var I=[],z=0;z<c.length;z++)I=I.concat(c[z]);g=I}var F=wt(g),re=It(g);return{min:re,max:F}},Ut=At,Bt=Array.prototype,Qt=Bt.splice,vr=Bt.indexOf,Or=function(c){for(var g=[],I=1;I<arguments.length;I++)g[I-1]=arguments[I];for(var z=0;z<g.length;z++)for(var F=g[z],re=-1;(re=vr.call(c,F))>-1;)Qt.call(c,re,1);return c},br=Or,Hr=Array.prototype.splice,Nr=function(g,I){if(!Rt(g))return[];for(var z=g?I.length:0,F=z-1;z--;){var re=void 0,fe=I[z];(z===F||fe!==re)&&(re=fe,Hr.call(g,fe,1))}return g},Cr=Nr,Yr=function(c,g,I){if(!xe(c)&&!ee(c))return c;var z=I;return it(c,function(F,re){z=g(z,F,re)}),z},kr=Yr,Xr=function(c,g){var I=[];if(!Rt(c))return I;for(var z=-1,F=[],re=c.length;++z<re;){var fe=c[z];g(fe,z,c)&&(I.push(fe),F.push(z))}return Cr(c,F),I},Zr=Xr;function dr(c){return typeof c=="string"}function Kr(c,g){var I;if(le(g))I=function(F,re){return g(F)-g(re)};else{var z=[];dr(g)?z.push(g):xe(g)&&(z=g),I=function(F,re){for(var fe=0;fe<z.length;fe+=1){var be=z[fe];if(F[be]>re[be])return 1;if(F[be]<re[be])return-1}return 0}}return c.sort(I),c}var Qr=Kr;function Tr(c,g){g===void 0&&(g=new Map);var I=[];if(Array.isArray(c))for(var z=0,F=c.length;z<F;z++){var re=c[z];g.has(re)||(I.push(re),g.set(re,!0))}return I}var Jr=function(){for(var c=[],g=0;g<arguments.length;g++)c[g]=arguments[g];return Tr([].concat.apply([],c))},qr=Jr,$r=function(c,g){for(var I=[],z={},F=0;F<c.length;F++){var re=c[F],fe=re[g];if(!ye(fe)){xe(fe)||(fe=[fe]);for(var be=0;be<fe.length;be++){var Te=fe[be];z[Te]||(I.push(Te),z[Te]=!0)}}}return I};function en(c){if(Rt(c))return c[0]}function Lr(c){if(Rt(c)){var g=c;return g[g.length-1]}}function tn(c,g){return xe(c)||dr(c)?c[0]===g:!1}var rn=tn;function nn(c,g){return xe(c)||dr(c)?c[c.length-1]===g:!1}var an=nn,on=function(c,g){for(var I=0;I<c.length;I++)if(!g(c[I],I))return!1;return!0},un=on,cn=function(c,g){for(var I=0;I<c.length;I++)if(g(c[I],I))return!0;return!1},fn=cn,Mr=Object.prototype.hasOwnProperty;function hr(c,g){if(!g||!xe(c))return{};for(var I={},z=le(g)?g:function(be){return be[g]},F,re=0;re<c.length;re++){var fe=c[re];F=z(fe),Mr.call(I,F)?I[F].push(fe):I[F]=[fe]}return I}var zr=hr;function Ar(c,g){if(!g)return{0:c};if(!le(g)){var I=xe(g)?g:g.replace(/\s+/g,"").split("*");g=function(z){for(var F="_",re=0,fe=I.length;re<fe;re++)F+=z[I[re]]&&z[I[re]].toString();return F}}return zr(c,g)}var Er=function(c,g){if(!g)return[c];var I=Ar(c,g),z=[];for(var F in I)z.push(I[F]);return z};function sn(c,g){return c["_wrap_"+g]}var ln=sn;function vn(c,g){if(c["_wrap_"+g])return c["_wrap_"+g];var I=function(z){c[g](z)};return c["_wrap_"+g]=I,I}var Pr=vn,Gr={};function Fr(c){var g=Gr[c];if(!g){for(var I=c.toString(16),z=I.length;z<6;z++)I="0"+I;g="#"+I,Gr[c]=g}return g}var dn=Fr;function hn(c){var g=0,I=0,z=0,F=0;return xe(c)?c.length===1?g=I=z=F=c[0]:c.length===2?(g=z=c[0],I=F=c[1]):c.length===3?(g=c[0],I=F=c[1],z=c[2]):(g=c[0],I=c[1],z=c[2],F=c[3]):g=I=z=F=c,{r1:g,r2:I,r3:z,r4:F}}var pn=hn,gn=function(c,g,I){return c<g?g:c>I?I:c},yn=gn,mn=function(c,g){var I=g.toString(),z=I.indexOf(".");if(z===-1)return Math.round(c);var F=I.substr(z+1).length;return F>20&&(F=20),parseFloat(c.toFixed(F))},xn=mn;function lr(c){return typeof c=="number"}function wn(c){return lr(c)&&c%1!==0}function Dr(c){return lr(c)&&c%2===0}function Rr(c){return lr(c)&&c%1===0}function _n(c){return lr(c)&&c<0}var bn=1e-5;function Mn(c,g,I){return I===void 0&&(I=bn),c===g||Math.abs(c-g)<I}function En(c){return lr(c)&&c%2!==0}var Dn=function(c){return lr(c)&&c>0},In=Dn,Sn=function(c,g){if(xe(c)){for(var I,z=-1/0,F=0;F<c.length;F++){var re=c[F],fe=le(g)?g(re):re[g];fe>z&&(I=re,z=fe)}return I}},On=function(c,g){if(xe(c)){for(var I,z=1/0,F=0;F<c.length;F++){var re=c[F],fe=le(g)?g(re):re[g];fe<z&&(I=re,z=fe)}return I}},Nn=function(c,g){return(c%g+g)%g},Cn=Nn,kn=180/Math.PI,Tn=function(c){return kn*c},xr=Tn,Ln=parseInt,zn=Math.PI/180,An=function(c){return zn*c},Pn=An,Gn=it,yr=function(c,g){return c.hasOwnProperty(g)},mr=yr,Fn=Object.values?function(c){return Object.values(c)}:function(c){var g=[];return it(c,function(I,z){le(c)&&z==="prototype"||g.push(I)}),g},Br=Fn,Rn=function(c,g){return er(Br(c),g)},O=function(c){return ye(c)?"":c.toString()},V=function(c){return O(c).toLowerCase()},ce=V,ge=function(c){var g=O(c);return g.charAt(0).toLowerCase()+g.substring(1)},ke=ge;function Ke(c,g){return!c||!g?c:c.replace(/\\?\{([^{}]+)\}/g,function(I,z){return I.charAt(0)==="\\"?I.slice(1):g[z]===void 0?"":g[z]})}var lt=Ke,Tt=function(c){return O(c).toUpperCase()},ir=Tt,ur=function(c){var g=O(c);return g.charAt(0).toUpperCase()+g.substring(1)},sr=ur,Ir={}.toString,ua=function(c){return Ir.call(c).replace(/^\[object /,"").replace(/]$/,"")},Xn=ua,ca=function(c){return ar(c,"Arguments")},fa=ca,sa=function(c){return ar(c,"Boolean")},la=sa;function va(c){return c instanceof Date}var da=function(c){return ar(c,"Error")},ha=da;function pa(c){return lr(c)&&isFinite(c)}function ga(c){return c===null}var ya=Object.prototype,ma=function(c){var g=c&&c.constructor,I=typeof g=="function"&&g.prototype||ya;return c===I},Zn=ma,xa=function(c){return ar(c,"RegExp")},wa=xa,_a=function(c){return c===void 0},ba=_a;function Ma(c){return c instanceof Element||c instanceof Document}function Ea(c){var g=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(I){return setTimeout(I,16)};return g(c)}function Da(c){var g=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.msCancelAnimationFrame||clearTimeout;g(c)}function Bn(c,g){for(var I in g)g.hasOwnProperty(I)&&I!=="constructor"&&g[I]!==void 0&&(c[I]=g[I])}function wr(c,g,I,z){return g&&Bn(c,g),I&&Bn(c,I),z&&Bn(c,z),c}var Ia=function(){for(var c=[],g=0;g<arguments.length;g++)c[g]=arguments[g];for(var I=c[0],z=1;z<c.length;z++){var F=c[z];le(F)&&(F=F.prototype),wr(I.prototype,F)}},Sa=Ia,jn=function(c){if(typeof c!="object"||c===null)return c;var g;if(xe(c)){g=[];for(var I=0,z=c.length;I<z;I++)typeof c[I]=="object"&&c[I]!=null?g[I]=jn(c[I]):g[I]=c[I]}else{g={};for(var F in c)typeof c[F]=="object"&&c[F]!=null?g[F]=jn(c[F]):g[F]=c[F]}return g},Oa=jn;function Na(c,g,I){var z;return function(){var F=this,re=arguments,fe=function(){z=null,I||c.apply(F,re)},be=I&&!z;clearTimeout(z),z=setTimeout(fe,g),be&&c.apply(F,re)}}var Ca=Na,ka=5;function Ta(c,g){if(Object.hasOwn)return Object.hasOwn(c,g);if(c==null)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(c),g)}function Kn(c,g,I,z){I=I||0,z=z||ka;for(var F in g)if(Ta(g,F)){var re=g[F];re!==null&&ee(re)?(ee(c[F])||(c[F]={}),I<z?Kn(c[F],re,I+1,z):c[F]=g[F]):xe(re)?(c[F]=[],c[F]=c[F].concat(re)):re!==void 0&&(c[F]=re)}}var La=function(c){for(var g=[],I=1;I<arguments.length;I++)g[I-1]=arguments[I];for(var z=0;z<g.length;z+=1)Kn(c,g[z]);return c},za=La,Aa=function(c,g,I,z){le(g)||(I=g,g=c,c=function(){});var F=Object.create?function(fe,be){return Object.create(fe,{constructor:{value:be}})}:function(fe,be){function Te(){}Te.prototype=fe;var Ge=new Te;return Ge.constructor=be,Ge},re=F(g.prototype,c);return c.prototype=wr(re,c.prototype),c.superclass=F(g.prototype,g),wr(re,I),wr(c,z),c},Pa=Aa,Ga=function(c,g){if(!Rt(c))return-1;var I=Array.prototype.indexOf;if(I)return I.call(c,g);for(var z=-1,F=0;F<c.length;F++)if(c[F]===g){z=F;break}return z},Fa=Ga,Ra=Object.prototype.hasOwnProperty;function Ba(c){if(ye(c))return!0;if(Rt(c))return!c.length;var g=Xn(c);if(g==="Map"||g==="Set")return!c.size;if(Zn(c))return!Object.keys(c).length;for(var I in c)if(Ra.call(c,I))return!1;return!0}var ja=Ba,Wn=function(c,g){if(c===g)return!0;if(!c||!g||dr(c)||dr(g))return!1;if(Rt(c)||Rt(g)){if(c.length!==g.length)return!1;for(var I=!0,z=0;z<c.length&&(I=Wn(c[z],g[z]),!!I);z++);return I}if(Ot(c)||Ot(g)){var F=Object.keys(c),re=Object.keys(g);if(F.length!==re.length)return!1;for(var I=!0,z=0;z<F.length&&(I=Wn(c[F[z]],g[F[z]]),!!I);z++);return I}return!1},Qn=Wn,Wa=function(c,g,I){return le(I)?!!I(c,g):Qn(c,g)},Ua=function(c,g){if(!Rt(c))return c;for(var I=[],z=0;z<c.length;z++){var F=c[z];I.push(g(F,z))}return I},Va=Ua,Ha=function(c){return c},Ya=function(c,g){g===void 0&&(g=Ha);var I={};return Fe(c)&&!ye(c)&&Object.keys(c).forEach(function(z){I[z]=g(c[z],z)}),I},Xa=function(c,g,I){for(var z=0,F=dr(g)?g.split("."):g;c&&z<F.length;)c=c[F[z++]];return c===void 0||z<F.length?I:c},Za=function(c,g,I){var z=c,F=dr(g)?g.split("."):g;return F.forEach(function(re,fe){fe<F.length-1?(Fe(z[re])||(z[re]=lr(F[fe+1])?[]:{}),z=z[re]):z[re]=I}),c},Ka=Object.prototype.hasOwnProperty,Qa=function(c,g){if(c===null||!ee(c))return{};var I={};return it(g,function(z){Ka.call(c,z)&&(I[z]=c[z])}),I},Ja=function(c,g){return kr(c,function(I,z,F){return g.includes(F)||(I[F]=z),I},{})},qa=function(c,g,I){var z,F,re,fe,be=0;I||(I={});var Te=function(){be=I.leading===!1?0:Date.now(),z=null,fe=c.apply(F,re),z||(F=re=null)},Ge=function(){var $e=Date.now();!be&&I.leading===!1&&(be=$e);var nt=g-($e-be);return F=this,re=arguments,nt<=0||nt>g?(z&&(clearTimeout(z),z=null),be=$e,fe=c.apply(F,re),z||(F=re=null)):!z&&I.trailing!==!1&&(z=setTimeout(Te,nt)),fe};return Ge.cancel=function(){clearTimeout(z),be=0,z=F=re=null},Ge},$a=function(c){return Rt(c)?Array.prototype.slice.call(c):[]},jr={},ei=function(c){return c=c||"g",jr[c]?jr[c]+=1:jr[c]=1,c+jr[c]},ti=function(){},ri=function(c){return c};function ni(c){return ye(c)?0:Rt(c)?c.length:Object.keys(c).length}var ai=function(){function c(){this.map={}}return c.prototype.has=function(g){return this.map[g]!==void 0},c.prototype.get=function(g,I){var z=this.map[g];return z===void 0?I:z},c.prototype.set=function(g,I){this.map[g]=I},c.prototype.clear=function(){this.map={}},c.prototype.delete=function(g){delete this.map[g]},c.prototype.size=function(){return Object.keys(this.map).length},c}(),ii=ai,oi=1e-6;function Un(c){return Math.abs(c)<oi?0:c<0?-1:1}function ui(c,g,I){return(I[0]-c[0])*(g[1]-c[1])===(g[0]-c[0])*(I[1]-c[1])&&Math.min(c[0],g[0])<=I[0]&&I[0]<=Math.max(c[0],g[0])&&Math.min(c[1],g[1])<=I[1]&&I[1]<=Math.max(c[1],g[1])}function Vn(c,g,I){var z=!1,F=c.length;if(F<=2)return!1;for(var re=0;re<F;re++){var fe=c[re],be=c[(re+1)%F];if(ui(fe,be,[g,I]))return!0;Un(fe[1]-I)>0!=Un(be[1]-I)>0&&Un(g-(I-fe[1])*(fe[0]-be[0])/(fe[1]-be[1])-fe[0])<0&&(z=!z)}return z}var Jn=function(c,g,I){return c>=g&&c<=I};function ci(c,g,I,z){var F=.001,re={x:I.x-c.x,y:I.y-c.y},fe={x:g.x-c.x,y:g.y-c.y},be={x:z.x-I.x,y:z.y-I.y},Te=fe.x*be.y-fe.y*be.x,Ge=Te*Te,$e=fe.x*fe.x+fe.y*fe.y,nt=be.x*be.x+be.y*be.y,et=null;if(Ge>F*$e*nt){var yt=(re.x*be.y-re.y*be.x)/Te,_t=(re.x*fe.y-re.y*fe.x)/Te;Jn(yt,0,1)&&Jn(_t,0,1)&&(et={x:c.x+yt*fe.x,y:c.y+yt*fe.y})}return et}function qn(c){for(var g=[],I=c.length,z=0;z<I-1;z++){var F=c[z],re=c[z+1];g.push({from:{x:F[0],y:F[1]},to:{x:re[0],y:re[1]}})}if(g.length>1){var fe=c[0],be=c[I-1];g.push({from:{x:be[0],y:be[1]},to:{x:fe[0],y:fe[1]}})}return g}function fi(c,g){var I=!1;return c.forEach(function(z){if(ci(z.from,z.to,g.from,g.to))return I=!0,!1}),I}function $n(c){var g=c.map(function(z){return z[0]}),I=c.map(function(z){return z[1]});return{minX:Math.min.apply(null,g),maxX:Math.max.apply(null,g),minY:Math.min.apply(null,I),maxY:Math.max.apply(null,I)}}function si(c,g){return!(g.minX>c.maxX||g.maxX<c.minX||g.minY>c.maxY||g.maxY<c.minY)}function li(c,g){if(c.length<2||g.length<2)return!1;var I=$n(c),z=$n(g);if(!si(I,z))return!1;var F=!1;if(g.forEach(function(Te){if(Vn(c,Te[0],Te[1]))return F=!0,!1}),F||(c.forEach(function(Te){if(Vn(g,Te[0],Te[1]))return F=!0,!1}),F))return!0;var re=qn(c),fe=qn(g),be=!1;return fe.forEach(function(Te){if(fi(re,Te))return be=!0,!1}),be}function vi(c){var g=document.createElement("div");g.innerHTML=c;var I=g.childNodes[0];return I&&g.contains(I)&&g.removeChild(I),I}function di(c,g){if(c)return Object.keys(g).forEach(function(I){c.style[I]=g[I]}),c}}}]);
