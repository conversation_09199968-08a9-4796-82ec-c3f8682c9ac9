!(function(){var et=Object.defineProperty;var Qe=Object.getOwnPropertySymbols;var tt=Object.prototype.hasOwnProperty,nt=Object.prototype.propertyIsEnumerable;var Xe=(be,Q,L)=>Q in be?et(be,Q,{enumerable:!0,configurable:!0,writable:!0,value:L}):be[Q]=L,Ke=(be,Q)=>{for(var L in Q||(Q={}))tt.call(Q,L)&&Xe(be,L,Q[L]);if(Qe)for(var L of Qe(Q))nt.call(Q,L)&&Xe(be,L,Q[L]);return be};var Ge=(be,Q,L)=>Xe(be,typeof Q!="symbol"?Q+"":Q,L);(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[646],{76350:function(be,Q,L){"use strict";L.d(Q,{Il:function(){return j},Ov:function(){return W},T$:function(){return A}});var ee=L(18261),F=L(44194),K=L(45516),P=["element"],j=F.createContext({});function W(){return F.useContext(j)}function N(){var w=(0,K.TH)(),o=W(),m=o.clientRoutes,p=(0,K.fp)(m,w.pathname);return p||[]}function A(){var w,o=N().slice(-1),m=((w=o[0])===null||w===void 0?void 0:w.route)||{},p=m.element,b=(0,ee.Z)(m,P);return b}function _(){var w=N(),o=W(),m=o.serverLoaderData,p=o.basename,b=React.useState(function(){var v={},c=!1;return w.forEach(function(i){var f=m[i.route.id];f&&(Object.assign(v,f),c=!0)}),c?v:void 0}),d=_slicedToArray(b,2),O=d[0],T=d[1];return React.useEffect(function(){window.__UMI_LOADER_DATA__||Promise.all(w.filter(function(v){return v.route.hasServerLoader}).map(function(v){return new Promise(function(c){fetchServerLoader({id:v.route.id,basename:p,cb:c})})})).then(function(v){if(v.length){var c={};v.forEach(function(i){Object.assign(c,i)}),T(c)}})},[]),{data:O}}function R(){var w=useRouteData(),o=W();return{data:o.clientLoaderData[w.route.id]}}function g(){var w=_(),o=R();return{data:_objectSpread(_objectSpread({},w.data),o.data)}}},2714:function(be,Q,L){"use strict";L.d(Q,{p6:function(){return oe}});var ee=L(18260),F=L(42549),K=L(8658),P=L(44194),j=L(8082),W=L(45516),N=L(76350);function A(B){var X=B.id,se=B.basename,le=B.cb,pe=new URLSearchParams({route:X,url:window.location.href}).toString(),me="".concat(_(window.umiServerLoaderPath||se),"__serverLoader?").concat(pe);fetch(me,{credentials:"include"}).then(function(ge){return ge.json()}).then(le).catch(console.error)}function _(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return B.endsWith("/")?B:"".concat(B,"/")}var R=L(18261),g=L(16076),w=L(49338),o=["content"],m=["content"],p=/^(http:|https:)?\/\//;function b(B){return p.test(B)||B.startsWith("/")&&!B.startsWith("/*")||B.startsWith("./")||B.startsWith("../")}var d=function(){return P.createElement("noscript",{dangerouslySetInnerHTML:{__html:"<b>Enable JavaScript to run this app.</b>"}})},O=function(X){var se,le=X.loaderData,pe=X.htmlPageOpts,me=X.manifest,ge=(me==null||(se=me.assets)===null||se===void 0?void 0:se["umi.css"])||"";return P.createElement("script",{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:"window.__UMI_LOADER_DATA__ = ".concat(JSON.stringify(le||{}),"; window.__UMI_METADATA_LOADER_DATA__ = ").concat(JSON.stringify(pe||{}),"; window.__UMI_BUILD_ClIENT_CSS__ = '").concat(ge,"'")}})};function T(B){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(typeof B=="string")return b(B)?(0,F.Z)({src:B},X):{content:B};if((0,w.Z)(B)==="object")return(0,F.Z)((0,F.Z)({},B),X);throw new Error("Invalid script type: ".concat((0,w.Z)(B)))}function v(B){return b(B)?{type:"link",href:B}:{type:"style",content:B}}var c=function(X){var se,le,pe,me,ge,Ee,_e=X.htmlPageOpts;return P.createElement(P.Fragment,null,(_e==null?void 0:_e.title)&&P.createElement("title",null,_e.title),_e==null||(se=_e.favicons)===null||se===void 0?void 0:se.map(function(Ce,De){return P.createElement("link",{key:De,rel:"shortcut icon",href:Ce})}),(_e==null?void 0:_e.description)&&P.createElement("meta",{name:"description",content:_e.description}),(_e==null||(le=_e.keywords)===null||le===void 0?void 0:le.length)&&P.createElement("meta",{name:"keywords",content:_e.keywords.join(",")}),_e==null||(pe=_e.metas)===null||pe===void 0?void 0:pe.map(function(Ce){return P.createElement("meta",{key:Ce.name,name:Ce.name,content:Ce.content})}),_e==null||(me=_e.links)===null||me===void 0?void 0:me.map(function(Ce,De){return P.createElement("link",(0,g.Z)({key:De},Ce))}),_e==null||(ge=_e.styles)===null||ge===void 0?void 0:ge.map(function(Ce,De){var Ie=v(Ce),Me=Ie.type,Le=Ie.href,Pe=Ie.content;if(Me==="link")return P.createElement("link",{key:De,rel:"stylesheet",href:Le});if(Me==="style")return P.createElement("style",{key:De},Pe)}),_e==null||(Ee=_e.headScripts)===null||Ee===void 0?void 0:Ee.map(function(Ce,De){var Ie=T(Ce),Me=Ie.content,Le=(0,R.Z)(Ie,o);return P.createElement("script",(0,g.Z)({dangerouslySetInnerHTML:{__html:Me},key:De},Le))}))};function i(B){var X,se=B.children,le=B.loaderData,pe=B.manifest,me=B.htmlPageOpts,ge=B.__INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ee=B.mountElementId;if(ge!=null&&ge.pureHtml)return P.createElement("html",null,P.createElement("head",null),P.createElement("body",null,P.createElement(d,null),P.createElement("div",{id:Ee},se),P.createElement(O,{manifest:pe,loaderData:le,htmlPageOpts:me})));if(ge!=null&&ge.pureApp)return P.createElement(P.Fragment,null,se);var _e=typeof window=="undefined"?pe==null?void 0:pe.assets["umi.css"]:window.__UMI_BUILD_ClIENT_CSS__;return P.createElement("html",{suppressHydrationWarning:!0,lang:(me==null?void 0:me.lang)||"en"},P.createElement("head",null,P.createElement("meta",{charSet:"utf-8"}),P.createElement("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),_e&&P.createElement("link",{suppressHydrationWarning:!0,rel:"stylesheet",href:_e}),P.createElement(c,{htmlPageOpts:me})),P.createElement("body",null,P.createElement(d,null),P.createElement("div",{id:Ee},se),P.createElement(O,{manifest:pe,loaderData:le,htmlPageOpts:me}),me==null||(X=me.scripts)===null||X===void 0?void 0:X.map(function(Ce,De){var Ie=T(Ce),Me=Ie.content,Le=(0,R.Z)(Ie,m);return P.createElement("script",(0,g.Z)({dangerouslySetInnerHTML:{__html:Me},key:De},Le))})))}var f=P.createContext(void 0);function C(){return P.useContext(f)}var I=["redirect"];function S(B){var X=B.routesById,se=B.parentId,le=B.routeComponents,pe=B.useStream,me=pe===void 0?!0:pe;return Object.keys(X).filter(function(ge){return X[ge].parentId===se}).map(function(ge){var Ee=U((0,F.Z)((0,F.Z)({route:X[ge],routeComponent:le[ge],loadingComponent:B.loadingComponent,reactRouter5Compat:B.reactRouter5Compat},B.reactRouter5Compat&&{hasChildren:Object.keys(X).filter(function(Ce){return X[Ce].parentId===ge}).length>0}),{},{useStream:me})),_e=S({routesById:X,routeComponents:le,parentId:Ee.id,loadingComponent:B.loadingComponent,reactRouter5Compat:B.reactRouter5Compat,useStream:me});return _e.length>0&&(Ee.children=_e,Ee.routes=_e),Ee})}function G(B){var X=(0,W.UO)(),se=(0,W.Gn)(B.to,X),le=(0,N.T$)(),pe=(0,W.TH)();if(le!=null&&le.keepQuery){var me=pe.search+pe.hash;se+=me}var ge=(0,F.Z)((0,F.Z)({},B),{},{to:se});return P.createElement(W.Fg,(0,g.Z)({replace:!0},ge))}function U(B){var X=B.route,se=B.useStream,le=se===void 0?!0:se,pe=X.redirect,me=(0,R.Z)(X,I),ge=B.reactRouter5Compat?H:ae;return(0,F.Z)({element:pe?P.createElement(G,{to:pe}):P.createElement(f.Provider,{value:{route:B.route}},P.createElement(ge,{loader:P.memo(B.routeComponent),loadingComponent:B.loadingComponent||z,hasChildren:B.hasChildren,useStream:le}))},me)}function z(){return P.createElement("div",null)}function H(B){var X=C(),se=X.route,le=(0,N.Ov)(),pe=le.history,me=le.clientRoutes,ge=(0,W.UO)(),Ee={params:ge,isExact:!0,path:se.path,url:pe.location.pathname},_e=B.loader,Ce={location:pe.location,match:Ee,history:pe,params:ge,route:se,routes:me};return B.useStream?P.createElement(P.Suspense,{fallback:P.createElement(B.loadingComponent,null)},P.createElement(_e,Ce,B.hasChildren&&P.createElement(W.j3,null))):P.createElement(_e,Ce,B.hasChildren&&P.createElement(W.j3,null))}function ae(B){var X=B.loader;return B.useStream?P.createElement(P.Suspense,{fallback:P.createElement(B.loadingComponent,null)},P.createElement(X,null)):P.createElement(X,null)}var q=null;function M(){return q}function J(B){var X=B.history,se=P.useState({action:X.action,location:X.location}),le=(0,K.Z)(se,2),pe=le[0],me=le[1];return(0,P.useLayoutEffect)(function(){return X.listen(me)},[X]),(0,P.useLayoutEffect)(function(){function ge(Ee){B.pluginManager.applyPlugins({key:"onRouteChange",type:"event",args:{routes:B.routes,clientRoutes:B.clientRoutes,location:Ee.location,action:Ee.action,basename:B.basename,isFirst:!!Ee.isFirst}})}return ge({location:pe.location,action:pe.action,isFirst:!0}),X.listen(ge)},[X,B.routes,B.clientRoutes]),P.createElement(W.F0,{navigator:X,location:pe.location,basename:B.basename},B.children)}function te(){var B=(0,N.Ov)(),X=B.clientRoutes;return(0,W.V$)(X)}var ye=["innerProvider","i18nProvider","accessProvider","dataflowProvider","outerProvider","rootContainer"],fe=function(X,se){var le=X.basename||"/",pe=S({routesById:X.routes,routeComponents:X.routeComponents,loadingComponent:X.loadingComponent,reactRouter5Compat:X.reactRouter5Compat,useStream:X.useStream});X.pluginManager.applyPlugins({key:"patchClientRoutes",type:"event",args:{routes:pe}});for(var me=P.createElement(J,{basename:le,pluginManager:X.pluginManager,routes:X.routes,clientRoutes:pe,history:X.history},se),ge=0,Ee=ye;ge<Ee.length;ge++){var _e=Ee[ge];me=X.pluginManager.applyPlugins({type:"modify",key:_e,initialValue:me,args:{routes:X.routes,history:X.history,plugin:X.pluginManager}})}var Ce=function(){var Ie=(0,P.useState)({}),Me=(0,K.Z)(Ie,2),Le=Me[0],Pe=Me[1],Ze=(0,P.useState)(window.__UMI_LOADER_DATA__||{}),ke=(0,K.Z)(Ze,2),We=ke[0],$e=ke[1],je=(0,P.useCallback)(function(qe,He){var Be,Ne=(((Be=(0,W.fp)(pe,qe,le))===null||Be===void 0?void 0:Be.map(function(Se){return Se.route.id}))||[]).filter(Boolean);Ne.forEach(function(Se){var ze,Ve;if(window.__umi_route_prefetch__){var u,D=(u=X.routeComponents[Se])===null||u===void 0||(u=u._payload)===null||u===void 0?void 0:u._result;typeof D=="function"&&D()}var E=(ze=X.routes[Se])===null||ze===void 0?void 0:ze.clientLoader,k=!!E,Z=(Ve=X.routes[Se])===null||Ve===void 0?void 0:Ve.hasServerLoader;!He&&Z&&!k&&!window.__UMI_LOADER_DATA__&&A({id:Se,basename:le,cb:function(we){P.startTransition(function(){$e(function(Re){return(0,F.Z)((0,F.Z)({},Re),{},(0,ee.Z)({},Se,we))})})}});var re=!!Le[Se],ce=k&&E.hydrate||!Z,ue=Z&&!window.__UMI_LOADER_DATA__;k&&!re&&(ce||ue)&&E({serverLoader:function(){return A({id:Se,basename:le,cb:function(Re){P.startTransition(function(){$e(function(Ue){return(0,F.Z)((0,F.Z)({},Ue),{},(0,ee.Z)({},Se,Re))})})}})}}).then(function(he){Pe(function(we){return(0,F.Z)((0,F.Z)({},we),{},(0,ee.Z)({},Se,he))})})})},[Le]);return(0,P.useEffect)(function(){return je(window.location.pathname,!0),X.history.listen(function(qe){je(qe.location.pathname)})},[]),(0,P.useLayoutEffect)(function(){typeof X.callback=="function"&&X.callback()},[]),P.createElement(N.Il.Provider,{value:{routes:X.routes,routeComponents:X.routeComponents,clientRoutes:pe,pluginManager:X.pluginManager,rootElement:X.rootElement,basename:le,clientLoaderData:Le,serverLoaderData:We,preloadRoute:je,history:X.history}},me)};return Ce};function oe(B){var X=B.rootElement||document.getElementById("root"),se=fe(B,P.createElement(te,null));if(B.components)return se;if(B.hydrate){var le=window.__UMI_LOADER_DATA__||{},pe=window.__UMI_METADATA_LOADER_DATA__||{},me={metadata:pe,loaderData:le,mountElementId:B.mountElementId},ge=B.__INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.pureApp||B.__INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.pureHtml;j.hydrateRoot(ge?X:document,ge?P.createElement(se,null):P.createElement(i,me,P.createElement(se,null)));return}if(j.createRoot){q=j.createRoot(X),q.render(P.createElement(se,null));return}j.render(P.createElement(se,null),X)}},53269:function(be,Q,L){"use strict";L.d(Q,{Z:function(){return R}});var ee=L(16076),F=L(18261),K=L(44194),P=L(32696),j=L(76350),W=L(8658);function N(g,w){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},m=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};if(typeof IntersectionObserver!="function")return null;var p=K.useRef(typeof IntersectionObserver=="function"),b=K.useRef(null);return K.useEffect(function(){if(!(!g.current||!p.current||m.disabled))return b.current=new IntersectionObserver(function(d){var O=(0,W.Z)(d,1),T=O[0];w(T)},o),b.current.observe(g.current),function(){var d;(d=b.current)===null||d===void 0||d.disconnect()}},[w,o,m.disabled,g]),b.current}var A=["prefetch"];function _(g){var w=K.useRef(null);return K.useEffect(function(){g&&(typeof g=="function"?g(w.current):g.current=w.current)}),w}var R=K.forwardRef(function(g,w){var o,m=g.prefetch,p=(0,F.Z)(g,A),b=typeof window!="undefined"&&window.__umi_route_prefetch__||{defaultPrefetch:"none",defaultPrefetchTimeout:50},d=b.defaultPrefetch,O=b.defaultPrefetchTimeout,T=(m===!0?"intent":m===!1?"none":m)||d;if(!["intent","render","viewport","none"].includes(T))throw new Error("Invalid prefetch value ".concat(T," found in Link component"));var v=(0,j.Ov)(),c=typeof g.to=="string"?g.to:(o=g.to)===null||o===void 0?void 0:o.pathname,i=K.useRef(!1),f=_(w),C=function(G){if(T==="intent"){var U=G.target||{};U.preloadTimeout||(U.preloadTimeout=setTimeout(function(){var z;U.preloadTimeout=null,(z=v.preloadRoute)===null||z===void 0||z.call(v,c)},g.prefetchTimeout||O))}},I=function(G){if(T==="intent"){var U=G.target||{};U.preloadTimeout&&(clearTimeout(U.preloadTimeout),U.preloadTimeout=null)}};return(0,K.useLayoutEffect)(function(){if(T==="render"&&!i.current){var S;(S=v.preloadRoute)===null||S===void 0||S.call(v,c),i.current=!0}},[T,c]),N(f,function(S){if(S!=null&&S.isIntersecting){var G;(G=v.preloadRoute)===null||G===void 0||G.call(v,c)}},{rootMargin:"100px"},{disabled:T!=="viewport"}),c?K.createElement(P.rU,(0,ee.Z)({onMouseEnter:C,onMouseLeave:I,ref:f},p),g.children):null})},96953:function(be,Q,L){"use strict";L.d(Q,{nG:function(){return Ue},Un:function(){return k}});var ee=L(41029);function F(l,h){return h>>>l|h<<32-l}function K(l,h,y){return l&h^~l&y}function P(l,h,y){return l&h^l&y^h&y}function j(l){return F(2,l)^F(13,l)^F(22,l)}function W(l){return F(6,l)^F(11,l)^F(25,l)}function N(l){return F(7,l)^F(18,l)^l>>>3}function A(l){return F(17,l)^F(19,l)^l>>>10}function _(l,h){return l[h&15]+=A(l[h+14&15])+l[h+9&15]+N(l[h+1&15])}var R=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],g,w,o,m="0123456789abcdef";function p(l,h){var y=(l&65535)+(h&65535),$=(l>>16)+(h>>16)+(y>>16);return $<<16|y&65535}function b(){g=new Array(8),w=new Array(2),o=new Array(64),w[0]=w[1]=0,g[0]=1779033703,g[1]=3144134277,g[2]=1013904242,g[3]=2773480762,g[4]=1359893119,g[5]=2600822924,g[6]=528734635,g[7]=1541459225}function d(){var l,h,y,$,Y,ie,ne,s,t,r,a=new Array(16);l=g[0],h=g[1],y=g[2],$=g[3],Y=g[4],ie=g[5],ne=g[6],s=g[7];for(var n=0;n<16;n++)a[n]=o[(n<<2)+3]|o[(n<<2)+2]<<8|o[(n<<2)+1]<<16|o[n<<2]<<24;for(var e=0;e<64;e++)t=s+W(Y)+K(Y,ie,ne)+R[e],e<16?t+=a[e]:t+=_(a,e),r=j(l)+P(l,h,y),s=ne,ne=ie,ie=Y,Y=p($,t),$=y,y=h,h=l,l=p(t,r);g[0]+=l,g[1]+=h,g[2]+=y,g[3]+=$,g[4]+=Y,g[5]+=ie,g[6]+=ne,g[7]+=s}function O(l,h){var y,$,Y=0;$=w[0]>>3&63;var ie=h&63;for((w[0]+=h<<3)<h<<3&&w[1]++,w[1]+=h>>29,y=0;y+63<h;y+=64){for(var ne=$;ne<64;ne++)o[ne]=l.charCodeAt(Y++);d(),$=0}for(var s=0;s<ie;s++)o[s]=l.charCodeAt(Y++)}function T(){var l=w[0]>>3&63;if(o[l++]=128,l<=56)for(var h=l;h<56;h++)o[h]=0;else{for(var y=l;y<64;y++)o[y]=0;d();for(var $=0;$<56;$++)o[$]=0}o[56]=w[1]>>>24&255,o[57]=w[1]>>>16&255,o[58]=w[1]>>>8&255,o[59]=w[1]&255,o[60]=w[0]>>>24&255,o[61]=w[0]>>>16&255,o[62]=w[0]>>>8&255,o[63]=w[0]&255,d()}function v(){for(var l=0,h=new Array(32),y=0;y<8;y++)h[l++]=g[y]>>>24&255,h[l++]=g[y]>>>16&255,h[l++]=g[y]>>>8&255,h[l++]=g[y]&255;return h}function c(){for(var l=new String,h=0;h<8;h++)for(var y=28;y>=0;y-=4)l+=m.charAt(g[h]>>>y&15);return l}function i(l){return b(),O(l,l.length),T(),c()}var f=i;function C(l){"@babel/helpers - typeof";return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(h){return typeof h}:function(h){return h&&typeof Symbol=="function"&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},C(l)}var I=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function S(l,h){return z(l)||U(l,h)||Ee(l,h)||G()}function G(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function U(l,h){var y=l==null?null:typeof Symbol!="undefined"&&l[Symbol.iterator]||l["@@iterator"];if(y!=null){var $=[],Y=!0,ie=!1,ne,s;try{for(y=y.call(l);!(Y=(ne=y.next()).done)&&($.push(ne.value),!(h&&$.length===h));Y=!0);}catch(t){ie=!0,s=t}finally{try{!Y&&y.return!=null&&y.return()}finally{if(ie)throw s}}return $}}function z(l){if(Array.isArray(l))return l}function H(l,h){var y=typeof Symbol!="undefined"&&l[Symbol.iterator]||l["@@iterator"];if(!y){if(Array.isArray(l)||(y=Ee(l))||h&&l&&typeof l.length=="number"){y&&(l=y);var $=0,Y=function(){};return{s:Y,n:function(){return $>=l.length?{done:!0}:{done:!1,value:l[$++]}},e:function(r){throw r},f:Y}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ie=!0,ne=!1,s;return{s:function(){y=y.call(l)},n:function(){var r=y.next();return ie=r.done,r},e:function(r){ne=!0,s=r},f:function(){try{!ie&&y.return!=null&&y.return()}finally{if(ne)throw s}}}}function ae(l,h){if(!(l instanceof h))throw new TypeError("Cannot call a class as a function")}function q(l,h){for(var y=0;y<h.length;y++){var $=h[y];$.enumerable=$.enumerable||!1,$.configurable=!0,"value"in $&&($.writable=!0),Object.defineProperty(l,$.key,$)}}function M(l,h,y){return h&&q(l.prototype,h),y&&q(l,y),Object.defineProperty(l,"prototype",{writable:!1}),l}function J(l,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(h&&h.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),Object.defineProperty(l,"prototype",{writable:!1}),h&&le(l,h)}function te(l){var h=X();return function(){var $=pe(l),Y;if(h){var ie=pe(this).constructor;Y=Reflect.construct($,arguments,ie)}else Y=$.apply(this,arguments);return ye(this,Y)}}function ye(l,h){if(h&&(C(h)==="object"||typeof h=="function"))return h;if(h!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fe(l)}function fe(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}function oe(l){var h=typeof Map=="function"?new Map:void 0;return oe=function($){if($===null||!se($))return $;if(typeof $!="function")throw new TypeError("Super expression must either be null or a function");if(typeof h!="undefined"){if(h.has($))return h.get($);h.set($,Y)}function Y(){return B($,arguments,pe(this).constructor)}return Y.prototype=Object.create($.prototype,{constructor:{value:Y,enumerable:!1,writable:!0,configurable:!0}}),le(Y,$)},oe(l)}function B(l,h,y){return X()?B=Reflect.construct.bind():B=function(Y,ie,ne){var s=[null];s.push.apply(s,ie);var t=Function.bind.apply(Y,s),r=new t;return ne&&le(r,ne.prototype),r},B.apply(null,arguments)}function X(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(l){return!1}}function se(l){return Function.toString.call(l).indexOf("[native code]")!==-1}function le(l,h){return le=Object.setPrototypeOf?Object.setPrototypeOf.bind():function($,Y){return $.__proto__=Y,$},le(l,h)}function pe(l){return pe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(y){return y.__proto__||Object.getPrototypeOf(y)},pe(l)}function me(l){return Ce(l)||_e(l)||Ee(l)||ge()}function ge(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ee(l,h){if(l){if(typeof l=="string")return De(l,h);var y=Object.prototype.toString.call(l).slice(8,-1);if(y==="Object"&&l.constructor&&(y=l.constructor.name),y==="Map"||y==="Set")return Array.from(l);if(y==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(y))return De(l,h)}}function _e(l){if(typeof Symbol!="undefined"&&l[Symbol.iterator]!=null||l["@@iterator"]!=null)return Array.from(l)}function Ce(l){if(Array.isArray(l))return De(l)}function De(l,h){(h==null||h>l.length)&&(h=l.length);for(var y=0,$=new Array(h);y<h;y++)$[y]=l[y];return $}function Ie(l,h){if(l==null)return{};var y=Me(l,h),$,Y;if(Object.getOwnPropertySymbols){var ie=Object.getOwnPropertySymbols(l);for(Y=0;Y<ie.length;Y++)$=ie[Y],!(h.indexOf($)>=0)&&Object.prototype.propertyIsEnumerable.call(l,$)&&(y[$]=l[$])}return y}function Me(l,h){if(l==null)return{};var y={},$=Object.keys(l),Y,ie;for(ie=0;ie<$.length;ie++)Y=$[ie],!(h.indexOf(Y)>=0)&&(y[Y]=l[Y]);return y}function Le(l,h){var y=Object.keys(l);if(Object.getOwnPropertySymbols){var $=Object.getOwnPropertySymbols(l);h&&($=$.filter(function(Y){return Object.getOwnPropertyDescriptor(l,Y).enumerable})),y.push.apply(y,$)}return y}function Pe(l){for(var h=1;h<arguments.length;h++){var y=arguments[h]!=null?arguments[h]:{};h%2?Le(Object(y),!0).forEach(function($){Ze(l,$,y[$])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(y)):Le(Object(y)).forEach(function($){Object.defineProperty(l,$,Object.getOwnPropertyDescriptor(y,$))})}return l}function Ze(l,h,y){return h in l?Object.defineProperty(l,h,{value:y,enumerable:!0,configurable:!0,writable:!0}):l[h]=y,l}var ke="routes";function We(l){return l.split("?")[0].split("#")[0]}var $e=function(h){if(!h.startsWith("http"))return!1;try{var y=new URL(h);return!!y}catch($){return!1}},je=function(h){var y=h.path;if(!y||y==="/")try{return"/".concat(f(JSON.stringify(h)))}catch($){}return y&&We(y)},qe=function(h,y){var $=h.name,Y=h.locale;return"locale"in h&&Y===!1||!$?!1:h.locale||"".concat(y,".").concat($)},He=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return h.endsWith("/*")?h.replace("/*","/"):(h||y).startsWith("/")||$e(h)?h:"/".concat(y,"/").concat(h).replace(/\/\//g,"/").replace(/\/\//g,"/")},Be=function(h,y){var $=h.menu,Y=$===void 0?{}:$,ie=h.indexRoute,ne=h.path,s=ne===void 0?"":ne,t=h.children||[],r=Y.name,a=r===void 0?h.name:r,n=Y.icon,e=n===void 0?h.icon:n,x=Y.hideChildren,V=x===void 0?h.hideChildren:x,de=Y.flatMenu,xe=de===void 0?h.flatMenu:de,Ae=ie&&Object.keys(ie).join(",")!=="redirect"?[Pe({path:s,menu:Y},ie)].concat(t||[]):t,Oe=Pe({},h);if(a&&(Oe.name=a),e&&(Oe.icon=e),Ae&&Ae.length){if(V)return delete Oe.children,Oe;var Te=Se(Pe(Pe({},y),{},{data:Ae}),h);if(xe)return Te;delete Oe[ke]}return Oe},Ne=function(h){return Array.isArray(h)&&h.length>0};function Se(l){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},y=l.data,$=l.formatMessage,Y=l.parentName,ie=l.locale;return!y||!Array.isArray(y)?[]:y.filter(function(ne){return ne?Ne(ne.children)||ne.path||ne.originPath||ne.layout?!0:(ne.redirect||ne.unaccessible,!1):!1}).filter(function(ne){var s,t;return!(ne==null||(s=ne.menu)===null||s===void 0)&&s.name||ne!=null&&ne.flatMenu||!(ne==null||(t=ne.menu)===null||t===void 0)&&t.flatMenu?!0:ne.menu!==!1}).map(function(ne){var s=Pe(Pe({},ne),{},{path:ne.path||ne.originPath});return!s.children&&s[ke]&&(s.children=s[ke],delete s[ke]),s.unaccessible&&delete s.name,s.path==="*"&&(s.path="."),s.path==="/*"&&(s.path="."),!s.path&&s.originPath&&(s.path=s.originPath),s}).map(function(){var ne=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},s=ne.children||ne[ke]||[],t=He(ne.path,h?h.path:"/"),r=ne.name,a=qe(ne,Y||"menu"),n=a!==!1&&ie!==!1&&$&&a?$({id:a,defaultMessage:r}):r,e=h.pro_layout_parentKeys,x=e===void 0?[]:e,V=h.children,de=h.icon,xe=h.flatMenu,Ae=h.indexRoute,Oe=h.routes,Te=Ie(h,I),Fe=new Set([].concat(me(x),me(ne.parentKeys||[])));h.key&&Fe.add(h.key);var ve=Pe(Pe(Pe({},Te),{},{menu:void 0},ne),{},{path:t,locale:a,key:ne.key||je(Pe(Pe({},ne),{},{path:t})),pro_layout_parentKeys:Array.from(Fe).filter(function(Ye){return Ye&&Ye!=="/"})});if(n?ve.name=n:delete ve.name,ve.menu===void 0&&delete ve.menu,Ne(s)){var Je=Se(Pe(Pe({},l),{},{data:s,parentName:a||""}),ve);Ne(Je)&&(ve.children=Je)}return Be(ve,l)}).flat(1)}var ze=function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return h.filter(function(y){return y&&(y.name||Ne(y.children))&&!y.hideInMenu&&!y.redirect}).map(function(y){var $=Pe({},y),Y=$.children||y[ke]||[];if(delete $[ke],Ne(Y)&&!$.hideChildrenInMenu&&Y.some(function(ne){return ne&&!!ne.name})){var ie=l(Y);if(ie.length)return Pe(Pe({},$),{},{children:ie})}return Pe({},y)}).filter(function(y){return y})},Ve=function(l){J(y,l);var h=te(y);function y(){return ae(this,y),h.apply(this,arguments)}return M(y,[{key:"get",value:function(Y){var ie;try{var ne=H(this.entries()),s;try{for(ne.s();!(s=ne.n()).done;){var t=S(s.value,2),r=t[0],a=t[1],n=We(r);if(!$e(r)&&(0,ee.Bo)(n,[]).test(Y)){ie=a;break}}}catch(e){ne.e(e)}finally{ne.f()}}catch(e){ie=void 0}return ie}}]),y}(oe(Map)),u=function(h){var y=new Ve,$=function Y(ie,ne){ie.forEach(function(s){var t=s.children||s[ke]||[];Ne(t)&&Y(t,s);var r=He(s.path,ne?ne.path:"/");y.set(We(r),s)})};return $(h),y},D=function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return h.map(function(y){var $=y.children||y[ke];if(Ne($)){var Y=l($);if(Y.length)return Pe({},y)}var ie=Pe({},y);return delete ie[ke],delete ie.children,ie}).filter(function(y){return y})},E=function(h,y,$,Y){var ie=Se({data:h,formatMessage:$,locale:y}),ne=Y?D(ie):ze(ie),s=u(ie);return{breadcrumb:s,menuData:ne}},k=E;function Z(l,h){var y=Object.keys(l);if(Object.getOwnPropertySymbols){var $=Object.getOwnPropertySymbols(l);h&&($=$.filter(function(Y){return Object.getOwnPropertyDescriptor(l,Y).enumerable})),y.push.apply(y,$)}return y}function re(l){for(var h=1;h<arguments.length;h++){var y=arguments[h]!=null?arguments[h]:{};h%2?Z(Object(y),!0).forEach(function($){ce(l,$,y[$])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(y)):Z(Object(y)).forEach(function($){Object.defineProperty(l,$,Object.getOwnPropertyDescriptor(y,$))})}return l}function ce(l,h,y){return h in l?Object.defineProperty(l,h,{value:y,enumerable:!0,configurable:!0,writable:!0}):l[h]=y,l}var ue=function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],y={};return h.forEach(function($){var Y=re({},$);if(!(!Y||!Y.key)){!Y.children&&Y[ke]&&(Y.children=Y[ke],delete Y[ke]);var ie=Y.children||[];y[We(Y.path||Y.key||"/")]=re({},Y),y[Y.key||Y.path||"/"]=re({},Y),ie&&(y=re(re({},y),l(ie)))}}),y},he=ue,we=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],y=arguments.length>1?arguments[1]:void 0,$=arguments.length>2?arguments[2]:void 0;return h.filter(function(Y){if(Y==="/"&&y==="/")return!0;if(Y!=="/"&&Y!=="/*"&&Y&&!$e(Y)){var ie=We(Y);try{if($&&(0,ee.Bo)("".concat(ie)).test(y)||(0,ee.Bo)("".concat(ie),[]).test(y)||(0,ee.Bo)("".concat(ie,"/(.*)")).test(y))return!0}catch(ne){}}return!1}).sort(function(Y,ie){return Y===y?10:ie===y?-10:Y.substr(1).split("/").length-ie.substr(1).split("/").length})},Re=function(h,y,$,Y){var ie=he(y),ne=Object.keys(ie),s=we(ne,h||"/",Y);return!s||s.length<1?[]:($||(s=[s[s.length-1]]),s.map(function(t){var r=ie[t]||{pro_layout_parentKeys:"",key:""},a=new Map,n=(r.pro_layout_parentKeys||[]).map(function(e){return a.has(e)?null:(a.set(e,!0),ie[e])}).filter(function(e){return e});return r.key&&n.push(r),n}).flat(1))},Ue=Re},41029:function(be,Q){var L;function ee(p){"@babel/helpers - typeof";return ee=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},ee(p)}L={value:!0},Q.Bo=L=L=L=L=L=L=void 0;function F(p){for(var b=[],d=0;d<p.length;){var O=p[d];if(O==="*"||O==="+"||O==="?"){b.push({type:"MODIFIER",index:d,value:p[d++]});continue}if(O==="\\"){b.push({type:"ESCAPED_CHAR",index:d++,value:p[d++]});continue}if(O==="{"){b.push({type:"OPEN",index:d,value:p[d++]});continue}if(O==="}"){b.push({type:"CLOSE",index:d,value:p[d++]});continue}if(O===":"){for(var T="",v=d+1;v<p.length;){var c=p.charCodeAt(v);if(c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||c===95){T+=p[v++];continue}break}if(!T)throw new TypeError("Missing parameter name at "+d);b.push({type:"NAME",index:d,value:T}),d=v;continue}if(O==="("){var i=1,f="",v=d+1;if(p[v]==="?")throw new TypeError('Pattern cannot start with "?" at '+v);for(;v<p.length;){if(p[v]==="\\"){f+=p[v++]+p[v++];continue}if(p[v]===")"){if(i--,i===0){v++;break}}else if(p[v]==="("&&(i++,p[v+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+v);f+=p[v++]}if(i)throw new TypeError("Unbalanced pattern at "+d);if(!f)throw new TypeError("Missing pattern at "+d);b.push({type:"PATTERN",index:d,value:f}),d=v;continue}b.push({type:"CHAR",index:d,value:p[d++]})}return b.push({type:"END",index:d,value:""}),b}function K(p,b){b===void 0&&(b={});for(var d=F(p),O=b.prefixes,T=O===void 0?"./":O,v="[^"+A(b.delimiter||"/#?")+"]+?",c=[],i=0,f=0,C="",I=function(oe){if(f<d.length&&d[f].type===oe)return d[f++].value},S=function(oe){var B=I(oe);if(B!==void 0)return B;var X=d[f],se=X.type,le=X.index;throw new TypeError("Unexpected "+se+" at "+le+", expected "+oe)},G=function(){for(var oe="",B;B=I("CHAR")||I("ESCAPED_CHAR");)oe+=B;return oe};f<d.length;){var U=I("CHAR"),z=I("NAME"),H=I("PATTERN");if(z||H){var ae=U||"";T.indexOf(ae)===-1&&(C+=ae,ae=""),C&&(c.push(C),C=""),c.push({name:z||i++,prefix:ae,suffix:"",pattern:H||v,modifier:I("MODIFIER")||""});continue}var q=U||I("ESCAPED_CHAR");if(q){C+=q;continue}C&&(c.push(C),C="");var M=I("OPEN");if(M){var ae=G(),J=I("NAME")||"",te=I("PATTERN")||"",ye=G();S("CLOSE"),c.push({name:J||(te?i++:""),pattern:J&&!te?v:te,prefix:ae,suffix:ye,modifier:I("MODIFIER")||""});continue}S("END")}return c}L=K;function P(p,b){return j(K(p,b),b)}L=P;function j(p,b){b===void 0&&(b={});var d=_(b),O=b.encode,T=O===void 0?function(f){return f}:O,v=b.validate,c=v===void 0?!0:v,i=p.map(function(f){if(ee(f)==="object")return new RegExp("^(?:"+f.pattern+")$",d)});return function(f){for(var C="",I=0;I<p.length;I++){var S=p[I];if(typeof S=="string"){C+=S;continue}var G=f?f[S.name]:void 0,U=S.modifier==="?"||S.modifier==="*",z=S.modifier==="*"||S.modifier==="+";if(Array.isArray(G)){if(!z)throw new TypeError('Expected "'+S.name+'" to not repeat, but got an array');if(G.length===0){if(U)continue;throw new TypeError('Expected "'+S.name+'" to not be empty')}for(var H=0;H<G.length;H++){var ae=T(G[H],S);if(c&&!i[I].test(ae))throw new TypeError('Expected all "'+S.name+'" to match "'+S.pattern+'", but got "'+ae+'"');C+=S.prefix+ae+S.suffix}continue}if(typeof G=="string"||typeof G=="number"){var ae=T(String(G),S);if(c&&!i[I].test(ae))throw new TypeError('Expected "'+S.name+'" to match "'+S.pattern+'", but got "'+ae+'"');C+=S.prefix+ae+S.suffix;continue}if(!U){var q=z?"an array":"a string";throw new TypeError('Expected "'+S.name+'" to be '+q)}}return C}}L=j;function W(p,b){var d=[],O=m(p,d,b);return N(O,d,b)}L=W;function N(p,b,d){d===void 0&&(d={});var O=d.decode,T=O===void 0?function(v){return v}:O;return function(v){var c=p.exec(v);if(!c)return!1;for(var i=c[0],f=c.index,C=Object.create(null),I=function(U){if(c[U]===void 0)return"continue";var z=b[U-1];z.modifier==="*"||z.modifier==="+"?C[z.name]=c[U].split(z.prefix+z.suffix).map(function(H){return T(H,z)}):C[z.name]=T(c[U],z)},S=1;S<c.length;S++)I(S);return{path:i,index:f,params:C}}}L=N;function A(p){return p.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function _(p){return p&&p.sensitive?"":"i"}function R(p,b){if(!b)return p;var d=p.source.match(/\((?!\?)/g);if(d)for(var O=0;O<d.length;O++)b.push({name:O,prefix:"",suffix:"",modifier:"",pattern:""});return p}function g(p,b,d){var O=p.map(function(T){return m(T,b,d).source});return new RegExp("(?:"+O.join("|")+")",_(d))}function w(p,b,d){return o(K(p,d),b,d)}function o(p,b,d){d===void 0&&(d={});for(var O=d.strict,T=O===void 0?!1:O,v=d.start,c=v===void 0?!0:v,i=d.end,f=i===void 0?!0:i,C=d.encode,I=C===void 0?function(fe){return fe}:C,S="["+A(d.endsWith||"")+"]|$",G="["+A(d.delimiter||"/#?")+"]",U=c?"^":"",z=0,H=p;z<H.length;z++){var ae=H[z];if(typeof ae=="string")U+=A(I(ae));else{var q=A(I(ae.prefix)),M=A(I(ae.suffix));if(ae.pattern)if(b&&b.push(ae),q||M)if(ae.modifier==="+"||ae.modifier==="*"){var J=ae.modifier==="*"?"?":"";U+="(?:"+q+"((?:"+ae.pattern+")(?:"+M+q+"(?:"+ae.pattern+"))*)"+M+")"+J}else U+="(?:"+q+"("+ae.pattern+")"+M+")"+ae.modifier;else U+="("+ae.pattern+")"+ae.modifier;else U+="(?:"+q+M+")"+ae.modifier}}if(f)T||(U+=G+"?"),U+=d.endsWith?"(?="+S+")":"$";else{var te=p[p.length-1],ye=typeof te=="string"?G.indexOf(te[te.length-1])>-1:te===void 0;T||(U+="(?:"+G+"(?="+S+"))?"),ye||(U+="(?="+G+"|"+S+")")}return new RegExp(U,_(d))}L=o;function m(p,b,d){return p instanceof RegExp?R(p,b):Array.isArray(p)?g(p,b,d):w(p,b,d)}Q.Bo=m},61087:function(be,Q,L){"use strict";L.d(Q,{l:function(){return P}});var ee=L(44194),F=function(){return F=Object.assign||function(N){for(var A,_=1,R=arguments.length;_<R;_++){A=arguments[_];for(var g in A)Object.prototype.hasOwnProperty.call(A,g)&&(N[g]=A[g])}return N},F.apply(this,arguments)};function K(N){var A,_=(typeof window!="undefined"?window:{}).URL,R=new _((A=window==null?void 0:window.location)===null||A===void 0?void 0:A.href);return Object.keys(N).forEach(function(g){var w=N[g];w!=null?Array.isArray(w)?(R.searchParams.delete(g),w.forEach(function(o){R.searchParams.append(g,o)})):w instanceof Date?Number.isNaN(w.getTime())||R.searchParams.set(g,w.toISOString()):typeof w=="object"?R.searchParams.set(g,JSON.stringify(w)):R.searchParams.set(g,w):R.searchParams.delete(g)}),R}function P(N,A){var _;N===void 0&&(N={}),A===void 0&&(A={disabled:!1});var R=(0,ee.useState)(),g=R[1],w=typeof window!="undefined"&&((_=window==null?void 0:window.location)===null||_===void 0?void 0:_.search),o=(0,ee.useMemo)(function(){return A.disabled?{}:new URLSearchParams(w||{})},[A.disabled,w]),m=(0,ee.useMemo)(function(){if(A.disabled)return{};if(typeof window=="undefined"||!window.URL)return{};var d=[];o.forEach(function(T,v){d.push({key:v,value:T})}),d=d.reduce(function(T,v){return(T[v.key]=T[v.key]||[]).push(v),T},{}),d=Object.keys(d).map(function(T){var v=d[T];return v.length===1?[T,v[0].value]:[T,v.map(function(c){var i=c.value;return i})]});var O=F({},N);return d.forEach(function(T){var v=T[0],c=T[1];O[v]=W(v,c,{},N)}),O},[A.disabled,N,o]);function p(d){if(!(typeof window=="undefined"||!window.URL)){var O=K(d);window.location.search!==O.search&&window.history.replaceState({},"",O.toString()),o.toString()!==O.searchParams.toString()&&g({})}}(0,ee.useEffect)(function(){A.disabled||typeof window=="undefined"||!window.URL||p(F(F({},N),m))},[A.disabled,m]);var b=function(d){p(d)};return(0,ee.useEffect)(function(){if(A.disabled)return function(){};if(typeof window=="undefined"||!window.URL)return function(){};var d=function(){g({})};return window.addEventListener("popstate",d),function(){window.removeEventListener("popstate",d)}},[A.disabled]),[m,b]}var j={true:!0,false:!1};function W(N,A,_,R){if(!_)return A;var g=_[N],w=A===void 0?R[N]:A;return g===Number?Number(w):g===Boolean||A==="true"||A==="false"?j[w]:Array.isArray(g)?g.find(function(o){return o==w})||R[N]:w}},43047:function(be,Q,L){"use strict";L.d(Q,{l7:function(){return Se}});var ee=L(39137),F=L.n(ee),K=L(39060),P=L.n(K),j=L(73656);function W(u,D){var E=Object.keys(u);if(Object.getOwnPropertySymbols){var k=Object.getOwnPropertySymbols(u);D&&(k=k.filter(function(Z){return Object.getOwnPropertyDescriptor(u,Z).enumerable})),E.push.apply(E,k)}return E}function N(u){for(var D=1;D<arguments.length;D++){var E=arguments[D]!=null?arguments[D]:{};D%2?W(Object(E),!0).forEach(function(k){w(u,k,E[k])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(E)):W(Object(E)).forEach(function(k){Object.defineProperty(u,k,Object.getOwnPropertyDescriptor(E,k))})}return u}function A(u){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?A=function(D){return typeof D}:A=function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},A(u)}function _(u,D){if(!(u instanceof D))throw new TypeError("Cannot call a class as a function")}function R(u,D){for(var E=0;E<D.length;E++){var k=D[E];k.enumerable=k.enumerable||!1,k.configurable=!0,"value"in k&&(k.writable=!0),Object.defineProperty(u,k.key,k)}}function g(u,D,E){return D&&R(u.prototype,D),E&&R(u,E),u}function w(u,D,E){return D in u?Object.defineProperty(u,D,{value:E,enumerable:!0,configurable:!0,writable:!0}):u[D]=E,u}function o(u,D){if(typeof D!="function"&&D!==null)throw new TypeError("Super expression must either be null or a function");u.prototype=Object.create(D&&D.prototype,{constructor:{value:u,writable:!0,configurable:!0}}),D&&p(u,D)}function m(u){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)},m(u)}function p(u,D){return p=Object.setPrototypeOf||function(k,Z){return k.__proto__=Z,k},p(u,D)}function b(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(u){return!1}}function d(u,D,E){return b()?d=Reflect.construct:d=function(Z,re,ce){var ue=[null];ue.push.apply(ue,re);var he=Function.bind.apply(Z,ue),we=new he;return ce&&p(we,ce.prototype),we},d.apply(null,arguments)}function O(u){return Function.toString.call(u).indexOf("[native code]")!==-1}function T(u){var D=typeof Map=="function"?new Map:void 0;return T=function(k){if(k===null||!O(k))return k;if(typeof k!="function")throw new TypeError("Super expression must either be null or a function");if(typeof D!="undefined"){if(D.has(k))return D.get(k);D.set(k,Z)}function Z(){return d(k,arguments,m(this).constructor)}return Z.prototype=Object.create(k.prototype,{constructor:{value:Z,enumerable:!1,writable:!0,configurable:!0}}),p(Z,k)},T(u)}function v(u){if(u===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u}function c(u,D){if(D&&(typeof D=="object"||typeof D=="function"))return D;if(D!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v(u)}function i(u){var D=b();return function(){var k=m(u),Z;if(D){var re=m(this).constructor;Z=Reflect.construct(k,arguments,re)}else Z=k.apply(this,arguments);return c(this,Z)}}function f(u){return C(u)||I(u)||S(u)||U()}function C(u){if(Array.isArray(u))return G(u)}function I(u){if(typeof Symbol!="undefined"&&u[Symbol.iterator]!=null||u["@@iterator"]!=null)return Array.from(u)}function S(u,D){if(u){if(typeof u=="string")return G(u,D);var E=Object.prototype.toString.call(u).slice(8,-1);if(E==="Object"&&u.constructor&&(E=u.constructor.name),E==="Map"||E==="Set")return Array.from(u);if(E==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(E))return G(u,D)}}function G(u,D){(D==null||D>u.length)&&(D=u.length);for(var E=0,k=new Array(D);E<D;E++)k[E]=u[E];return k}function U(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function z(u){if(!Array.isArray(u))throw new TypeError("Middlewares must be an array!");for(var D=u.length,E=0;E<D;E++)if(typeof u[E]!="function")throw new TypeError("Middleware must be componsed of function");return function(Z,re){var ce=-1;function ue(he){if(he<=ce)return Promise.reject(new Error("next() should not be called multiple times in one middleware!"));ce=he;var we=u[he]||re;if(!we)return Promise.resolve();try{return Promise.resolve(we(Z,function(){return ue(he+1)}))}catch(Re){return Promise.reject(Re)}}return ue(0)}}var H=function(){function u(D){if(_(this,u),!Array.isArray(D))throw new TypeError("Default middlewares must be an array!");this.defaultMiddlewares=f(D),this.middlewares=[]}return g(u,[{key:"use",value:function(E){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!1,core:!1,defaultInstance:!1},Z=!1,re=!1,ce=!1;if(typeof k=="number"?(Z=!0,re=!1):A(k)==="object"&&k&&(re=k.global||!1,Z=k.core||!1,ce=k.defaultInstance||!1),re){u.globalMiddlewares.splice(u.globalMiddlewares.length-u.defaultGlobalMiddlewaresLength,0,E);return}if(Z){u.coreMiddlewares.splice(u.coreMiddlewares.length-u.defaultCoreMiddlewaresLength,0,E);return}if(ce){this.defaultMiddlewares.push(E);return}this.middlewares.push(E)}},{key:"execute",value:function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,k=z([].concat(f(this.middlewares),f(this.defaultMiddlewares),f(u.globalMiddlewares),f(u.coreMiddlewares)));return k(E)}}]),u}();H.globalMiddlewares=[],H.defaultGlobalMiddlewaresLength=0,H.coreMiddlewares=[],H.defaultCoreMiddlewaresLength=0;var ae=function(){function u(D){_(this,u),this.cache=new Map,this.timer={},this.extendOptions(D)}return g(u,[{key:"extendOptions",value:function(E){this.maxCache=E.maxCache||0}},{key:"get",value:function(E){return this.cache.get(JSON.stringify(E))}},{key:"set",value:function(E,k){var Z=this,re=arguments.length>2&&arguments[2]!==void 0?arguments[2]:6e4;if(this.maxCache>0&&this.cache.size>=this.maxCache){var ce=f(this.cache.keys())[0];this.cache.delete(ce),this.timer[ce]&&clearTimeout(this.timer[ce])}var ue=JSON.stringify(E);this.cache.set(ue,k),re>0&&(this.timer[ue]=setTimeout(function(){Z.cache.delete(ue),delete Z.timer[ue]},re))}},{key:"delete",value:function(E){var k=JSON.stringify(E);return delete this.timer[k],this.cache.delete(k)}},{key:"clear",value:function(){return this.timer={},this.cache.clear()}}]),u}(),q=function(u){o(E,u);var D=i(E);function E(k,Z){var re,ce=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"RequestError";return _(this,E),re=D.call(this,k),re.name="RequestError",re.request=Z,re.type=ce,re}return E}(T(Error)),M=function(u){o(E,u);var D=i(E);function E(k,Z,re,ce){var ue,he=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"ResponseError";return _(this,E),ue=D.call(this,Z||k.statusText),ue.name="ResponseError",ue.data=re,ue.response=k,ue.request=ce,ue.type=he,ue}return E}(T(Error));function J(u){return new Promise(function(D,E){var k=new FileReader;k.onload=function(){D(k.result)},k.onerror=E,k.readAsText(u,"GBK")})}function te(u){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,E=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,k=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;try{return JSON.parse(u)}catch(Z){if(D)throw new M(E,"JSON.parse fail",u,k,"ParseError")}return u}function ye(u,D,E){return new Promise(function(k,Z){setTimeout(function(){Z(new q(D||"timeout of ".concat(u,"ms exceeded"),E,"Timeout"))},u)})}function fe(u){return new Promise(function(D,E){u.cancelToken&&u.cancelToken.promise.then(function(k){E(k)})})}var oe=Object.prototype.toString;function B(){var u;return typeof j!="undefined"&&oe.call(j)==="[object process]"&&(u="NODE"),typeof XMLHttpRequest!="undefined"&&(u="BROWSER"),u}function X(u){return A(u)==="object"&&Object.prototype.toString.call(u)==="[object Array]"}function se(u){return typeof URLSearchParams!="undefined"&&u instanceof URLSearchParams}function le(u){return A(u)==="object"&&Object.prototype.toString.call(u)==="[object Date]"}function pe(u){return u!==null&&A(u)==="object"}function me(u,D){if(u)if(A(u)!=="object"&&(u=[u]),X(u))for(var E=0;E<u.length;E++)D.call(null,u[E],E,u);else for(var k in u)Object.prototype.hasOwnProperty.call(u,k)&&D.call(null,u[k],k,u)}function ge(u){return se(u)?(0,ee.parse)(u.toString(),{strictNullHandling:!0}):typeof u=="string"?[u]:u}function Ee(u){return(0,ee.stringify)(u,{arrayFormat:"repeat",strictNullHandling:!0})}function _e(u,D){return N(N(N({},u),D),{},{headers:N(N({},u.headers),D.headers),params:N(N({},ge(u.params)),ge(D.params)),method:(D.method||u.method||"get").toLowerCase()})}var Ce=function(D){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},k=E.prefix,Z=E.suffix;return k&&(D="".concat(k).concat(D)),Z&&(D="".concat(D).concat(Z)),{url:D,options:E}},De=!1;function Ie(u,D){var E=D.method,k=E===void 0?"get":E;return k.toLowerCase()==="get"}function Me(u,D){if(!u)return D();var E=u.req;E=E===void 0?{}:E;var k=E.options,Z=k===void 0?{}:k,re=E.url,ce=re===void 0?"":re,ue=u.cache,he=u.responseInterceptors,we=Z.timeout,Re=we===void 0?0:we,Ue=Z.timeoutMessage,l=Z.__umiRequestCoreType__,h=l===void 0?"normal":l,y=Z.useCache,$=y===void 0?!1:y,Y=Z.method,ie=Y===void 0?"get":Y,ne=Z.params,s=Z.ttl,t=Z.validateCache,r=t===void 0?Ie:t;if(h!=="normal")return D();var a=fetch;if(!a)throw new Error("Global fetch not exist!");var n=B()==="BROWSER",e=r(ce,Z)&&$&&n;if(e){var x=ue.get({url:ce,params:ne,method:ie});if(x)return x=x.clone(),x.useCache=!0,u.res=x,D()}var V;return Re>0?V=Promise.race([fe(Z),a(ce,Z),ye(Re,Ue,u.req)]):V=Promise.race([fe(Z),a(ce,Z)]),he.forEach(function(de){V=V.then(function(xe){var Ae=typeof xe.clone=="function"?xe.clone():xe;return de(Ae,Z)})}),V.then(function(de){if(e&&de.status===200){var xe=de.clone();xe.useCache=!0,ue.set({url:ce,params:ne,method:ie},xe,s)}return u.res=de,D()})}function Le(u,D){var E;return D().then(function(){if(u){var k=u.res,Z=k===void 0?{}:k,re=u.req,ce=re===void 0?{}:re,ue=ce||{},he=ue.options;he=he===void 0?{}:he;var we=he.responseType,Re=we===void 0?"json":we,Ue=he.charset,l=Ue===void 0?"utf8":Ue,h=he.getResponse,y=he.throwErrIfParseFail,$=y===void 0?!1:y,Y=he.parseResponse,ie=Y===void 0?!0:Y;if(ie&&!(!Z||!Z.clone)){if(E=B()==="BROWSER"?Z.clone():Z,E.useCache=Z.useCache||!1,l==="gbk")try{return Z.blob().then(J).then(function(ne){return te(ne,!1,E,ce)})}catch(ne){throw new M(E,ne.message,null,ce,"ParseError")}else if(Re==="json")return Z.text().then(function(ne){return te(ne,$,E,ce)});try{return Z[Re]()}catch(ne){throw new M(E,"responseType not support",null,ce,"ParseError")}}}}).then(function(k){if(u){var Z=u.res,re=u.req,ce=re===void 0?{}:re,ue=ce||{},he=ue.options;he=he===void 0?{}:he;var we=he.getResponse,Re=we===void 0?!1:we;if(E){if(E.status>=200&&E.status<300){if(Re){u.res={data:k,response:E};return}u.res=k;return}throw new M(E,"http error",k,ce,"HttpError")}}}).catch(function(k){if(k instanceof q||k instanceof M)throw k;var Z=u.req,re=u.res;throw k.request=k.request||Z,k.response=k.response||re,k.type=k.type||k.name,k.data=k.data||void 0,k})}function Pe(u,D){if(!u)return D();var E=u.req;E=E===void 0?{}:E;var k=E.options,Z=k===void 0?{}:k,re=Z.method,ce=re===void 0?"get":re;if(["post","put","patch","delete"].indexOf(ce.toLowerCase())===-1)return D();var ue=Z.requestType,he=ue===void 0?"json":ue,we=Z.data;if(we){var Re=Object.prototype.toString.call(we);Re==="[object Object]"||Re==="[object Array]"?he==="json"?(Z.headers=N({Accept:"application/json","Content-Type":"application/json;charset=UTF-8"},Z.headers),Z.body=JSON.stringify(we)):he==="form"&&(Z.headers=N({Accept:"application/json","Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},Z.headers),Z.body=Ee(we)):(Z.headers=N({Accept:"application/json"},Z.headers),Z.body=we)}return u.req.options=Z,D()}function Ze(u,D){var E,k;if(u)if(D)E=D(u);else if(se(u))E=u.toString();else if(X(u))k=[],me(u,function(re){re===null||typeof re=="undefined"?k.push(re):k.push(pe(re)?JSON.stringify(re):re)}),E=Ee(k);else{k={},me(u,function(re,ce){var ue=re;re===null||typeof re=="undefined"?k[ce]=re:le(re)?ue=re.toISOString():X(re)?ue=re:pe(re)&&(ue=JSON.stringify(re)),k[ce]=ue});var Z=Ee(k);E=Z}return E}function ke(u,D){if(!u)return D();var E=u.req;E=E===void 0?{}:E;var k=E.options,Z=k===void 0?{}:k,re=Z.paramsSerializer,ce=Z.params,ue=u.req;ue=ue===void 0?{}:ue;var he=ue.url,we=he===void 0?"":he;Z.method=Z.method?Z.method.toUpperCase():"GET",Z.credentials=Z.credentials||"same-origin";var Re=Ze(ce,re);if(u.req.originUrl=we,Re){var Ue=we.indexOf("?")!==-1?"&":"?";u.req.url="".concat(we).concat(Ue).concat(Re)}return u.req.options=Z,D()}var We=[Pe,ke,Le],$e=[Me];H.globalMiddlewares=We,H.defaultGlobalMiddlewaresLength=We.length,H.coreMiddlewares=$e,H.defaultCoreMiddlewaresLength=$e.length;var je=function(){function u(D){_(this,u),this.onion=new H([]),this.fetchIndex=0,this.mapCache=new ae(D),this.initOptions=D,this.instanceRequestInterceptors=[],this.instanceResponseInterceptors=[]}return g(u,[{key:"use",value:function(E){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!1,core:!1};return this.onion.use(E,k),this}},{key:"extendOptions",value:function(E){this.initOptions=_e(this.initOptions,E),this.mapCache.extendOptions(E)}},{key:"dealRequestInterceptors",value:function(E){var k=function(ce,ue){return ce.then(function(){var he=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return E.req.url=he.url||E.req.url,E.req.options=he.options||E.req.options,ue(E.req.url,E.req.options)})},Z=[].concat(f(u.requestInterceptors),f(this.instanceRequestInterceptors));return Z.reduce(k,Promise.resolve()).then(function(){var re=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return E.req.url=re.url||E.req.url,E.req.options=re.options||E.req.options,Promise.resolve()})}},{key:"request",value:function(E,k){var Z=this,re=this.onion,ce={req:{url:E,options:N(N({},k),{},{url:E})},res:null,cache:this.mapCache,responseInterceptors:[].concat(f(u.responseInterceptors),f(this.instanceResponseInterceptors))};if(typeof E!="string")throw new Error("url MUST be a string");return new Promise(function(ue,he){Z.dealRequestInterceptors(ce).then(function(){return re.execute(ce)}).then(function(){ue(ce.res)}).catch(function(we){var Re=ce.req.options.errorHandler;if(Re)try{var Ue=Re(we);ue(Ue)}catch(l){he(l)}else he(we)})})}}],[{key:"requestUse",value:function(E){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!0};if(typeof E!="function")throw new TypeError("Interceptor must be function!");k.global?u.requestInterceptors.push(E):this.instanceRequestInterceptors.push(E)}},{key:"responseUse",value:function(E){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{global:!0};if(typeof E!="function")throw new TypeError("Interceptor must be function!");k.global?u.responseInterceptors.push(E):this.instanceResponseInterceptors.push(E)}}]),u}();je.requestInterceptors=[Ce],je.responseInterceptors=[];function qe(u){this.message=u}qe.prototype.toString=function(){return this.message?"Cancel: ".concat(this.message):"Cancel"},qe.prototype.__CANCEL__=!0;function He(u){if(typeof u!="function")throw new TypeError("executor must be a function.");var D;this.promise=new Promise(function(Z){D=Z});var E=this;u(function(Z){E.reason||(E.reason=new qe(Z),D(E.reason))})}He.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},He.source=function(){var D,E=new He(function(Z){D=Z});return{token:E,cancel:D}};function Be(u){return!!(u&&u.__CANCEL__)}var Ne=function(){var D=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=new je(D),k=function(ce){var ue=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},he=_e(E.initOptions,ue);return E.request(ce,he)};k.use=E.use.bind(E),k.fetchIndex=E.fetchIndex,k.interceptors={request:{use:je.requestUse.bind(E)},response:{use:je.responseUse.bind(E)}};var Z=["get","post","delete","put","patch","head","options","rpc"];return Z.forEach(function(re){k[re]=function(ce,ue){return k(ce,N(N({},ue),{},{method:re}))}}),k.Cancel=qe,k.CancelToken=He,k.isCancel=Be,k.extendOptions=E.extendOptions.bind(E),k.middlewares={instance:E.onion.middlewares,defaultInstance:E.onion.defaultMiddlewares,global:H.globalMiddlewares,core:H.coreMiddlewares},k},Se=function(D){return Ne(D)},ze=Ne({parseResponse:!1}),Ve=Ne({});Q.ZP=Ve},81308:function(be,Q,L){"use strict";L.d(Q,{A:function(){return o},Q:function(){return m}});var ee=L(73510),F=L(42549),K=L(24721),P=L(49338),j=L(57564),W=L(49556),N=L(86101),A=L(42208),_=L(18260);function R(p,b){if(!p)throw new Error(b)}function g(p){var b=p.fns,d=p.args;if(b.length===1)return b[0];var O=b.pop();return b.reduce(function(T,v){return function(){return v(T,d)}},O)}function w(p){return!!p&&(0,P.Z)(p)==="object"&&typeof p.then=="function"}var o=function(p){return p.compose="compose",p.modify="modify",p.event="event",p}({}),m=function(){function p(b){(0,N.Z)(this,p),(0,_.Z)(this,"opts",void 0),(0,_.Z)(this,"hooks",{}),this.opts=b}return(0,A.Z)(p,[{key:"register",value:function(d){var O=this;R(d.apply,"plugin register failed, apply must supplied"),Object.keys(d.apply).forEach(function(T){R(O.opts.validKeys.indexOf(T)>-1,"register failed, invalid key ".concat(T," ").concat(d.path?"from plugin ".concat(d.path):"",".")),O.hooks[T]=(O.hooks[T]||[]).concat(d.apply[T])})}},{key:"getHooks",value:function(d){var O=d.split("."),T=(0,W.Z)(O),v=T[0],c=T.slice(1),i=this.hooks[v]||[];return c.length&&(i=i.map(function(f){try{var C=f,I=(0,j.Z)(c),S;try{for(I.s();!(S=I.n()).done;){var G=S.value;C=C[G]}}catch(U){I.e(U)}finally{I.f()}return C}catch(U){return null}}).filter(Boolean)),i}},{key:"applyPlugins",value:function(d){var O=d.key,T=d.type,v=d.initialValue,c=d.args,i=d.async,f=this.getHooks(O)||[];switch(c&&R((0,P.Z)(c)==="object","applyPlugins failed, args must be plain object."),i&&R(T===o.modify||T===o.event,"async only works with modify and event type."),T){case o.modify:return i?f.reduce(function(){var C=(0,K.Z)((0,ee.Z)().mark(function I(S,G){var U;return(0,ee.Z)().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:if(R(typeof G=="function"||(0,P.Z)(G)==="object"||w(G),"applyPlugins failed, all hooks for key ".concat(O," must be function, plain object or Promise.")),!w(S)){H.next=5;break}return H.next=4,S;case 4:S=H.sent;case 5:if(typeof G!="function"){H.next=16;break}if(U=G(S,c),!w(U)){H.next=13;break}return H.next=10,U;case 10:return H.abrupt("return",H.sent);case 13:return H.abrupt("return",U);case 14:H.next=21;break;case 16:if(!w(G)){H.next=20;break}return H.next=19,G;case 19:G=H.sent;case 20:return H.abrupt("return",(0,F.Z)((0,F.Z)({},S),G));case 21:case"end":return H.stop()}},I)}));return function(I,S){return C.apply(this,arguments)}}(),w(v)?v:Promise.resolve(v)):f.reduce(function(C,I){return R(typeof I=="function"||(0,P.Z)(I)==="object","applyPlugins failed, all hooks for key ".concat(O," must be function or plain object.")),typeof I=="function"?I(C,c):(0,F.Z)((0,F.Z)({},C),I)},v);case o.event:return(0,K.Z)((0,ee.Z)().mark(function C(){var I,S,G,U;return(0,ee.Z)().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:I=(0,j.Z)(f),H.prev=1,I.s();case 3:if((S=I.n()).done){H.next=12;break}if(G=S.value,R(typeof G=="function","applyPlugins failed, all hooks for key ".concat(O," must be function.")),U=G(c),!(i&&w(U))){H.next=10;break}return H.next=10,U;case 10:H.next=3;break;case 12:H.next=17;break;case 14:H.prev=14,H.t0=H.catch(1),I.e(H.t0);case 17:return H.prev=17,I.f(),H.finish(17);case 20:case"end":return H.stop()}},C,null,[[1,14,17,20]])}))();case o.compose:return function(){return g({fns:f.concat(v),args:c})()}}}}],[{key:"create",value:function(d){var O=new p({validKeys:d.validKeys});return d.plugins.forEach(function(T){O.register(T)}),O}}]),p}()},11151:function(be,Q,L){"use strict";var ee=L(44194);function F(w,o){return w===o&&(w!==0||1/w===1/o)||w!==w&&o!==o}var K=typeof Object.is=="function"?Object.is:F,P=ee.useState,j=ee.useEffect,W=ee.useLayoutEffect,N=ee.useDebugValue;function A(w,o){var m=o(),p=P({inst:{value:m,getSnapshot:o}}),b=p[0].inst,d=p[1];return W(function(){b.value=m,b.getSnapshot=o,_(b)&&d({inst:b})},[w,m,o]),j(function(){return _(b)&&d({inst:b}),w(function(){_(b)&&d({inst:b})})},[w]),N(m),m}function _(w){var o=w.getSnapshot;w=w.value;try{var m=o();return!K(w,m)}catch(p){return!0}}function R(w,o){return o()}var g=typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"?R:A;Q.useSyncExternalStore=ee.useSyncExternalStore!==void 0?ee.useSyncExternalStore:g},94191:function(be,Q,L){"use strict";be.exports=L(11151)},82500:function(be,Q,L){"use strict";var ee;ee={value:!0},Q.AU=Q.Ts=void 0;const F=L(38069),K=L(42911),P=L(32650);var j;(function(_){_.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:P.Event.None}),_.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:P.Event.None});function R(g){const w=g;return w&&(w===_.None||w===_.Cancelled||K.boolean(w.isCancellationRequested)&&!!w.onCancellationRequested)}_.is=R})(j||(Q.Ts=j={}));const W=Object.freeze(function(_,R){const g=(0,F.default)().timer.setTimeout(_.bind(R),0);return{dispose(){g.dispose()}}});class N{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?W:(this._emitter||(this._emitter=new P.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class A{get token(){return this._token||(this._token=new N),this._token}cancel(){this._token?this._token.cancel():this._token=j.Cancelled}dispose(){this._token?this._token instanceof N&&this._token.dispose():this._token=j.None}}Q.AU=A},32650:function(be,Q,L){"use strict";Object.defineProperty(Q,"__esModule",{value:!0}),Q.Emitter=Q.Event=void 0;const ee=L(38069);var F;(function(j){const W={dispose(){}};j.None=function(){return W}})(F||(Q.Event=F={}));class K{add(W,N=null,A){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(W),this._contexts.push(N),Array.isArray(A)&&A.push({dispose:()=>this.remove(W,N)})}remove(W,N=null){if(!this._callbacks)return;let A=!1;for(let _=0,R=this._callbacks.length;_<R;_++)if(this._callbacks[_]===W)if(this._contexts[_]===N){this._callbacks.splice(_,1),this._contexts.splice(_,1);return}else A=!0;if(A)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...W){if(!this._callbacks)return[];const N=[],A=this._callbacks.slice(0),_=this._contexts.slice(0);for(let R=0,g=A.length;R<g;R++)try{N.push(A[R].apply(_[R],W))}catch(w){(0,ee.default)().console.error(w)}return N}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class P{constructor(W){this._options=W}get event(){return this._event||(this._event=(W,N,A)=>{this._callbacks||(this._callbacks=new K),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(W,N);const _={dispose:()=>{this._callbacks&&(this._callbacks.remove(W,N),_.dispose=P._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(A)&&A.push(_),_}),this._event}fire(W){this._callbacks&&this._callbacks.invoke.call(this._callbacks,W)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}Q.Emitter=P,P._noop=function(){}},42911:function(be,Q){"use strict";Object.defineProperty(Q,"__esModule",{value:!0}),Q.stringArray=Q.array=Q.func=Q.error=Q.number=Q.string=Q.boolean=void 0;function L(N){return N===!0||N===!1}Q.boolean=L;function ee(N){return typeof N=="string"||N instanceof String}Q.string=ee;function F(N){return typeof N=="number"||N instanceof Number}Q.number=F;function K(N){return N instanceof Error}Q.error=K;function P(N){return typeof N=="function"}Q.func=P;function j(N){return Array.isArray(N)}Q.array=j;function W(N){return j(N)&&N.every(A=>ee(A))}Q.stringArray=W},38069:function(be,Q){"use strict";Object.defineProperty(Q,"__esModule",{value:!0});let L;function ee(){if(L===void 0)throw new Error("No runtime abstraction layer installed");return L}(function(F){function K(P){if(P===void 0)throw new Error("No runtime abstraction layer provided");L=P}F.install=K})(ee||(ee={})),Q.default=ee},62118:function(be){"use strict";var Q=!1,L=function(){};if(Q){var ee=function(K,P){var j=arguments.length;P=new Array(j>1?j-1:0);for(var W=1;W<j;W++)P[W-1]=arguments[W];var N=0,A="Warning: "+K.replace(/%s/g,function(){return P[N++]});typeof console!="undefined"&&console.error(A);try{throw new Error(A)}catch(_){}};L=function(F,K,P){var j=arguments.length;P=new Array(j>2?j-2:0);for(var W=2;W<j;W++)P[W-2]=arguments[W];if(K===void 0)throw new Error("`warning(condition, format, ...args)` requires a warning message argument");F||ee.apply(null,[K].concat(P))}}be.exports=L},8004:function(be,Q,L){"use strict";L.d(Q,{ZP:function(){return c}});const ee=-1,F=0,K=1,P=2,j=3,W=4,N=5,A=6,_=7,R=8,g=typeof self=="object"?self:globalThis,w=(i,f)=>{const C=(S,G)=>(i.set(G,S),S),I=S=>{if(i.has(S))return i.get(S);const[G,U]=f[S];switch(G){case F:case ee:return C(U,S);case K:{const z=C([],S);for(const H of U)z.push(I(H));return z}case P:{const z=C({},S);for(const[H,ae]of U)z[I(H)]=I(ae);return z}case j:return C(new Date(U),S);case W:{const{source:z,flags:H}=U;return C(new RegExp(z,H),S)}case N:{const z=C(new Map,S);for(const[H,ae]of U)z.set(I(H),I(ae));return z}case A:{const z=C(new Set,S);for(const H of U)z.add(I(H));return z}case _:{const{name:z,message:H}=U;return C(new g[z](H),S)}case R:return C(BigInt(U),S);case"BigInt":return C(Object(BigInt(U)),S);case"ArrayBuffer":return C(new Uint8Array(U).buffer,U);case"DataView":{const{buffer:z}=new Uint8Array(U);return C(new DataView(z),U)}}return C(new g[G](U),S)};return I},o=i=>w(new Map,i)(0),m="",{toString:p}={},{keys:b}=Object,d=i=>{const f=typeof i;if(f!=="object"||!i)return[F,f];const C=p.call(i).slice(8,-1);switch(C){case"Array":return[K,m];case"Object":return[P,m];case"Date":return[j,m];case"RegExp":return[W,m];case"Map":return[N,m];case"Set":return[A,m];case"DataView":return[K,C]}return C.includes("Array")?[K,C]:C.includes("Error")?[_,C]:[P,C]},O=([i,f])=>i===F&&(f==="function"||f==="symbol"),T=(i,f,C,I)=>{const S=(U,z)=>{const H=I.push(U)-1;return C.set(z,H),H},G=U=>{if(C.has(U))return C.get(U);let[z,H]=d(U);switch(z){case F:{let q=U;switch(H){case"bigint":z=R,q=U.toString();break;case"function":case"symbol":if(i)throw new TypeError("unable to serialize "+H);q=null;break;case"undefined":return S([ee],U)}return S([z,q],U)}case K:{if(H){let J=U;return H==="DataView"?J=new Uint8Array(U.buffer):H==="ArrayBuffer"&&(J=new Uint8Array(U)),S([H,[...J]],U)}const q=[],M=S([z,q],U);for(const J of U)q.push(G(J));return M}case P:{if(H)switch(H){case"BigInt":return S([H,U.toString()],U);case"Boolean":case"Number":case"String":return S([H,U.valueOf()],U)}if(f&&"toJSON"in U)return G(U.toJSON());const q=[],M=S([z,q],U);for(const J of b(U))(i||!O(d(U[J])))&&q.push([G(J),G(U[J])]);return M}case j:return S([z,U.toISOString()],U);case W:{const{source:q,flags:M}=U;return S([z,{source:q,flags:M}],U)}case N:{const q=[],M=S([z,q],U);for(const[J,te]of U)(i||!(O(d(J))||O(d(te))))&&q.push([G(J),G(te)]);return M}case A:{const q=[],M=S([z,q],U);for(const J of U)(i||!O(d(J)))&&q.push(G(J));return M}}const{message:ae}=U;return S([z,{name:H,message:ae}],U)};return G},v=(i,{json:f,lossy:C}={})=>{const I=[];return T(!(f||C),!!f,new Map,I)(i),I};var c=typeof structuredClone=="function"?(i,f)=>f&&("json"in f||"lossy"in f)?o(v(i,f)):structuredClone(i):(i,f)=>o(v(i,f))},5715:function(be,Q,L){"use strict";L.d(Q,{l:function(){return R}});var ee=L(81050),F=L(93405),K=L(69245),P=L(89389),j=L(59521),W=L(55739);const N=function(v){const f=this.constructor.prototype,C=f[v],I=function(){return C.apply(I,arguments)};return Object.setPrototypeOf(I,f),I},A={}.hasOwnProperty;class _ extends N{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=(0,j.r)()}copy(){const c=new _;let i=-1;for(;++i<this.attachers.length;){const f=this.attachers[i];c.use(...f)}return c.data(F(!0,{},this.namespace)),c}data(c,i){return typeof c=="string"?arguments.length===2?(o("data",this.frozen),this.namespace[c]=i,this):A.call(this.namespace,c)&&this.namespace[c]||void 0:c?(o("data",this.frozen),this.namespace=c,this):this.namespace}freeze(){if(this.frozen)return this;const c=this;for(;++this.freezeIndex<this.attachers.length;){const[i,...f]=this.attachers[this.freezeIndex];if(f[0]===!1)continue;f[0]===!0&&(f[0]=void 0);const C=i.call(c,...f);typeof C=="function"&&this.transformers.use(C)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(c){this.freeze();const i=b(c),f=this.parser||this.Parser;return g("parse",f),f(String(i),i)}process(c,i){const f=this;return this.freeze(),g("process",this.parser||this.Parser),w("process",this.compiler||this.Compiler),i?C(void 0,i):new Promise(C);function C(I,S){const G=b(c),U=f.parse(G);f.run(U,G,function(H,ae,q){if(H||!ae||!q)return z(H);const M=ae,J=f.stringify(M,q);O(J)?q.value=J:q.result=J,z(H,q)});function z(H,ae){H||!ae?S(H):I?I(ae):((0,K.ok)(i,"`done` is defined if `resolve` is not"),i(void 0,ae))}}}processSync(c){let i=!1,f;return this.freeze(),g("processSync",this.parser||this.Parser),w("processSync",this.compiler||this.Compiler),this.process(c,C),p("processSync","process",i),(0,K.ok)(f,"we either bailed on an error or have a tree"),f;function C(I,S){i=!0,(0,ee.N)(I),f=S}}run(c,i,f){m(c),this.freeze();const C=this.transformers;return!f&&typeof i=="function"&&(f=i,i=void 0),f?I(void 0,f):new Promise(I);function I(S,G){(0,K.ok)(typeof i!="function","`file` can\u2019t be a `done` anymore, we checked");const U=b(i);C.run(c,U,z);function z(H,ae,q){const M=ae||c;H?G(H):S?S(M):((0,K.ok)(f,"`done` is defined if `resolve` is not"),f(void 0,M,q))}}}runSync(c,i){let f=!1,C;return this.run(c,i,I),p("runSync","run",f),(0,K.ok)(C,"we either bailed on an error or have a tree"),C;function I(S,G){(0,ee.N)(S),C=G,f=!0}}stringify(c,i){this.freeze();const f=b(i),C=this.compiler||this.Compiler;return w("stringify",C),m(c),C(c,f)}use(c,...i){const f=this.attachers,C=this.namespace;if(o("use",this.frozen),c!=null)if(typeof c=="function")U(c,i);else if(typeof c=="object")Array.isArray(c)?G(c):S(c);else throw new TypeError("Expected usable value, not `"+c+"`");return this;function I(z){if(typeof z=="function")U(z,[]);else if(typeof z=="object")if(Array.isArray(z)){const[H,...ae]=z;U(H,ae)}else S(z);else throw new TypeError("Expected usable value, not `"+z+"`")}function S(z){if(!("plugins"in z)&&!("settings"in z))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");G(z.plugins),z.settings&&(C.settings=F(!0,C.settings,z.settings))}function G(z){let H=-1;if(z!=null)if(Array.isArray(z))for(;++H<z.length;){const ae=z[H];I(ae)}else throw new TypeError("Expected a list of plugins, not `"+z+"`")}function U(z,H){let ae=-1,q=-1;for(;++ae<f.length;)if(f[ae][0]===z){q=ae;break}if(q===-1)f.push([z,...H]);else if(H.length>0){let[M,...J]=H;const te=f[q][1];(0,P.Z)(te)&&(0,P.Z)(M)&&(M=F(!0,te,M)),f[q]=[z,M,...J]}}}}const R=new _().freeze();function g(v,c){if(typeof c!="function")throw new TypeError("Cannot `"+v+"` without `parser`")}function w(v,c){if(typeof c!="function")throw new TypeError("Cannot `"+v+"` without `compiler`")}function o(v,c){if(c)throw new Error("Cannot call `"+v+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function m(v){if(!(0,P.Z)(v)||typeof v.type!="string")throw new TypeError("Expected node, got `"+v+"`")}function p(v,c,i){if(!i)throw new Error("`"+v+"` finished async. Use `"+c+"` instead")}function b(v){return d(v)?v:new W.k(v)}function d(v){return!!(v&&typeof v=="object"&&"message"in v&&"messages"in v)}function O(v){return typeof v=="string"||T(v)}function T(v){return!!(v&&typeof v=="object"&&"byteLength"in v&&"byteOffset"in v)}},83368:function(be,Q,L){"use strict";L.d(Q,{U:function(){return F}});var ee=L(6606);const F=function(K,P,j){const W=(0,ee.O)(j);if(!K||!K.type||!K.children)throw new Error("Expected parent node");if(typeof P=="number"){if(P<0||P===Number.POSITIVE_INFINITY)throw new Error("Expected positive finite number as index")}else if(P=K.children.indexOf(P),P<0)throw new Error("Expected child node or index");for(;++P<K.children.length;)if(W(K.children[P],P,K))return K.children[P]}},6606:function(be,Q,L){"use strict";L.d(Q,{O:function(){return F}});const ee=function(_,R,g,w,o){const m=F(R);if(g!=null&&(typeof g!="number"||g<0||g===Number.POSITIVE_INFINITY))throw new Error("Expected positive finite index");if(w!=null&&(!ee(w)||!w.children))throw new Error("Expected parent node");if(w==null!=(g==null))throw new Error("Expected both parent and index");return A(_)?m.call(o,_,g,w):!1},F=function(_){if(_==null)return N;if(typeof _=="function")return W(_);if(typeof _=="object")return Array.isArray(_)?K(_):P(_);if(typeof _=="string")return j(_);throw new Error("Expected function, string, or object as test")};function K(_){const R=[];let g=-1;for(;++g<_.length;)R[g]=F(_[g]);return W(w);function w(...o){let m=-1;for(;++m<R.length;)if(R[m].apply(this,o))return!0;return!1}}function P(_){const R=_;return W(g);function g(w){const o=w;let m;for(m in _)if(o[m]!==R[m])return!1;return!0}}function j(_){return W(R);function R(g){return g&&g.type===_}}function W(_){return R;function R(g,w,o){return!!(A(g)&&_.call(this,g,typeof w=="number"?w:void 0,o||void 0))}}function N(){return!0}function A(_){return _!==null&&typeof _=="object"&&"type"in _}},34183:function(be,Q,L){"use strict";L.d(Q,{FK:function(){return P},Pk:function(){return F},rb:function(){return ee}});const ee=K("end"),F=K("start");function K(j){return W;function W(N){const A=N&&N.position&&N.position[j]||{};if(typeof A.line=="number"&&A.line>0&&typeof A.column=="number"&&A.column>0)return{line:A.line,column:A.column,offset:typeof A.offset=="number"&&A.offset>-1?A.offset:void 0}}}function P(j){const W=F(j),N=ee(j);if(W&&N)return{start:W,end:N}}},40014:function(be,Q,L){"use strict";L.d(Q,{y:function(){return ee}});function ee(j){return!j||typeof j!="object"?"":"position"in j||"type"in j?K(j.position):"start"in j||"end"in j?K(j):"line"in j||"column"in j?F(j):""}function F(j){return P(j&&j.line)+":"+P(j&&j.column)}function K(j){return F(j&&j.start)+"-"+F(j&&j.end)}function P(j){return j&&typeof j=="number"?j:1}},19517:function(be,Q,L){"use strict";L.d(Q,{BK:function(){return j},S4:function(){return N}});var ee=L(6606);function F(_){return _}const K=[],P=!0,j=!1,W="skip";function N(_,R,g,w){let o;typeof R=="function"&&typeof g!="function"?(w=g,g=R):o=R;const m=(0,ee.O)(o),p=w?-1:1;b(_,void 0,[])();function b(d,O,T){const v=d&&typeof d=="object"?d:{};if(typeof v.type=="string"){const i=typeof v.tagName=="string"?v.tagName:typeof v.name=="string"?v.name:void 0;Object.defineProperty(c,"name",{value:"node ("+(d.type+(i?"<"+i+">":""))+")"})}return c;function c(){let i=K,f,C,I;if((!R||m(d,O,T[T.length-1]||void 0))&&(i=A(g(d,T)),i[0]===j))return i;if("children"in d&&d.children){const S=d;if(S.children&&i[0]!==W)for(C=(w?S.children.length:-1)+p,I=T.concat(S);C>-1&&C<S.children.length;){const G=S.children[C];if(f=b(G,C,I)(),f[0]===j)return f;C=typeof f[1]=="number"?f[1]:C+p}}return i}}}function A(_){return Array.isArray(_)?_:typeof _=="number"?[P,_]:_==null?K:[_]}},92662:function(be,Q,L){"use strict";L.d(Q,{Vn:function(){return F}});var ee=L(19517);function F(K,P,j,W){let N,A,_;typeof P=="function"&&typeof j!="function"?(A=void 0,_=P,N=j):(A=P,_=j,N=W),(0,ee.S4)(K,A,R,N);function R(g,w){const o=w[w.length-1],m=o?o.children.indexOf(g):void 0;return _(g,m,o)}}},38029:function(be,Q,L){"use strict";L.d(Q,{$:function(){return F}});var ee=L(40014);class F extends Error{constructor(P,j,W){super(),typeof j=="string"&&(W=j,j=void 0);let N="",A={},_=!1;if(j&&("line"in j&&"column"in j?A={place:j}:"start"in j&&"end"in j?A={place:j}:"type"in j?A={ancestors:[j],place:j.position}:A=Ke({},j)),typeof P=="string"?N=P:!A.cause&&P&&(_=!0,N=P.message,A.cause=P),!A.ruleId&&!A.source&&typeof W=="string"){const g=W.indexOf(":");g===-1?A.ruleId=W:(A.source=W.slice(0,g),A.ruleId=W.slice(g+1))}if(!A.place&&A.ancestors&&A.ancestors){const g=A.ancestors[A.ancestors.length-1];g&&(A.place=g.position)}const R=A.place&&"start"in A.place?A.place.start:A.place;this.ancestors=A.ancestors||void 0,this.cause=A.cause||void 0,this.column=R?R.column:void 0,this.fatal=void 0,this.file,this.message=N,this.line=R?R.line:void 0,this.name=(0,ee.y)(A.place)||"1:1",this.place=A.place||void 0,this.reason=this.message,this.ruleId=A.ruleId||void 0,this.source=A.source||void 0,this.stack=_&&A.cause&&typeof A.cause.stack=="string"?A.cause.stack:"",this.actual,this.expected,this.note,this.url}}F.prototype.file="",F.prototype.name="",F.prototype.reason="",F.prototype.message="",F.prototype.stack="",F.prototype.column=void 0,F.prototype.line=void 0,F.prototype.ancestors=void 0,F.prototype.cause=void 0,F.prototype.fatal=void 0,F.prototype.place=void 0,F.prototype.ruleId=void 0,F.prototype.source=void 0},55739:function(be,Q,L){"use strict";L.d(Q,{k:function(){return b}});var ee=L(38029);const F={basename:K,dirname:P,extname:j,join:W,sep:"/"};function K(c,i){if(i!==void 0&&typeof i!="string")throw new TypeError('"ext" argument must be a string');_(c);let f=0,C=-1,I=c.length,S;if(i===void 0||i.length===0||i.length>c.length){for(;I--;)if(c.codePointAt(I)===47){if(S){f=I+1;break}}else C<0&&(S=!0,C=I+1);return C<0?"":c.slice(f,C)}if(i===c)return"";let G=-1,U=i.length-1;for(;I--;)if(c.codePointAt(I)===47){if(S){f=I+1;break}}else G<0&&(S=!0,G=I+1),U>-1&&(c.codePointAt(I)===i.codePointAt(U--)?U<0&&(C=I):(U=-1,C=G));return f===C?C=G:C<0&&(C=c.length),c.slice(f,C)}function P(c){if(_(c),c.length===0)return".";let i=-1,f=c.length,C;for(;--f;)if(c.codePointAt(f)===47){if(C){i=f;break}}else C||(C=!0);return i<0?c.codePointAt(0)===47?"/":".":i===1&&c.codePointAt(0)===47?"//":c.slice(0,i)}function j(c){_(c);let i=c.length,f=-1,C=0,I=-1,S=0,G;for(;i--;){const U=c.codePointAt(i);if(U===47){if(G){C=i+1;break}continue}f<0&&(G=!0,f=i+1),U===46?I<0?I=i:S!==1&&(S=1):I>-1&&(S=-1)}return I<0||f<0||S===0||S===1&&I===f-1&&I===C+1?"":c.slice(I,f)}function W(...c){let i=-1,f;for(;++i<c.length;)_(c[i]),c[i]&&(f=f===void 0?c[i]:f+"/"+c[i]);return f===void 0?".":N(f)}function N(c){_(c);const i=c.codePointAt(0)===47;let f=A(c,!i);return f.length===0&&!i&&(f="."),f.length>0&&c.codePointAt(c.length-1)===47&&(f+="/"),i?"/"+f:f}function A(c,i){let f="",C=0,I=-1,S=0,G=-1,U,z;for(;++G<=c.length;){if(G<c.length)U=c.codePointAt(G);else{if(U===47)break;U=47}if(U===47){if(!(I===G-1||S===1))if(I!==G-1&&S===2){if(f.length<2||C!==2||f.codePointAt(f.length-1)!==46||f.codePointAt(f.length-2)!==46){if(f.length>2){if(z=f.lastIndexOf("/"),z!==f.length-1){z<0?(f="",C=0):(f=f.slice(0,z),C=f.length-1-f.lastIndexOf("/")),I=G,S=0;continue}}else if(f.length>0){f="",C=0,I=G,S=0;continue}}i&&(f=f.length>0?f+"/..":"..",C=2)}else f.length>0?f+="/"+c.slice(I+1,G):f=c.slice(I+1,G),C=G-I-1;I=G,S=0}else U===46&&S>-1?S++:S=-1}return f}function _(c){if(typeof c!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(c))}const R={cwd:g};function g(){return"/"}function w(c){return!!(c!==null&&typeof c=="object"&&"href"in c&&c.href&&"protocol"in c&&c.protocol&&c.auth===void 0)}function o(c){if(typeof c=="string")c=new URL(c);else if(!w(c)){const i=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+c+"`");throw i.code="ERR_INVALID_ARG_TYPE",i}if(c.protocol!=="file:"){const i=new TypeError("The URL must be of scheme file");throw i.code="ERR_INVALID_URL_SCHEME",i}return m(c)}function m(c){if(c.hostname!==""){const C=new TypeError('File URL host must be "localhost" or empty on darwin');throw C.code="ERR_INVALID_FILE_URL_HOST",C}const i=c.pathname;let f=-1;for(;++f<i.length;)if(i.codePointAt(f)===37&&i.codePointAt(f+1)===50){const C=i.codePointAt(f+2);if(C===70||C===102){const I=new TypeError("File URL path must not include encoded / characters");throw I.code="ERR_INVALID_FILE_URL_PATH",I}}return decodeURIComponent(i)}const p=["history","path","basename","stem","extname","dirname"];class b{constructor(i){let f;i?w(i)?f={path:i}:typeof i=="string"||v(i)?f={value:i}:f=i:f={},this.cwd="cwd"in f?"":R.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let C=-1;for(;++C<p.length;){const S=p[C];S in f&&f[S]!==void 0&&f[S]!==null&&(this[S]=S==="history"?[...f[S]]:f[S])}let I;for(I in f)p.includes(I)||(this[I]=f[I])}get basename(){return typeof this.path=="string"?F.basename(this.path):void 0}set basename(i){O(i,"basename"),d(i,"basename"),this.path=F.join(this.dirname||"",i)}get dirname(){return typeof this.path=="string"?F.dirname(this.path):void 0}set dirname(i){T(this.basename,"dirname"),this.path=F.join(i||"",this.basename)}get extname(){return typeof this.path=="string"?F.extname(this.path):void 0}set extname(i){if(d(i,"extname"),T(this.dirname,"extname"),i){if(i.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(i.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=F.join(this.dirname,this.stem+(i||""))}get path(){return this.history[this.history.length-1]}set path(i){w(i)&&(i=o(i)),O(i,"path"),this.path!==i&&this.history.push(i)}get stem(){return typeof this.path=="string"?F.basename(this.path,this.extname):void 0}set stem(i){O(i,"stem"),d(i,"stem"),this.path=F.join(this.dirname||"",i+(this.extname||""))}fail(i,f,C){const I=this.message(i,f,C);throw I.fatal=!0,I}info(i,f,C){const I=this.message(i,f,C);return I.fatal=void 0,I}message(i,f,C){const I=new ee.$(i,f,C);return this.path&&(I.name=this.path+":"+I.name,I.file=this.path),I.fatal=!1,this.messages.push(I),I}toString(i){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(i||void 0).decode(this.value)}}function d(c,i){if(c&&c.includes(F.sep))throw new Error("`"+i+"` cannot be a path: did not expect `"+F.sep+"`")}function O(c,i){if(!c)throw new Error("`"+i+"` cannot be empty")}function T(c,i){if(!c)throw new Error("Setting `"+i+"` requires `path` to be set too")}function v(c){return!!(c&&typeof c=="object"&&"byteLength"in c&&"byteOffset"in c)}},2792:function(be,Q,L){"use strict";L.d(Q,{n:function(){return F}});class ee{constructor(_,R,g,w){this._uri=_,this._languageId=R,this._version=g,this._content=w,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(_){if(_){const R=this.offsetAt(_.start),g=this.offsetAt(_.end);return this._content.substring(R,g)}return this._content}update(_,R){for(const g of _)if(ee.isIncremental(g)){const w=W(g.range),o=this.offsetAt(w.start),m=this.offsetAt(w.end);this._content=this._content.substring(0,o)+g.text+this._content.substring(m,this._content.length);const p=Math.max(w.start.line,0),b=Math.max(w.end.line,0);let d=this._lineOffsets;const O=P(g.text,!1,o);if(b-p===O.length)for(let v=0,c=O.length;v<c;v++)d[v+p+1]=O[v];else O.length<1e4?d.splice(p+1,b-p,...O):this._lineOffsets=d=d.slice(0,p+1).concat(O,d.slice(b+1));const T=g.text.length-(m-o);if(T!==0)for(let v=p+1+O.length,c=d.length;v<c;v++)d[v]=d[v]+T}else if(ee.isFull(g))this._content=g.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=R}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=P(this._content,!0)),this._lineOffsets}positionAt(_){_=Math.max(Math.min(_,this._content.length),0);const R=this.getLineOffsets();let g=0,w=R.length;if(w===0)return{line:0,character:_};for(;g<w;){const m=Math.floor((g+w)/2);R[m]>_?w=m:g=m+1}const o=g-1;return _=this.ensureBeforeEOL(_,R[o]),{line:o,character:_-R[o]}}offsetAt(_){const R=this.getLineOffsets();if(_.line>=R.length)return this._content.length;if(_.line<0)return 0;const g=R[_.line];if(_.character<=0)return g;const w=_.line+1<R.length?R[_.line+1]:this._content.length,o=Math.min(g+_.character,w);return this.ensureBeforeEOL(o,g)}ensureBeforeEOL(_,R){for(;_>R&&j(this._content.charCodeAt(_-1));)_--;return _}get lineCount(){return this.getLineOffsets().length}static isIncremental(_){const R=_;return R!=null&&typeof R.text=="string"&&R.range!==void 0&&(R.rangeLength===void 0||typeof R.rangeLength=="number")}static isFull(_){const R=_;return R!=null&&typeof R.text=="string"&&R.range===void 0&&R.rangeLength===void 0}}var F;(function(A){function _(w,o,m,p){return new ee(w,o,m,p)}A.create=_;function R(w,o,m){if(w instanceof ee)return w.update(o,m),w;throw new Error("TextDocument.update: document must be created by TextDocument.create")}A.update=R;function g(w,o){const m=w.getText(),p=K(o.map(N),(O,T)=>{const v=O.range.start.line-T.range.start.line;return v===0?O.range.start.character-T.range.start.character:v});let b=0;const d=[];for(const O of p){const T=w.offsetAt(O.range.start);if(T<b)throw new Error("Overlapping edit");T>b&&d.push(m.substring(b,T)),O.newText.length&&d.push(O.newText),b=w.offsetAt(O.range.end)}return d.push(m.substr(b)),d.join("")}A.applyEdits=g})(F||(F={}));function K(A,_){if(A.length<=1)return A;const R=A.length/2|0,g=A.slice(0,R),w=A.slice(R);K(g,_),K(w,_);let o=0,m=0,p=0;for(;o<g.length&&m<w.length;)_(g[o],w[m])<=0?A[p++]=g[o++]:A[p++]=w[m++];for(;o<g.length;)A[p++]=g[o++];for(;m<w.length;)A[p++]=w[m++];return A}function P(A,_,R=0){const g=_?[R]:[];for(let w=0;w<A.length;w++){const o=A.charCodeAt(w);j(o)&&(o===13&&w+1<A.length&&A.charCodeAt(w+1)===10&&w++,g.push(R+w+1))}return g}function j(A){return A===13||A===10}function W(A){const _=A.start,R=A.end;return _.line>R.line||_.line===R.line&&_.character>R.character?{start:R,end:_}:A}function N(A){const _=W(A.range);return _!==A.range?{newText:A.newText,range:_}:A}},87541:function(be,Q,L){"use strict";L.d(Q,{Ly:function(){return j},e6:function(){return W}});var ee;(function(t){function r(a){return typeof a=="string"}t.is=r})(ee||(ee={}));var F;(function(t){function r(a){return typeof a=="string"}t.is=r})(F||(F={}));var K;(function(t){t.MIN_VALUE=-2147483648,t.MAX_VALUE=2147483647;function r(a){return typeof a=="number"&&t.MIN_VALUE<=a&&a<=t.MAX_VALUE}t.is=r})(K||(K={}));var P;(function(t){t.MIN_VALUE=0,t.MAX_VALUE=2147483647;function r(a){return typeof a=="number"&&t.MIN_VALUE<=a&&a<=t.MAX_VALUE}t.is=r})(P||(P={}));var j;(function(t){function r(n,e){return n===Number.MAX_VALUE&&(n=P.MAX_VALUE),e===Number.MAX_VALUE&&(e=P.MAX_VALUE),{line:n,character:e}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&s.uinteger(e.line)&&s.uinteger(e.character)}t.is=a})(j||(j={}));var W;(function(t){function r(n,e,x,V){if(s.uinteger(n)&&s.uinteger(e)&&s.uinteger(x)&&s.uinteger(V))return{start:j.create(n,e),end:j.create(x,V)};if(j.is(n)&&j.is(e))return{start:n,end:e};throw new Error(`Range#create called with invalid arguments[${n}, ${e}, ${x}, ${V}]`)}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&j.is(e.start)&&j.is(e.end)}t.is=a})(W||(W={}));var N;(function(t){function r(n,e){return{uri:n,range:e}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&W.is(e.range)&&(s.string(e.uri)||s.undefined(e.uri))}t.is=a})(N||(N={}));var A;(function(t){function r(n,e,x,V){return{targetUri:n,targetRange:e,targetSelectionRange:x,originSelectionRange:V}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&W.is(e.targetRange)&&s.string(e.targetUri)&&W.is(e.targetSelectionRange)&&(W.is(e.originSelectionRange)||s.undefined(e.originSelectionRange))}t.is=a})(A||(A={}));var _;(function(t){function r(n,e,x,V){return{red:n,green:e,blue:x,alpha:V}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.numberRange(e.red,0,1)&&s.numberRange(e.green,0,1)&&s.numberRange(e.blue,0,1)&&s.numberRange(e.alpha,0,1)}t.is=a})(_||(_={}));var R;(function(t){function r(n,e){return{range:n,color:e}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&W.is(e.range)&&_.is(e.color)}t.is=a})(R||(R={}));var g;(function(t){function r(n,e,x){return{label:n,textEdit:e,additionalTextEdits:x}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.string(e.label)&&(s.undefined(e.textEdit)||v.is(e))&&(s.undefined(e.additionalTextEdits)||s.typedArray(e.additionalTextEdits,v.is))}t.is=a})(g||(g={}));var w;(function(t){t.Comment="comment",t.Imports="imports",t.Region="region"})(w||(w={}));var o;(function(t){function r(n,e,x,V,de,xe){const Ae={startLine:n,endLine:e};return s.defined(x)&&(Ae.startCharacter=x),s.defined(V)&&(Ae.endCharacter=V),s.defined(de)&&(Ae.kind=de),s.defined(xe)&&(Ae.collapsedText=xe),Ae}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.uinteger(e.startLine)&&s.uinteger(e.startLine)&&(s.undefined(e.startCharacter)||s.uinteger(e.startCharacter))&&(s.undefined(e.endCharacter)||s.uinteger(e.endCharacter))&&(s.undefined(e.kind)||s.string(e.kind))}t.is=a})(o||(o={}));var m;(function(t){function r(n,e){return{location:n,message:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&N.is(e.location)&&s.string(e.message)}t.is=a})(m||(m={}));var p;(function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4})(p||(p={}));var b;(function(t){t.Unnecessary=1,t.Deprecated=2})(b||(b={}));var d;(function(t){function r(a){const n=a;return s.objectLiteral(n)&&s.string(n.href)}t.is=r})(d||(d={}));var O;(function(t){function r(n,e,x,V,de,xe){let Ae={range:n,message:e};return s.defined(x)&&(Ae.severity=x),s.defined(V)&&(Ae.code=V),s.defined(de)&&(Ae.source=de),s.defined(xe)&&(Ae.relatedInformation=xe),Ae}t.create=r;function a(n){var e;let x=n;return s.defined(x)&&W.is(x.range)&&s.string(x.message)&&(s.number(x.severity)||s.undefined(x.severity))&&(s.integer(x.code)||s.string(x.code)||s.undefined(x.code))&&(s.undefined(x.codeDescription)||s.string((e=x.codeDescription)===null||e===void 0?void 0:e.href))&&(s.string(x.source)||s.undefined(x.source))&&(s.undefined(x.relatedInformation)||s.typedArray(x.relatedInformation,m.is))}t.is=a})(O||(O={}));var T;(function(t){function r(n,e,...x){let V={title:n,command:e};return s.defined(x)&&x.length>0&&(V.arguments=x),V}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.title)&&s.string(e.command)}t.is=a})(T||(T={}));var v;(function(t){function r(x,V){return{range:x,newText:V}}t.replace=r;function a(x,V){return{range:{start:x,end:x},newText:V}}t.insert=a;function n(x){return{range:x,newText:""}}t.del=n;function e(x){const V=x;return s.objectLiteral(V)&&s.string(V.newText)&&W.is(V.range)}t.is=e})(v||(v={}));var c;(function(t){function r(n,e,x){const V={label:n};return e!==void 0&&(V.needsConfirmation=e),x!==void 0&&(V.description=x),V}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&s.string(e.label)&&(s.boolean(e.needsConfirmation)||e.needsConfirmation===void 0)&&(s.string(e.description)||e.description===void 0)}t.is=a})(c||(c={}));var i;(function(t){function r(a){const n=a;return s.string(n)}t.is=r})(i||(i={}));var f;(function(t){function r(x,V,de){return{range:x,newText:V,annotationId:de}}t.replace=r;function a(x,V,de){return{range:{start:x,end:x},newText:V,annotationId:de}}t.insert=a;function n(x,V){return{range:x,newText:"",annotationId:V}}t.del=n;function e(x){const V=x;return v.is(V)&&(c.is(V.annotationId)||i.is(V.annotationId))}t.is=e})(f||(f={}));var C;(function(t){function r(n,e){return{textDocument:n,edits:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&J.is(e.textDocument)&&Array.isArray(e.edits)}t.is=a})(C||(C={}));var I;(function(t){function r(n,e,x){let V={kind:"create",uri:n};return e!==void 0&&(e.overwrite!==void 0||e.ignoreIfExists!==void 0)&&(V.options=e),x!==void 0&&(V.annotationId=x),V}t.create=r;function a(n){let e=n;return e&&e.kind==="create"&&s.string(e.uri)&&(e.options===void 0||(e.options.overwrite===void 0||s.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||s.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||i.is(e.annotationId))}t.is=a})(I||(I={}));var S;(function(t){function r(n,e,x,V){let de={kind:"rename",oldUri:n,newUri:e};return x!==void 0&&(x.overwrite!==void 0||x.ignoreIfExists!==void 0)&&(de.options=x),V!==void 0&&(de.annotationId=V),de}t.create=r;function a(n){let e=n;return e&&e.kind==="rename"&&s.string(e.oldUri)&&s.string(e.newUri)&&(e.options===void 0||(e.options.overwrite===void 0||s.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||s.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||i.is(e.annotationId))}t.is=a})(S||(S={}));var G;(function(t){function r(n,e,x){let V={kind:"delete",uri:n};return e!==void 0&&(e.recursive!==void 0||e.ignoreIfNotExists!==void 0)&&(V.options=e),x!==void 0&&(V.annotationId=x),V}t.create=r;function a(n){let e=n;return e&&e.kind==="delete"&&s.string(e.uri)&&(e.options===void 0||(e.options.recursive===void 0||s.boolean(e.options.recursive))&&(e.options.ignoreIfNotExists===void 0||s.boolean(e.options.ignoreIfNotExists)))&&(e.annotationId===void 0||i.is(e.annotationId))}t.is=a})(G||(G={}));var U;(function(t){function r(a){let n=a;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(e=>s.string(e.kind)?I.is(e)||S.is(e)||G.is(e):C.is(e)))}t.is=r})(U||(U={}));class z{constructor(r,a){this.edits=r,this.changeAnnotations=a}insert(r,a,n){let e,x;if(n===void 0?e=v.insert(r,a):i.is(n)?(x=n,e=f.insert(r,a,n)):(this.assertChangeAnnotations(this.changeAnnotations),x=this.changeAnnotations.manage(n),e=f.insert(r,a,x)),this.edits.push(e),x!==void 0)return x}replace(r,a,n){let e,x;if(n===void 0?e=v.replace(r,a):i.is(n)?(x=n,e=f.replace(r,a,n)):(this.assertChangeAnnotations(this.changeAnnotations),x=this.changeAnnotations.manage(n),e=f.replace(r,a,x)),this.edits.push(e),x!==void 0)return x}delete(r,a){let n,e;if(a===void 0?n=v.del(r):i.is(a)?(e=a,n=f.del(r,a)):(this.assertChangeAnnotations(this.changeAnnotations),e=this.changeAnnotations.manage(a),n=f.del(r,e)),this.edits.push(n),e!==void 0)return e}add(r){this.edits.push(r)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(r){if(r===void 0)throw new Error("Text edit change is not configured to manage change annotations.")}}class H{constructor(r){this._annotations=r===void 0?Object.create(null):r,this._counter=0,this._size=0}all(){return this._annotations}get size(){return this._size}manage(r,a){let n;if(i.is(r)?n=r:(n=this.nextId(),a=r),this._annotations[n]!==void 0)throw new Error(`Id ${n} is already in use.`);if(a===void 0)throw new Error(`No annotation provided for id ${n}`);return this._annotations[n]=a,this._size++,n}nextId(){return this._counter++,this._counter.toString()}}class ae{constructor(r){this._textEditChanges=Object.create(null),r!==void 0?(this._workspaceEdit=r,r.documentChanges?(this._changeAnnotations=new H(r.changeAnnotations),r.changeAnnotations=this._changeAnnotations.all(),r.documentChanges.forEach(a=>{if(C.is(a)){const n=new z(a.edits,this._changeAnnotations);this._textEditChanges[a.textDocument.uri]=n}})):r.changes&&Object.keys(r.changes).forEach(a=>{const n=new z(r.changes[a]);this._textEditChanges[a]=n})):this._workspaceEdit={}}get edit(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit}getTextEditChange(r){if(J.is(r)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");const a={uri:r.uri,version:r.version};let n=this._textEditChanges[a.uri];if(!n){const e=[],x={textDocument:a,edits:e};this._workspaceEdit.documentChanges.push(x),n=new z(e,this._changeAnnotations),this._textEditChanges[a.uri]=n}return n}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");let a=this._textEditChanges[r];if(!a){let n=[];this._workspaceEdit.changes[r]=n,a=new z(n),this._textEditChanges[r]=a}return a}}initDocumentChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new H,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())}initChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))}createFile(r,a,n){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let e;c.is(a)||i.is(a)?e=a:n=a;let x,V;if(e===void 0?x=I.create(r,n):(V=i.is(e)?e:this._changeAnnotations.manage(e),x=I.create(r,n,V)),this._workspaceEdit.documentChanges.push(x),V!==void 0)return V}renameFile(r,a,n,e){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let x;c.is(n)||i.is(n)?x=n:e=n;let V,de;if(x===void 0?V=S.create(r,a,e):(de=i.is(x)?x:this._changeAnnotations.manage(x),V=S.create(r,a,e,de)),this._workspaceEdit.documentChanges.push(V),de!==void 0)return de}deleteFile(r,a,n){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let e;c.is(a)||i.is(a)?e=a:n=a;let x,V;if(e===void 0?x=G.create(r,n):(V=i.is(e)?e:this._changeAnnotations.manage(e),x=G.create(r,n,V)),this._workspaceEdit.documentChanges.push(x),V!==void 0)return V}}var q;(function(t){function r(n){return{uri:n}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)}t.is=a})(q||(q={}));var M;(function(t){function r(n,e){return{uri:n,version:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)&&s.integer(e.version)}t.is=a})(M||(M={}));var J;(function(t){function r(n,e){return{uri:n,version:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)&&(e.version===null||s.integer(e.version))}t.is=a})(J||(J={}));var te;(function(t){function r(n,e,x,V){return{uri:n,languageId:e,version:x,text:V}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.string(e.uri)&&s.string(e.languageId)&&s.integer(e.version)&&s.string(e.text)}t.is=a})(te||(te={}));var ye;(function(t){t.PlainText="plaintext",t.Markdown="markdown";function r(a){const n=a;return n===t.PlainText||n===t.Markdown}t.is=r})(ye||(ye={}));var fe;(function(t){function r(a){const n=a;return s.objectLiteral(a)&&ye.is(n.kind)&&s.string(n.value)}t.is=r})(fe||(fe={}));var oe;(function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25})(oe||(oe={}));var B;(function(t){t.PlainText=1,t.Snippet=2})(B||(B={}));var X;(function(t){t.Deprecated=1})(X||(X={}));var se;(function(t){function r(n,e,x){return{newText:n,insert:e,replace:x}}t.create=r;function a(n){const e=n;return e&&s.string(e.newText)&&W.is(e.insert)&&W.is(e.replace)}t.is=a})(se||(se={}));var le;(function(t){t.asIs=1,t.adjustIndentation=2})(le||(le={}));var pe;(function(t){function r(a){const n=a;return n&&(s.string(n.detail)||n.detail===void 0)&&(s.string(n.description)||n.description===void 0)}t.is=r})(pe||(pe={}));var me;(function(t){function r(a){return{label:a}}t.create=r})(me||(me={}));var ge;(function(t){function r(a,n){return{items:a||[],isIncomplete:!!n}}t.create=r})(ge||(ge={}));var Ee;(function(t){function r(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}t.fromPlainText=r;function a(n){const e=n;return s.string(e)||s.objectLiteral(e)&&s.string(e.language)&&s.string(e.value)}t.is=a})(Ee||(Ee={}));var _e;(function(t){function r(a){let n=a;return!!n&&s.objectLiteral(n)&&(fe.is(n.contents)||Ee.is(n.contents)||s.typedArray(n.contents,Ee.is))&&(a.range===void 0||W.is(a.range))}t.is=r})(_e||(_e={}));var Ce;(function(t){function r(a,n){return n?{label:a,documentation:n}:{label:a}}t.create=r})(Ce||(Ce={}));var De;(function(t){function r(a,n,...e){let x={label:a};return s.defined(n)&&(x.documentation=n),s.defined(e)?x.parameters=e:x.parameters=[],x}t.create=r})(De||(De={}));var Ie;(function(t){t.Text=1,t.Read=2,t.Write=3})(Ie||(Ie={}));var Me;(function(t){function r(a,n){let e={range:a};return s.number(n)&&(e.kind=n),e}t.create=r})(Me||(Me={}));var Le;(function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26})(Le||(Le={}));var Pe;(function(t){t.Deprecated=1})(Pe||(Pe={}));var Ze;(function(t){function r(a,n,e,x,V){let de={name:a,kind:n,location:{uri:x,range:e}};return V&&(de.containerName=V),de}t.create=r})(Ze||(Ze={}));var ke;(function(t){function r(a,n,e,x){return x!==void 0?{name:a,kind:n,location:{uri:e,range:x}}:{name:a,kind:n,location:{uri:e}}}t.create=r})(ke||(ke={}));var We;(function(t){function r(n,e,x,V,de,xe){let Ae={name:n,detail:e,kind:x,range:V,selectionRange:de};return xe!==void 0&&(Ae.children=xe),Ae}t.create=r;function a(n){let e=n;return e&&s.string(e.name)&&s.number(e.kind)&&W.is(e.range)&&W.is(e.selectionRange)&&(e.detail===void 0||s.string(e.detail))&&(e.deprecated===void 0||s.boolean(e.deprecated))&&(e.children===void 0||Array.isArray(e.children))&&(e.tags===void 0||Array.isArray(e.tags))}t.is=a})(We||(We={}));var $e;(function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"})($e||($e={}));var je;(function(t){t.Invoked=1,t.Automatic=2})(je||(je={}));var qe;(function(t){function r(n,e,x){let V={diagnostics:n};return e!=null&&(V.only=e),x!=null&&(V.triggerKind=x),V}t.create=r;function a(n){let e=n;return s.defined(e)&&s.typedArray(e.diagnostics,O.is)&&(e.only===void 0||s.typedArray(e.only,s.string))&&(e.triggerKind===void 0||e.triggerKind===je.Invoked||e.triggerKind===je.Automatic)}t.is=a})(qe||(qe={}));var He;(function(t){function r(n,e,x){let V={title:n},de=!0;return typeof e=="string"?(de=!1,V.kind=e):T.is(e)?V.command=e:V.edit=e,de&&x!==void 0&&(V.kind=x),V}t.create=r;function a(n){let e=n;return e&&s.string(e.title)&&(e.diagnostics===void 0||s.typedArray(e.diagnostics,O.is))&&(e.kind===void 0||s.string(e.kind))&&(e.edit!==void 0||e.command!==void 0)&&(e.command===void 0||T.is(e.command))&&(e.isPreferred===void 0||s.boolean(e.isPreferred))&&(e.edit===void 0||U.is(e.edit))}t.is=a})(He||(He={}));var Be;(function(t){function r(n,e){let x={range:n};return s.defined(e)&&(x.data=e),x}t.create=r;function a(n){let e=n;return s.defined(e)&&W.is(e.range)&&(s.undefined(e.command)||T.is(e.command))}t.is=a})(Be||(Be={}));var Ne;(function(t){function r(n,e){return{tabSize:n,insertSpaces:e}}t.create=r;function a(n){let e=n;return s.defined(e)&&s.uinteger(e.tabSize)&&s.boolean(e.insertSpaces)}t.is=a})(Ne||(Ne={}));var Se;(function(t){function r(n,e,x){return{range:n,target:e,data:x}}t.create=r;function a(n){let e=n;return s.defined(e)&&W.is(e.range)&&(s.undefined(e.target)||s.string(e.target))}t.is=a})(Se||(Se={}));var ze;(function(t){function r(n,e){return{range:n,parent:e}}t.create=r;function a(n){let e=n;return s.objectLiteral(e)&&W.is(e.range)&&(e.parent===void 0||t.is(e.parent))}t.is=a})(ze||(ze={}));var Ve;(function(t){t.namespace="namespace",t.type="type",t.class="class",t.enum="enum",t.interface="interface",t.struct="struct",t.typeParameter="typeParameter",t.parameter="parameter",t.variable="variable",t.property="property",t.enumMember="enumMember",t.event="event",t.function="function",t.method="method",t.macro="macro",t.keyword="keyword",t.modifier="modifier",t.comment="comment",t.string="string",t.number="number",t.regexp="regexp",t.operator="operator",t.decorator="decorator"})(Ve||(Ve={}));var u;(function(t){t.declaration="declaration",t.definition="definition",t.readonly="readonly",t.static="static",t.deprecated="deprecated",t.abstract="abstract",t.async="async",t.modification="modification",t.documentation="documentation",t.defaultLibrary="defaultLibrary"})(u||(u={}));var D;(function(t){function r(a){const n=a;return s.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}t.is=r})(D||(D={}));var E;(function(t){function r(n,e){return{range:n,text:e}}t.create=r;function a(n){const e=n;return e!=null&&W.is(e.range)&&s.string(e.text)}t.is=a})(E||(E={}));var k;(function(t){function r(n,e,x){return{range:n,variableName:e,caseSensitiveLookup:x}}t.create=r;function a(n){const e=n;return e!=null&&W.is(e.range)&&s.boolean(e.caseSensitiveLookup)&&(s.string(e.variableName)||e.variableName===void 0)}t.is=a})(k||(k={}));var Z;(function(t){function r(n,e){return{range:n,expression:e}}t.create=r;function a(n){const e=n;return e!=null&&W.is(e.range)&&(s.string(e.expression)||e.expression===void 0)}t.is=a})(Z||(Z={}));var re;(function(t){function r(n,e){return{frameId:n,stoppedLocation:e}}t.create=r;function a(n){const e=n;return s.defined(e)&&W.is(n.stoppedLocation)}t.is=a})(re||(re={}));var ce;(function(t){t.Type=1,t.Parameter=2;function r(a){return a===1||a===2}t.is=r})(ce||(ce={}));var ue;(function(t){function r(n){return{value:n}}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&(e.tooltip===void 0||s.string(e.tooltip)||fe.is(e.tooltip))&&(e.location===void 0||N.is(e.location))&&(e.command===void 0||T.is(e.command))}t.is=a})(ue||(ue={}));var he;(function(t){function r(n,e,x){const V={position:n,label:e};return x!==void 0&&(V.kind=x),V}t.create=r;function a(n){const e=n;return s.objectLiteral(e)&&j.is(e.position)&&(s.string(e.label)||s.typedArray(e.label,ue.is))&&(e.kind===void 0||ce.is(e.kind))&&e.textEdits===void 0||s.typedArray(e.textEdits,v.is)&&(e.tooltip===void 0||s.string(e.tooltip)||fe.is(e.tooltip))&&(e.paddingLeft===void 0||s.boolean(e.paddingLeft))&&(e.paddingRight===void 0||s.boolean(e.paddingRight))}t.is=a})(he||(he={}));var we;(function(t){function r(a){return{kind:"snippet",value:a}}t.createSnippet=r})(we||(we={}));var Re;(function(t){function r(a,n,e,x){return{insertText:a,filterText:n,range:e,command:x}}t.create=r})(Re||(Re={}));var Ue;(function(t){function r(a){return{items:a}}t.create=r})(Ue||(Ue={}));var l;(function(t){t.Invoked=0,t.Automatic=1})(l||(l={}));var h;(function(t){function r(a,n){return{range:a,text:n}}t.create=r})(h||(h={}));var y;(function(t){function r(a,n){return{triggerKind:a,selectedCompletionInfo:n}}t.create=r})(y||(y={}));var $;(function(t){function r(a){const n=a;return s.objectLiteral(n)&&F.is(n.uri)&&s.string(n.name)}t.is=r})($||($={}));const Y=null;var ie;(function(t){function r(x,V,de,xe){return new ne(x,V,de,xe)}t.create=r;function a(x){let V=x;return!!(s.defined(V)&&s.string(V.uri)&&(s.undefined(V.languageId)||s.string(V.languageId))&&s.uinteger(V.lineCount)&&s.func(V.getText)&&s.func(V.positionAt)&&s.func(V.offsetAt))}t.is=a;function n(x,V){let de=x.getText(),xe=e(V,(Oe,Te)=>{let Fe=Oe.range.start.line-Te.range.start.line;return Fe===0?Oe.range.start.character-Te.range.start.character:Fe}),Ae=de.length;for(let Oe=xe.length-1;Oe>=0;Oe--){let Te=xe[Oe],Fe=x.offsetAt(Te.range.start),ve=x.offsetAt(Te.range.end);if(ve<=Ae)de=de.substring(0,Fe)+Te.newText+de.substring(ve,de.length);else throw new Error("Overlapping edit");Ae=Fe}return de}t.applyEdits=n;function e(x,V){if(x.length<=1)return x;const de=x.length/2|0,xe=x.slice(0,de),Ae=x.slice(de);e(xe,V),e(Ae,V);let Oe=0,Te=0,Fe=0;for(;Oe<xe.length&&Te<Ae.length;)V(xe[Oe],Ae[Te])<=0?x[Fe++]=xe[Oe++]:x[Fe++]=Ae[Te++];for(;Oe<xe.length;)x[Fe++]=xe[Oe++];for(;Te<Ae.length;)x[Fe++]=Ae[Te++];return x}})(ie||(ie={}));class ne{constructor(r,a,n,e){this._uri=r,this._languageId=a,this._version=n,this._content=e,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(r){if(r){let a=this.offsetAt(r.start),n=this.offsetAt(r.end);return this._content.substring(a,n)}return this._content}update(r,a){this._content=r.text,this._version=a,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let r=[],a=this._content,n=!0;for(let e=0;e<a.length;e++){n&&(r.push(e),n=!1);let x=a.charAt(e);n=x==="\r"||x===`
`,x==="\r"&&e+1<a.length&&a.charAt(e+1)===`
`&&e++}n&&a.length>0&&r.push(a.length),this._lineOffsets=r}return this._lineOffsets}positionAt(r){r=Math.max(Math.min(r,this._content.length),0);let a=this.getLineOffsets(),n=0,e=a.length;if(e===0)return j.create(0,r);for(;n<e;){let V=Math.floor((n+e)/2);a[V]>r?e=V:n=V+1}let x=n-1;return j.create(x,r-a[x])}offsetAt(r){let a=this.getLineOffsets();if(r.line>=a.length)return this._content.length;if(r.line<0)return 0;let n=a[r.line],e=r.line+1<a.length?a[r.line+1]:this._content.length;return Math.max(Math.min(n+r.character,e),n)}get lineCount(){return this.getLineOffsets().length}}var s;(function(t){const r=Object.prototype.toString;function a(ve){return typeof ve!="undefined"}t.defined=a;function n(ve){return typeof ve=="undefined"}t.undefined=n;function e(ve){return ve===!0||ve===!1}t.boolean=e;function x(ve){return r.call(ve)==="[object String]"}t.string=x;function V(ve){return r.call(ve)==="[object Number]"}t.number=V;function de(ve,Je,Ye){return r.call(ve)==="[object Number]"&&Je<=ve&&ve<=Ye}t.numberRange=de;function xe(ve){return r.call(ve)==="[object Number]"&&-2147483648<=ve&&ve<=2147483647}t.integer=xe;function Ae(ve){return r.call(ve)==="[object Number]"&&0<=ve&&ve<=2147483647}t.uinteger=Ae;function Oe(ve){return r.call(ve)==="[object Function]"}t.func=Oe;function Te(ve){return ve!==null&&typeof ve=="object"}t.objectLiteral=Te;function Fe(ve,Je){return Array.isArray(ve)&&ve.every(Je)}t.typedArray=Fe})(s||(s={}))},43545:function(be,Q,L){"use strict";L.d(Q,{c:function(){return P},o:function(){return K}});var ee=L(73656),F;(()=>{"use strict";var j={470:_=>{function R(o){if(typeof o!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(o))}function g(o,m){for(var p,b="",d=0,O=-1,T=0,v=0;v<=o.length;++v){if(v<o.length)p=o.charCodeAt(v);else{if(p===47)break;p=47}if(p===47){if(!(O===v-1||T===1))if(O!==v-1&&T===2){if(b.length<2||d!==2||b.charCodeAt(b.length-1)!==46||b.charCodeAt(b.length-2)!==46){if(b.length>2){var c=b.lastIndexOf("/");if(c!==b.length-1){c===-1?(b="",d=0):d=(b=b.slice(0,c)).length-1-b.lastIndexOf("/"),O=v,T=0;continue}}else if(b.length===2||b.length===1){b="",d=0,O=v,T=0;continue}}m&&(b.length>0?b+="/..":b="..",d=2)}else b.length>0?b+="/"+o.slice(O+1,v):b=o.slice(O+1,v),d=v-O-1;O=v,T=0}else p===46&&T!==-1?++T:T=-1}return b}var w={resolve:function(){for(var o,m="",p=!1,b=arguments.length-1;b>=-1&&!p;b--){var d;b>=0?d=arguments[b]:(o===void 0&&(o=ee.cwd()),d=o),R(d),d.length!==0&&(m=d+"/"+m,p=d.charCodeAt(0)===47)}return m=g(m,!p),p?m.length>0?"/"+m:"/":m.length>0?m:"."},normalize:function(o){if(R(o),o.length===0)return".";var m=o.charCodeAt(0)===47,p=o.charCodeAt(o.length-1)===47;return(o=g(o,!m)).length!==0||m||(o="."),o.length>0&&p&&(o+="/"),m?"/"+o:o},isAbsolute:function(o){return R(o),o.length>0&&o.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var o,m=0;m<arguments.length;++m){var p=arguments[m];R(p),p.length>0&&(o===void 0?o=p:o+="/"+p)}return o===void 0?".":w.normalize(o)},relative:function(o,m){if(R(o),R(m),o===m||(o=w.resolve(o))===(m=w.resolve(m)))return"";for(var p=1;p<o.length&&o.charCodeAt(p)===47;++p);for(var b=o.length,d=b-p,O=1;O<m.length&&m.charCodeAt(O)===47;++O);for(var T=m.length-O,v=d<T?d:T,c=-1,i=0;i<=v;++i){if(i===v){if(T>v){if(m.charCodeAt(O+i)===47)return m.slice(O+i+1);if(i===0)return m.slice(O+i)}else d>v&&(o.charCodeAt(p+i)===47?c=i:i===0&&(c=0));break}var f=o.charCodeAt(p+i);if(f!==m.charCodeAt(O+i))break;f===47&&(c=i)}var C="";for(i=p+c+1;i<=b;++i)i!==b&&o.charCodeAt(i)!==47||(C.length===0?C+="..":C+="/..");return C.length>0?C+m.slice(O+c):(O+=c,m.charCodeAt(O)===47&&++O,m.slice(O))},_makeLong:function(o){return o},dirname:function(o){if(R(o),o.length===0)return".";for(var m=o.charCodeAt(0),p=m===47,b=-1,d=!0,O=o.length-1;O>=1;--O)if((m=o.charCodeAt(O))===47){if(!d){b=O;break}}else d=!1;return b===-1?p?"/":".":p&&b===1?"//":o.slice(0,b)},basename:function(o,m){if(m!==void 0&&typeof m!="string")throw new TypeError('"ext" argument must be a string');R(o);var p,b=0,d=-1,O=!0;if(m!==void 0&&m.length>0&&m.length<=o.length){if(m.length===o.length&&m===o)return"";var T=m.length-1,v=-1;for(p=o.length-1;p>=0;--p){var c=o.charCodeAt(p);if(c===47){if(!O){b=p+1;break}}else v===-1&&(O=!1,v=p+1),T>=0&&(c===m.charCodeAt(T)?--T==-1&&(d=p):(T=-1,d=v))}return b===d?d=v:d===-1&&(d=o.length),o.slice(b,d)}for(p=o.length-1;p>=0;--p)if(o.charCodeAt(p)===47){if(!O){b=p+1;break}}else d===-1&&(O=!1,d=p+1);return d===-1?"":o.slice(b,d)},extname:function(o){R(o);for(var m=-1,p=0,b=-1,d=!0,O=0,T=o.length-1;T>=0;--T){var v=o.charCodeAt(T);if(v!==47)b===-1&&(d=!1,b=T+1),v===46?m===-1?m=T:O!==1&&(O=1):m!==-1&&(O=-1);else if(!d){p=T+1;break}}return m===-1||b===-1||O===0||O===1&&m===b-1&&m===p+1?"":o.slice(m,b)},format:function(o){if(o===null||typeof o!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof o);return function(m,p){var b=p.dir||p.root,d=p.base||(p.name||"")+(p.ext||"");return b?b===p.root?b+d:b+"/"+d:d}(0,o)},parse:function(o){R(o);var m={root:"",dir:"",base:"",ext:"",name:""};if(o.length===0)return m;var p,b=o.charCodeAt(0),d=b===47;d?(m.root="/",p=1):p=0;for(var O=-1,T=0,v=-1,c=!0,i=o.length-1,f=0;i>=p;--i)if((b=o.charCodeAt(i))!==47)v===-1&&(c=!1,v=i+1),b===46?O===-1?O=i:f!==1&&(f=1):O!==-1&&(f=-1);else if(!c){T=i+1;break}return O===-1||v===-1||f===0||f===1&&O===v-1&&O===T+1?v!==-1&&(m.base=m.name=T===0&&d?o.slice(1,v):o.slice(T,v)):(T===0&&d?(m.name=o.slice(1,O),m.base=o.slice(1,v)):(m.name=o.slice(T,O),m.base=o.slice(T,v)),m.ext=o.slice(O,v)),T>0?m.dir=o.slice(0,T-1):d&&(m.dir="/"),m},sep:"/",delimiter:":",win32:null,posix:null};w.posix=w,_.exports=w}},W={};function N(_){var R=W[_];if(R!==void 0)return R.exports;var g=W[_]={exports:{}};return j[_](g,g.exports,N),g.exports}N.d=(_,R)=>{for(var g in R)N.o(R,g)&&!N.o(_,g)&&Object.defineProperty(_,g,{enumerable:!0,get:R[g]})},N.o=(_,R)=>Object.prototype.hasOwnProperty.call(_,R),N.r=_=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(_,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(_,"__esModule",{value:!0})};var A={};(()=>{let _;N.r(A),N.d(A,{URI:()=>d,Utils:()=>ae}),typeof ee=="object"?_=ee.platform==="win32":typeof navigator=="object"&&(_=navigator.userAgent.indexOf("Windows")>=0);const R=/^\w[\w\d+.-]*$/,g=/^\//,w=/^\/\//;function o(q,M){if(!q.scheme&&M)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${q.authority}", path: "${q.path}", query: "${q.query}", fragment: "${q.fragment}"}`);if(q.scheme&&!R.test(q.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(q.path){if(q.authority){if(!g.test(q.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(w.test(q.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const m="",p="/",b=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class d{constructor(M,J,te,ye,fe,oe=!1){Ge(this,"scheme");Ge(this,"authority");Ge(this,"path");Ge(this,"query");Ge(this,"fragment");typeof M=="object"?(this.scheme=M.scheme||m,this.authority=M.authority||m,this.path=M.path||m,this.query=M.query||m,this.fragment=M.fragment||m):(this.scheme=function(B,X){return B||X?B:"file"}(M,oe),this.authority=J||m,this.path=function(B,X){switch(B){case"https":case"http":case"file":X?X[0]!==p&&(X=p+X):X=p}return X}(this.scheme,te||m),this.query=ye||m,this.fragment=fe||m,o(this,oe))}static isUri(M){return M instanceof d||!!M&&typeof M.authority=="string"&&typeof M.fragment=="string"&&typeof M.path=="string"&&typeof M.query=="string"&&typeof M.scheme=="string"&&typeof M.fsPath=="string"&&typeof M.with=="function"&&typeof M.toString=="function"}get fsPath(){return f(this,!1)}with(M){if(!M)return this;let{scheme:J,authority:te,path:ye,query:fe,fragment:oe}=M;return J===void 0?J=this.scheme:J===null&&(J=m),te===void 0?te=this.authority:te===null&&(te=m),ye===void 0?ye=this.path:ye===null&&(ye=m),fe===void 0?fe=this.query:fe===null&&(fe=m),oe===void 0?oe=this.fragment:oe===null&&(oe=m),J===this.scheme&&te===this.authority&&ye===this.path&&fe===this.query&&oe===this.fragment?this:new T(J,te,ye,fe,oe)}static parse(M,J=!1){const te=b.exec(M);return te?new T(te[2]||m,G(te[4]||m),G(te[5]||m),G(te[7]||m),G(te[9]||m),J):new T(m,m,m,m,m)}static file(M){let J=m;if(_&&(M=M.replace(/\\/g,p)),M[0]===p&&M[1]===p){const te=M.indexOf(p,2);te===-1?(J=M.substring(2),M=p):(J=M.substring(2,te),M=M.substring(te)||p)}return new T("file",J,M,m,m)}static from(M){const J=new T(M.scheme,M.authority,M.path,M.query,M.fragment);return o(J,!0),J}toString(M=!1){return C(this,M)}toJSON(){return this}static revive(M){if(M){if(M instanceof d)return M;{const J=new T(M);return J._formatted=M.external,J._fsPath=M._sep===O?M.fsPath:null,J}}return M}}const O=_?1:void 0;class T extends d{constructor(){super(...arguments);Ge(this,"_formatted",null);Ge(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=f(this,!1)),this._fsPath}toString(J=!1){return J?C(this,!0):(this._formatted||(this._formatted=C(this,!1)),this._formatted)}toJSON(){const J={$mid:1};return this._fsPath&&(J.fsPath=this._fsPath,J._sep=O),this._formatted&&(J.external=this._formatted),this.path&&(J.path=this.path),this.scheme&&(J.scheme=this.scheme),this.authority&&(J.authority=this.authority),this.query&&(J.query=this.query),this.fragment&&(J.fragment=this.fragment),J}}const v={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function c(q,M,J){let te,ye=-1;for(let fe=0;fe<q.length;fe++){const oe=q.charCodeAt(fe);if(oe>=97&&oe<=122||oe>=65&&oe<=90||oe>=48&&oe<=57||oe===45||oe===46||oe===95||oe===126||M&&oe===47||J&&oe===91||J&&oe===93||J&&oe===58)ye!==-1&&(te+=encodeURIComponent(q.substring(ye,fe)),ye=-1),te!==void 0&&(te+=q.charAt(fe));else{te===void 0&&(te=q.substr(0,fe));const B=v[oe];B!==void 0?(ye!==-1&&(te+=encodeURIComponent(q.substring(ye,fe)),ye=-1),te+=B):ye===-1&&(ye=fe)}}return ye!==-1&&(te+=encodeURIComponent(q.substring(ye))),te!==void 0?te:q}function i(q){let M;for(let J=0;J<q.length;J++){const te=q.charCodeAt(J);te===35||te===63?(M===void 0&&(M=q.substr(0,J)),M+=v[te]):M!==void 0&&(M+=q[J])}return M!==void 0?M:q}function f(q,M){let J;return J=q.authority&&q.path.length>1&&q.scheme==="file"?`//${q.authority}${q.path}`:q.path.charCodeAt(0)===47&&(q.path.charCodeAt(1)>=65&&q.path.charCodeAt(1)<=90||q.path.charCodeAt(1)>=97&&q.path.charCodeAt(1)<=122)&&q.path.charCodeAt(2)===58?M?q.path.substr(1):q.path[1].toLowerCase()+q.path.substr(2):q.path,_&&(J=J.replace(/\//g,"\\")),J}function C(q,M){const J=M?i:c;let te="",{scheme:ye,authority:fe,path:oe,query:B,fragment:X}=q;if(ye&&(te+=ye,te+=":"),(fe||ye==="file")&&(te+=p,te+=p),fe){let se=fe.indexOf("@");if(se!==-1){const le=fe.substr(0,se);fe=fe.substr(se+1),se=le.lastIndexOf(":"),se===-1?te+=J(le,!1,!1):(te+=J(le.substr(0,se),!1,!1),te+=":",te+=J(le.substr(se+1),!1,!0)),te+="@"}fe=fe.toLowerCase(),se=fe.lastIndexOf(":"),se===-1?te+=J(fe,!1,!0):(te+=J(fe.substr(0,se),!1,!0),te+=fe.substr(se))}if(oe){if(oe.length>=3&&oe.charCodeAt(0)===47&&oe.charCodeAt(2)===58){const se=oe.charCodeAt(1);se>=65&&se<=90&&(oe=`/${String.fromCharCode(se+32)}:${oe.substr(3)}`)}else if(oe.length>=2&&oe.charCodeAt(1)===58){const se=oe.charCodeAt(0);se>=65&&se<=90&&(oe=`${String.fromCharCode(se+32)}:${oe.substr(2)}`)}te+=J(oe,!0,!1)}return B&&(te+="?",te+=J(B,!1,!1)),X&&(te+="#",te+=M?X:c(X,!1,!1)),te}function I(q){try{return decodeURIComponent(q)}catch(M){return q.length>3?q.substr(0,3)+I(q.substr(3)):q}}const S=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function G(q){return q.match(S)?q.replace(S,M=>I(M)):q}var U=N(470);const z=U.posix||U,H="/";var ae;(function(q){q.joinPath=function(M,...J){return M.with({path:z.join(M.path,...J)})},q.resolvePath=function(M,...J){let te=M.path,ye=!1;te[0]!==H&&(te=H+te,ye=!0);let fe=z.resolve(te,...J);return ye&&fe[0]===H&&!M.authority&&(fe=fe.substring(1)),M.with({path:fe})},q.dirname=function(M){if(M.path.length===0||M.path===H)return M;let J=z.dirname(M.path);return J.length===1&&J.charCodeAt(0)===46&&(J=""),M.with({path:J})},q.basename=function(M){return z.basename(M.path)},q.extname=function(M){return z.extname(M.path)}})(ae||(ae={}))})(),F=A})();const{URI:K,Utils:P}=F}}]);
}());