"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[937],{77167:function(Oe,Ht,Z){Z.r(Ht),Z.d(Ht,{genCalc:function(){return Ze},genStyleUtils:function(){return Le},mergeToken:function(){return Ot},statistic:function(){return Yt},statisticToken:function(){return ee}});var S=Z(68874),b=Z(56840),D=Z(94450),M=Z(28659),q=Z(44194),W=Z(68086),w=Z(65096),o=Z(46509),u=Z(33485),d=Z(70647),y=Z(63009),R=(0,o.Z)(function k(){(0,w.Z)(this,k)}),$=R,J="CALC_UNIT",F=new RegExp(J,"g");function K(k){return typeof k=="number"?"".concat(k).concat(J):k}var ot=function(k){(0,d.Z)(h,k);var E=(0,y.Z)(h);function h(v,s){var P;(0,w.Z)(this,h),P=E.call(this),(0,D.Z)((0,u.Z)(P),"result",""),(0,D.Z)((0,u.Z)(P),"unitlessCssVar",void 0),(0,D.Z)((0,u.Z)(P),"lowPriority",void 0);var B=(0,S.Z)(v);return P.unitlessCssVar=s,v instanceof h?P.result="(".concat(v.result,")"):B==="number"?P.result=K(v):B==="string"&&(P.result=v),P}return(0,o.Z)(h,[{key:"add",value:function(s){return s instanceof h?this.result="".concat(this.result," + ").concat(s.getResult()):(typeof s=="number"||typeof s=="string")&&(this.result="".concat(this.result," + ").concat(K(s))),this.lowPriority=!0,this}},{key:"sub",value:function(s){return s instanceof h?this.result="".concat(this.result," - ").concat(s.getResult()):(typeof s=="number"||typeof s=="string")&&(this.result="".concat(this.result," - ").concat(K(s))),this.lowPriority=!0,this}},{key:"mul",value:function(s){return this.lowPriority&&(this.result="(".concat(this.result,")")),s instanceof h?this.result="".concat(this.result," * ").concat(s.getResult(!0)):(typeof s=="number"||typeof s=="string")&&(this.result="".concat(this.result," * ").concat(s)),this.lowPriority=!1,this}},{key:"div",value:function(s){return this.lowPriority&&(this.result="(".concat(this.result,")")),s instanceof h?this.result="".concat(this.result," / ").concat(s.getResult(!0)):(typeof s=="number"||typeof s=="string")&&(this.result="".concat(this.result," / ").concat(s)),this.lowPriority=!1,this}},{key:"getResult",value:function(s){return this.lowPriority||s?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(s){var P=this,B=s||{},z=B.unit,_=!0;return typeof z=="boolean"?_=z:Array.from(this.unitlessCssVar).some(function(Rt){return P.result.includes(Rt)})&&(_=!1),this.result=this.result.replace(F,_?"px":""),typeof this.lowPriority!="undefined"?"calc(".concat(this.result,")"):this.result}}]),h}($),Ct=function(k){(0,d.Z)(h,k);var E=(0,y.Z)(h);function h(v){var s;return(0,w.Z)(this,h),s=E.call(this),(0,D.Z)((0,u.Z)(s),"result",0),v instanceof h?s.result=v.result:typeof v=="number"&&(s.result=v),s}return(0,o.Z)(h,[{key:"add",value:function(s){return s instanceof h?this.result+=s.result:typeof s=="number"&&(this.result+=s),this}},{key:"sub",value:function(s){return s instanceof h?this.result-=s.result:typeof s=="number"&&(this.result-=s),this}},{key:"mul",value:function(s){return s instanceof h?this.result*=s.result:typeof s=="number"&&(this.result*=s),this}},{key:"div",value:function(s){return s instanceof h?this.result/=s.result:typeof s=="number"&&(this.result/=s),this}},{key:"equal",value:function(){return this.result}}]),h}($),gt=Ct,ht=function(E,h){var v=E==="css"?ot:gt;return function(s){return new v(s,h)}},Ze=ht,Et=function(E,h){return"".concat([h,E.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))},Gt=Et,le=Z(3389);function xe(k,E,h,v){var s=(0,M.Z)({},E[k]);if(v!=null&&v.deprecatedTokens){var P=v.deprecatedTokens;P.forEach(function(z){var _=(0,b.Z)(z,2),Rt=_[0],Ut=_[1];if(s!=null&&s[Rt]||s!=null&&s[Ut]){var It;(It=s[Ut])!==null&&It!==void 0||(s[Ut]=s==null?void 0:s[Rt])}})}var B=(0,M.Z)((0,M.Z)({},h),s);return Object.keys(B).forEach(function(z){B[z]===E[z]&&delete B[z]}),B}var wt=xe,Nt=typeof CSSINJS_STATISTIC!="undefined",$t=!0;function Ot(){for(var k=arguments.length,E=new Array(k),h=0;h<k;h++)E[h]=arguments[h];if(!Nt)return Object.assign.apply(Object,[{}].concat(E));$t=!1;var v={};return E.forEach(function(s){if((0,S.Z)(s)==="object"){var P=Object.keys(s);P.forEach(function(B){Object.defineProperty(v,B,{configurable:!0,enumerable:!0,get:function(){return s[B]}})})}}),$t=!0,v}var Yt={},Te={};function ke(){}var ue=function(E){var h,v=E,s=ke;return Nt&&typeof Proxy!="undefined"&&(h=new Set,v=new Proxy(E,{get:function(B,z){if($t){var _;(_=h)===null||_===void 0||_.add(z)}return B[z]}}),s=function(B,z){var _;Yt[B]={global:Array.from(h),component:(0,M.Z)((0,M.Z)({},(_=Yt[B])===null||_===void 0?void 0:_.component),z)}}),{token:v,keys:h,flush:s}},ee=ue;function Ee(k,E,h){if(typeof h=="function"){var v;return h(Ot(E,(v=E[k])!==null&&v!==void 0?v:{}))}return h!=null?h:{}}var Qt=Ee;function Re(k){return k==="js"?{max:Math.max,min:Math.min}:{max:function(){for(var h=arguments.length,v=new Array(h),s=0;s<h;s++)v[s]=arguments[s];return"max(".concat(v.map(function(P){return(0,W.unit)(P)}).join(","),")")},min:function(){for(var h=arguments.length,v=new Array(h),s=0;s<h;s++)v[s]=arguments[s];return"min(".concat(v.map(function(P){return(0,W.unit)(P)}).join(","),")")}}}var Pe=Re,Ie=1e3*60*10,Ae=function(){function k(){(0,w.Z)(this,k),(0,D.Z)(this,"map",new Map),(0,D.Z)(this,"objectIDMap",new WeakMap),(0,D.Z)(this,"nextID",0),(0,D.Z)(this,"lastAccessBeat",new Map),(0,D.Z)(this,"accessBeat",0)}return(0,o.Z)(k,[{key:"set",value:function(h,v){this.clear();var s=this.getCompositeKey(h);this.map.set(s,v),this.lastAccessBeat.set(s,Date.now())}},{key:"get",value:function(h){var v=this.getCompositeKey(h),s=this.map.get(v);return this.lastAccessBeat.set(v,Date.now()),this.accessBeat+=1,s}},{key:"getCompositeKey",value:function(h){var v=this,s=h.map(function(P){return P&&(0,S.Z)(P)==="object"?"obj_".concat(v.getObjectID(P)):"".concat((0,S.Z)(P),"_").concat(P)});return s.join("|")}},{key:"getObjectID",value:function(h){if(this.objectIDMap.has(h))return this.objectIDMap.get(h);var v=this.nextID;return this.objectIDMap.set(h,v),this.nextID+=1,v}},{key:"clear",value:function(){var h=this;if(this.accessBeat>1e4){var v=Date.now();this.lastAccessBeat.forEach(function(s,P){v-s>Ie&&(h.map.delete(P),h.lastAccessBeat.delete(P))}),this.accessBeat=0}}}]),k}(),fe=new Ae;function re(k,E){return q.useMemo(function(){var h=fe.get(E);if(h)return h;var v=k();return fe.set(E,v),v},E)}var he=re,de=function(){return{}},ne=de;function ie(k){var E=k.useCSP,h=E===void 0?ne:E,v=k.useToken,s=k.usePrefix,P=k.getResetStyles,B=k.getCommonStyle,z=k.getCompUnitless;function _(Y,Zt,tt,G){var pt=Array.isArray(Y)?Y[0]:Y;function it(rt){return"".concat(String(pt)).concat(rt.slice(0,1).toUpperCase()).concat(rt.slice(1))}var at=(G==null?void 0:G.unitless)||{},Pt=typeof z=="function"?z(Y):{},mt=(0,M.Z)((0,M.Z)({},Pt),{},(0,D.Z)({},it("zIndexPopup"),!0));Object.keys(at).forEach(function(rt){mt[it(rt)]=at[rt]});var et=(0,M.Z)((0,M.Z)({},G),{},{unitless:mt,prefixToken:it}),At=Ut(Y,Zt,tt,et),st=Rt(pt,tt,et);return function(rt){var nt=arguments.length>1&&arguments[1]!==void 0?arguments[1]:rt,dt=At(rt,nt),Lt=(0,b.Z)(dt,2),X=Lt[1],Mt=st(nt),ct=(0,b.Z)(Mt,2),yt=ct[0],Xt=ct[1];return[yt,X,Xt]}}function Rt(Y,Zt,tt){var G=tt.unitless,pt=tt.injectStyle,it=pt===void 0?!0:pt,at=tt.prefixToken,Pt=tt.ignore,mt=function(st){var rt=st.rootCls,nt=st.cssVar,dt=nt===void 0?{}:nt,Lt=v(),X=Lt.realToken;return(0,W.useCSSVarRegister)({path:[Y],prefix:dt.prefix,key:dt.key,unitless:G,ignore:Pt,token:X,scope:rt},function(){var Mt=Qt(Y,X,Zt),ct=wt(Y,X,Mt,{deprecatedTokens:tt==null?void 0:tt.deprecatedTokens});return Object.keys(Mt).forEach(function(yt){ct[at(yt)]=ct[yt],delete ct[yt]}),ct}),null},et=function(st){var rt=v(),nt=rt.cssVar;return[function(dt){return it&&nt?q.createElement(q.Fragment,null,q.createElement(mt,{rootCls:st,cssVar:nt,component:Y}),dt):dt},nt==null?void 0:nt.key]};return et}function Ut(Y,Zt,tt){var G=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},pt=Array.isArray(Y)?Y:[Y,Y],it=(0,b.Z)(pt,1),at=it[0],Pt=pt.join("-"),mt=k.layer||{name:"antd"};return function(et){var At=arguments.length>1&&arguments[1]!==void 0?arguments[1]:et,st=v(),rt=st.theme,nt=st.realToken,dt=st.hashId,Lt=st.token,X=st.cssVar,Mt=s(),ct=Mt.rootPrefixCls,yt=Mt.iconPrefixCls,Xt=h(),ae=X?"css":"js",Me=he(function(){var jt=new Set;return X&&Object.keys(G.unitless||{}).forEach(function(Jt){jt.add((0,W.token2CSSVar)(Jt,X.prefix)),jt.add((0,W.token2CSSVar)(Jt,Gt(at,X.prefix)))}),Ze(ae,jt)},[ae,at,X==null?void 0:X.prefix]),je=Pe(ae),Ve=je.max,Ue=je.min,Be={theme:rt,token:Lt,hashId:dt,nonce:function(){return Xt.nonce},clientOnly:G.clientOnly,layer:mt,order:G.order||-999};typeof P=="function"&&(0,W.useStyleRegister)((0,M.Z)((0,M.Z)({},Be),{},{clientOnly:!1,path:["Shared",ct]}),function(){return P(Lt,{prefix:{rootPrefixCls:ct,iconPrefixCls:yt},csp:Xt})});var se=(0,W.useStyleRegister)((0,M.Z)((0,M.Z)({},Be),{},{path:[Pt,et,yt]}),function(){if(G.injectStyle===!1)return[];var jt=ee(Lt),Jt=jt.token,Vt=jt.flush,Wt=Qt(at,nt,tt),He=".".concat(et),ve=wt(at,nt,Wt,{deprecatedTokens:G.deprecatedTokens});X&&Wt&&(0,S.Z)(Wt)==="object"&&Object.keys(Wt).forEach(function(ye){Wt[ye]="var(".concat((0,W.token2CSSVar)(ye,Gt(at,X.prefix)),")")});var ge=Ot(Jt,{componentCls:He,prefixCls:et,iconCls:".".concat(yt),antCls:".".concat(ct),calc:Me,max:Ve,min:Ue},X?Wt:ve),pe=Zt(ge,{hashId:dt,prefixCls:et,rootPrefixCls:ct,iconPrefixCls:yt});Vt(at,ve);var me=typeof B=="function"?B(ge,et,At,G.resetFont):null;return[G.resetStyle===!1?null:me,pe]});return[se,dt]}}function It(Y,Zt,tt){var G=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},pt=Ut(Y,Zt,tt,(0,M.Z)({resetStyle:!1,order:-998},G)),it=function(Pt){var mt=Pt.prefixCls,et=Pt.rootCls,At=et===void 0?mt:et;return pt(mt,At),null};return it}return{genStyleHooks:_,genSubStyleComponent:It,genComponentStyleHook:Ut}}var Le=ie},68086:function(Oe,Ht,Z){Z.r(Ht),Z.d(Ht,{Keyframes:function(){return Er},NaNLinter:function(){return sr},StyleContext:function(){return wt},StyleProvider:function(){return xe},Theme:function(){return ne},_experimental:function(){return Vr},createCache:function(){return Gt},createTheme:function(){return Le},extractStyle:function(){return Tr},genCalc:function(){return Ae},getComputedToken:function(){return pe},legacyLogicalPropertiesTransformer:function(){return Ar},legacyNotSelectorLinter:function(){return rr},logicalPropertiesLinter:function(){return ir},parentSelectorLinter:function(){return cr},px2remTransformer:function(){return jr},token2CSSVar:function(){return at},unit:function(){return pt},useCSSVarRegister:function(){return Cr},useCacheToken:function(){return ye},useStyleRegister:function(){return mr}});var S=Z(56840),b=Z(94450),D=Z(18191),M=Z(28659),q=Z(77116),W=Z(19524),w=Z(44194),o=Z.t(w,2),u=Z(60505),d=Z(65010),y=Z(35564),R=Z(65096),$=Z(46509),J="%";function F(i){return i.join(J)}var K=function(){function i(n){(0,R.Z)(this,i),(0,b.Z)(this,"instanceId",void 0),(0,b.Z)(this,"cache",new Map),this.instanceId=n}return(0,$.Z)(i,[{key:"get",value:function(t){return this.opGet(F(t))}},{key:"opGet",value:function(t){return this.cache.get(t)||null}},{key:"update",value:function(t,r){return this.opUpdate(F(t),r)}},{key:"opUpdate",value:function(t,r){var e=this.cache.get(t),a=r(e);a===null?this.cache.delete(t):this.cache.set(t,a)}}]),i}(),ot=K,Ct=["children"],gt="data-token-hash",ht="data-css-hash",Ze="data-cache-path",Et="__cssinjs_instance__";function Gt(){var i=Math.random().toString(12).slice(2);if(typeof document!="undefined"&&document.head&&document.body){var n=document.body.querySelectorAll("style[".concat(ht,"]"))||[],t=document.head.firstChild;Array.from(n).forEach(function(e){e[Et]=e[Et]||i,e[Et]===i&&document.head.insertBefore(e,t)});var r={};Array.from(document.querySelectorAll("style[".concat(ht,"]"))).forEach(function(e){var a=e.getAttribute(ht);if(r[a]){if(e[Et]===i){var c;(c=e.parentNode)===null||c===void 0||c.removeChild(e)}}else r[a]=!0})}return new ot(i)}var le=w.createContext({hashPriority:"low",cache:Gt(),defaultCache:!0}),xe=function(n){var t=n.children,r=(0,u.Z)(n,Ct),e=w.useContext(le),a=(0,d.Z)(function(){var c=(0,M.Z)({},e);Object.keys(r).forEach(function(l){var p=r[l];r[l]!==void 0&&(c[l]=p)});var f=r.cache;return c.cache=c.cache||Gt(),c.defaultCache=!f&&e.defaultCache,c},[e,r],function(c,f){return!(0,y.Z)(c[0],f[0],!0)||!(0,y.Z)(c[1],f[1],!0)});return w.createElement(le.Provider,{value:a},t)},wt=le,Nt=Z(68874),$t=Z(88340),Ot=Z(33485),Yt=Z(70647),Te=Z(63009),ke=(0,$.Z)(function i(){(0,R.Z)(this,i)}),ue=ke,ee="CALC_UNIT",Ee=new RegExp(ee,"g");function Qt(i){return typeof i=="number"?"".concat(i).concat(ee):i}var Re=function(i){(0,Yt.Z)(t,i);var n=(0,Te.Z)(t);function t(r,e){var a;(0,R.Z)(this,t),a=n.call(this),(0,b.Z)((0,Ot.Z)(a),"result",""),(0,b.Z)((0,Ot.Z)(a),"unitlessCssVar",void 0),(0,b.Z)((0,Ot.Z)(a),"lowPriority",void 0);var c=(0,Nt.Z)(r);return a.unitlessCssVar=e,r instanceof t?a.result="(".concat(r.result,")"):c==="number"?a.result=Qt(r):c==="string"&&(a.result=r),a}return(0,$.Z)(t,[{key:"add",value:function(e){return e instanceof t?this.result="".concat(this.result," + ").concat(e.getResult()):(typeof e=="number"||typeof e=="string")&&(this.result="".concat(this.result," + ").concat(Qt(e))),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof t?this.result="".concat(this.result," - ").concat(e.getResult()):(typeof e=="number"||typeof e=="string")&&(this.result="".concat(this.result," - ").concat(Qt(e))),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof t?this.result="".concat(this.result," * ").concat(e.getResult(!0)):(typeof e=="number"||typeof e=="string")&&(this.result="".concat(this.result," * ").concat(e)),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof t?this.result="".concat(this.result," / ").concat(e.getResult(!0)):(typeof e=="number"||typeof e=="string")&&(this.result="".concat(this.result," / ").concat(e)),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(e){var a=this,c=e||{},f=c.unit,l=!0;return typeof f=="boolean"?l=f:Array.from(this.unitlessCssVar).some(function(p){return a.result.includes(p)})&&(l=!1),this.result=this.result.replace(Ee,l?"px":""),typeof this.lowPriority!="undefined"?"calc(".concat(this.result,")"):this.result}}]),t}(ue),Pe=function(i){(0,Yt.Z)(t,i);var n=(0,Te.Z)(t);function t(r){var e;return(0,R.Z)(this,t),e=n.call(this),(0,b.Z)((0,Ot.Z)(e),"result",0),r instanceof t?e.result=r.result:typeof r=="number"&&(e.result=r),e}return(0,$.Z)(t,[{key:"add",value:function(e){return e instanceof t?this.result+=e.result:typeof e=="number"&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof t?this.result-=e.result:typeof e=="number"&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof t?this.result*=e.result:typeof e=="number"&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof t?this.result/=e.result:typeof e=="number"&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}]),t}(ue),Ie=function(n,t){var r=n==="css"?Re:Pe;return function(e){return new r(e,t)}},Ae=Ie;function fe(i,n){if(i.length!==n.length)return!1;for(var t=0;t<i.length;t++)if(i[t]!==n[t])return!1;return!0}var re=function(){function i(){(0,R.Z)(this,i),(0,b.Z)(this,"cache",void 0),(0,b.Z)(this,"keys",void 0),(0,b.Z)(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return(0,$.Z)(i,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(t){var r,e,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c={map:this.cache};return t.forEach(function(f){if(!c)c=void 0;else{var l;c=(l=c)===null||l===void 0||(l=l.map)===null||l===void 0?void 0:l.get(f)}}),(r=c)!==null&&r!==void 0&&r.value&&a&&(c.value[1]=this.cacheCallTimes++),(e=c)===null||e===void 0?void 0:e.value}},{key:"get",value:function(t){var r;return(r=this.internalGet(t,!0))===null||r===void 0?void 0:r[0]}},{key:"has",value:function(t){return!!this.internalGet(t)}},{key:"set",value:function(t,r){var e=this;if(!this.has(t)){if(this.size()+1>i.MAX_CACHE_SIZE+i.MAX_CACHE_OFFSET){var a=this.keys.reduce(function(p,m){var C=(0,S.Z)(p,2),g=C[1];return e.internalGet(m)[1]<g?[m,e.internalGet(m)[1]]:p},[this.keys[0],this.cacheCallTimes]),c=(0,S.Z)(a,1),f=c[0];this.delete(f)}this.keys.push(t)}var l=this.cache;t.forEach(function(p,m){if(m===t.length-1)l.set(p,{value:[r,e.cacheCallTimes++]});else{var C=l.get(p);C?C.map||(C.map=new Map):l.set(p,{map:new Map}),l=l.get(p).map}})}},{key:"deleteByPath",value:function(t,r){var e=t.get(r[0]);if(r.length===1){var a;return e.map?t.set(r[0],{map:e.map}):t.delete(r[0]),(a=e.value)===null||a===void 0?void 0:a[0]}var c=this.deleteByPath(e.map,r.slice(1));return(!e.map||e.map.size===0)&&!e.value&&t.delete(r[0]),c}},{key:"delete",value:function(t){if(this.has(t))return this.keys=this.keys.filter(function(r){return!fe(r,t)}),this.deleteByPath(this.cache,t)}}]),i}();(0,b.Z)(re,"MAX_CACHE_SIZE",20),(0,b.Z)(re,"MAX_CACHE_OFFSET",5);var he=Z(56049),de=0,ne=function(){function i(n){(0,R.Z)(this,i),(0,b.Z)(this,"derivatives",void 0),(0,b.Z)(this,"id",void 0),this.derivatives=Array.isArray(n)?n:[n],this.id=de,n.length===0&&(0,he.Kp)(n.length>0,"[Ant Design CSS-in-JS] Theme should have at least one derivative function."),de+=1}return(0,$.Z)(i,[{key:"getDerivativeToken",value:function(t){return this.derivatives.reduce(function(r,e){return e(t,r)},void 0)}}]),i}(),ie=new re;function Le(i){var n=Array.isArray(i)?i:[i];return ie.has(n)||ie.set(n,new ne(n)),ie.get(n)}var k=new WeakMap,E={};function h(i,n){for(var t=k,r=0;r<n.length;r+=1){var e=n[r];t.has(e)||t.set(e,new WeakMap),t=t.get(e)}return t.has(E)||t.set(E,i()),t.get(E)}var v=new WeakMap;function s(i){var n=v.get(i)||"";return n||(Object.keys(i).forEach(function(t){var r=i[t];n+=t,r instanceof ne?n+=r.id:r&&(0,Nt.Z)(r)==="object"?n+=s(r):n+=r}),n=(0,q.Z)(n),v.set(i,n)),n}function P(i,n){return(0,q.Z)("".concat(n,"_").concat(s(i)))}var B="random-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,""),z="_bAmBoO_";function _(i,n,t){if((0,$t.Z)()){var r,e;(0,W.hq)(i,B);var a=document.createElement("div");a.style.position="fixed",a.style.left="0",a.style.top="0",n==null||n(a),document.body.appendChild(a);var c=t?t(a):(r=getComputedStyle(a).content)===null||r===void 0?void 0:r.includes(z);return(e=a.parentNode)===null||e===void 0||e.removeChild(a),(0,W.jL)(B),c}return!1}var Rt=null;function Ut(){return Rt===void 0&&(Rt=_("@layer ".concat(B," { .").concat(B,' { content: "').concat(z,'"!important; } }'),function(i){i.className=B})),Rt}var It=void 0;function Y(){return It===void 0&&(It=_(":where(.".concat(B,') { content: "').concat(z,'"!important; }'),function(i){i.className=B})),It}var Zt=void 0;function tt(){return Zt===void 0&&(Zt=_(".".concat(B," { inset-block: 93px !important; }"),function(i){i.className=B},function(i){return getComputedStyle(i).bottom==="93px"})),Zt}var G=(0,$t.Z)();function pt(i){return typeof i=="number"?"".concat(i,"px"):i}function it(i,n,t){var r,e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(a)return i;var c=(0,M.Z)((0,M.Z)({},e),{},(r={},(0,b.Z)(r,gt,n),(0,b.Z)(r,ht,t),r)),f=Object.keys(c).map(function(l){var p=c[l];return p?"".concat(l,'="').concat(p,'"'):null}).filter(function(l){return l}).join(" ");return"<style ".concat(f,">").concat(i,"</style>")}var at=function(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return"--".concat(t?"".concat(t,"-"):"").concat(n).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},Pt=function(n,t,r){return Object.keys(n).length?".".concat(t).concat(r!=null&&r.scope?".".concat(r.scope):"","{").concat(Object.entries(n).map(function(e){var a=(0,S.Z)(e,2),c=a[0],f=a[1];return"".concat(c,":").concat(f,";")}).join(""),"}"):""},mt=function(n,t,r){var e={},a={};return Object.entries(n).forEach(function(c){var f,l,p=(0,S.Z)(c,2),m=p[0],C=p[1];if(r!=null&&(f=r.preserve)!==null&&f!==void 0&&f[m])a[m]=C;else if((typeof C=="string"||typeof C=="number")&&!(r!=null&&(l=r.ignore)!==null&&l!==void 0&&l[m])){var g,x=at(m,r==null?void 0:r.prefix);e[x]=typeof C=="number"&&!(r!=null&&(g=r.unitless)!==null&&g!==void 0&&g[m])?"".concat(C,"px"):String(C),a[m]="var(".concat(x,")")}}),[a,Pt(e,t,{scope:r==null?void 0:r.scope})]},et=Z(99047),At=(0,M.Z)({},o),st=At.useInsertionEffect,rt=function(n,t,r){w.useMemo(n,r),(0,et.Z)(function(){return t(!0)},r)},nt=st?function(i,n,t){return st(function(){return i(),n()},t)}:rt,dt=nt,Lt=(0,M.Z)({},o),X=Lt.useInsertionEffect,Mt=function(n){var t=[],r=!1;function e(a){r||t.push(a)}return w.useEffect(function(){return r=!1,function(){r=!0,t.length&&t.forEach(function(a){return a()})}},n),e},ct=function(){return function(n){n()}},yt=typeof X!="undefined"?Mt:ct,Xt=yt;function ae(){return!1}var Me=!1;function je(){return Me}var Ve=ae;if(0)var Ue,Be;function se(i,n,t,r,e){var a=w.useContext(wt),c=a.cache,f=[i].concat((0,D.Z)(n)),l=F(f),p=Xt([l]),m=Ve(),C=function(T){c.opUpdate(l,function(I){var H=I||[void 0,void 0],A=(0,S.Z)(H,2),j=A[0],L=j===void 0?0:j,V=A[1],U=V,Q=U||t(),N=[L,Q];return T?T(N):N})};w.useMemo(function(){C()},[l]);var g=c.opGet(l),x=g[1];return dt(function(){e==null||e(x)},function(O){return C(function(T){var I=(0,S.Z)(T,2),H=I[0],A=I[1];return O&&H===0&&(e==null||e(x)),[H+1,A]}),function(){c.opUpdate(l,function(T){var I=T||[],H=(0,S.Z)(I,2),A=H[0],j=A===void 0?0:A,L=H[1],V=j-1;return V===0?(p(function(){(O||!c.opGet(l))&&(r==null||r(L,!1))}),null):[j-1,L]})}},[l]),x}var jt={},Jt="css",Vt=new Map;function Wt(i){Vt.set(i,(Vt.get(i)||0)+1)}function He(i,n){if(typeof document!="undefined"){var t=document.querySelectorAll("style[".concat(gt,'="').concat(i,'"]'));t.forEach(function(r){if(r[Et]===n){var e;(e=r.parentNode)===null||e===void 0||e.removeChild(r)}})}}var ve=0;function ge(i,n){Vt.set(i,(Vt.get(i)||0)-1);var t=Array.from(Vt.keys()),r=t.filter(function(e){var a=Vt.get(e)||0;return a<=0});t.length-r.length>ve&&r.forEach(function(e){He(e,n),Vt.delete(e)})}var pe=function(n,t,r,e){var a=r.getDerivativeToken(n),c=(0,M.Z)((0,M.Z)({},a),t);return e&&(c=e(c)),c},me="token";function ye(i,n){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=(0,w.useContext)(wt),e=r.cache.instanceId,a=r.container,c=t.salt,f=c===void 0?"":c,l=t.override,p=l===void 0?jt:l,m=t.formatToken,C=t.getComputedToken,g=t.cssVar,x=h(function(){return Object.assign.apply(Object,[{}].concat((0,D.Z)(n)))},n),O=s(x),T=s(p),I=g?s(g):"",H=se(me,[f,i.id,O,T,I],function(){var A,j=C?C(x,p,i):pe(x,p,i,m),L=(0,M.Z)({},j),V="";if(g){var U=mt(j,g.key,{prefix:g.prefix,ignore:g.ignore,unitless:g.unitless,preserve:g.preserve}),Q=(0,S.Z)(U,2);j=Q[0],V=Q[1]}var N=P(j,f);j._tokenKey=N,L._tokenKey=P(L,f);var vt=(A=g==null?void 0:g.key)!==null&&A!==void 0?A:N;j._themeKey=vt,Wt(vt);var lt="".concat(Jt,"-").concat((0,q.Z)(N));return j._hashId=lt,[j,lt,L,V,(g==null?void 0:g.key)||""]},function(A){ge(A[0]._themeKey,e)},function(A){var j=(0,S.Z)(A,4),L=j[0],V=j[3];if(g&&V){var U=(0,W.hq)(V,(0,q.Z)("css-variables-".concat(L._themeKey)),{mark:ht,prepend:"queue",attachTo:a,priority:-999});U[Et]=e,U.setAttribute(gt,L._themeKey)}});return H}var Qe=function(n,t,r){var e=(0,S.Z)(n,5),a=e[2],c=e[3],f=e[4],l=r||{},p=l.plain;if(!c)return null;var m=a._tokenKey,C=-999,g={"data-rc-order":"prependQueue","data-rc-priority":"".concat(C)},x=it(c,f,m,g,p);return[C,m,x]},Xe=Z(95687),We=Z(82785),Fe=Z(84055),Je=Z(28067);function Kt(i,n){var t=n.path,r=n.parentSelectors;(0,he.ZP)(!1,"[Ant Design CSS-in-JS] ".concat(t?"Error in ".concat(t,": "):"").concat(i).concat(r.length?" Selector: ".concat(r.join(" | ")):""))}var Br=function(n,t,r){if(n==="content"){var e=/(attr|counters?|url|(((repeating-)?(linear|radial))|conic)-gradient)\(|(no-)?(open|close)-quote/,a=["normal","none","initial","inherit","unset"];(typeof t!="string"||a.indexOf(t)===-1&&!e.test(t)&&(t.charAt(0)!==t.charAt(t.length-1)||t.charAt(0)!=='"'&&t.charAt(0)!=="'"))&&lintWarning("You seem to be using a value for 'content' without quotes, try replacing it with `content: '\"".concat(t,"\"'`."),r)}},Hr=null,wr=function(n,t,r){n==="animation"&&r.hashId&&t!=="none"&&lintWarning("You seem to be using hashed animation '".concat(t,"', in which case 'animationName' with Keyframe as value is recommended."),r)},Nr=null;function qe(i){var n,t=((n=i.match(/:not\(([^)]*)\)/))===null||n===void 0?void 0:n[1])||"",r=t.split(/(\[[^[]*])|(?=[.#])/).filter(function(e){return e});return r.length>1}function tr(i){return i.parentSelectors.reduce(function(n,t){return n?t.includes("&")?t.replace(/&/g,n):"".concat(n," ").concat(t):t},"")}var er=function(n,t,r){var e=tr(r),a=e.match(/:not\([^)]*\)/g)||[];a.length>0&&a.some(qe)&&Kt("Concat ':not' selector not support in legacy browsers.",r)},rr=er,nr=function(n,t,r){switch(n){case"marginLeft":case"marginRight":case"paddingLeft":case"paddingRight":case"left":case"right":case"borderLeft":case"borderLeftWidth":case"borderLeftStyle":case"borderLeftColor":case"borderRight":case"borderRightWidth":case"borderRightStyle":case"borderRightColor":case"borderTopLeftRadius":case"borderTopRightRadius":case"borderBottomLeftRadius":case"borderBottomRightRadius":Kt("You seem to be using non-logical property '".concat(n,"' which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),r);return;case"margin":case"padding":case"borderWidth":case"borderStyle":if(typeof t=="string"){var e=t.split(" ").map(function(f){return f.trim()});e.length===4&&e[1]!==e[3]&&Kt("You seem to be using '".concat(n,"' property with different left ").concat(n," and right ").concat(n,", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),r)}return;case"clear":case"textAlign":(t==="left"||t==="right")&&Kt("You seem to be using non-logical value '".concat(t,"' of ").concat(n,", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),r);return;case"borderRadius":if(typeof t=="string"){var a=t.split("/").map(function(f){return f.trim()}),c=a.reduce(function(f,l){if(f)return f;var p=l.split(" ").map(function(m){return m.trim()});return p.length>=2&&p[0]!==p[1]||p.length===3&&p[1]!==p[2]||p.length===4&&p[2]!==p[3]?!0:f},!1);c&&Kt("You seem to be using non-logical value '".concat(t,"' of ").concat(n,", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),r)}return;default:}},ir=nr,ar=function(n,t,r){(typeof t=="string"&&/NaN/g.test(t)||Number.isNaN(t))&&Kt("Unexpected 'NaN' in property '".concat(n,": ").concat(t,"'."),r)},sr=ar,or=function(n,t,r){r.parentSelectors.some(function(e){var a=e.split(",");return a.some(function(c){return c.split("&").length>2})})&&Kt("Should not use more than one `&` in a selector.",r)},cr=or,oe="data-ant-cssinjs-cache-path",De="_FILE_STYLE__";function lr(i){return Object.keys(i).map(function(n){var t=i[n];return"".concat(n,":").concat(t)}).join(";")}var Ft,we=!0;function $r(i){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;Ft=i,we=n}function ur(){if(!Ft&&(Ft={},(0,$t.Z)())){var i=document.createElement("div");i.className=oe,i.style.position="fixed",i.style.visibility="hidden",i.style.top="-9999px",document.body.appendChild(i);var n=getComputedStyle(i).content||"";n=n.replace(/^"/,"").replace(/"$/,""),n.split(";").forEach(function(e){var a=e.split(":"),c=(0,S.Z)(a,2),f=c[0],l=c[1];Ft[f]=l});var t=document.querySelector("style[".concat(oe,"]"));if(t){var r;we=!1,(r=t.parentNode)===null||r===void 0||r.removeChild(t)}document.body.removeChild(i)}}function fr(i){return ur(),!!Ft[i]}function hr(i){var n=Ft[i],t=null;if(n&&(0,$t.Z)())if(we)t=De;else{var r=document.querySelector("style[".concat(ht,'="').concat(Ft[i],'"]'));r?t=r.innerHTML:delete Ft[i]}return[t,n]}var dr="_skip_check_",Ke="_multi_value_";function Se(i){var n=(0,Fe.q)((0,Je.MY)(i),Fe.P);return n.replace(/\{%%%\:[^;];}/g,";")}function vr(i){return(0,Nt.Z)(i)==="object"&&i&&(dr in i||Ke in i)}function ze(i,n,t){if(!n)return i;var r=".".concat(n),e=t==="low"?":where(".concat(r,")"):r,a=i.split(",").map(function(c){var f,l=c.trim().split(/\s+/),p=l[0]||"",m=((f=p.match(/^\w+/))===null||f===void 0?void 0:f[0])||"";return p="".concat(m).concat(e).concat(p.slice(m.length)),[p].concat((0,D.Z)(l.slice(1))).join(" ")});return a.join(",")}var gr=function i(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{root:!0,parentSelectors:[]},e=r.root,a=r.injectHash,c=r.parentSelectors,f=t.hashId,l=t.layer,p=t.path,m=t.hashPriority,C=t.transformers,g=C===void 0?[]:C,x=t.linters,O=x===void 0?[]:x,T="",I={};function H(L){var V=L.getName(f);if(!I[V]){var U=i(L.style,t,{root:!1,parentSelectors:c}),Q=(0,S.Z)(U,1),N=Q[0];I[V]="@keyframes ".concat(L.getName(f)).concat(N)}}function A(L){var V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return L.forEach(function(U){Array.isArray(U)?A(U,V):U&&V.push(U)}),V}var j=A(Array.isArray(n)?n:[n]);return j.forEach(function(L){var V=typeof L=="string"&&!e?{}:L;if(typeof V=="string")T+="".concat(V,`
`);else if(V._keyframe)H(V);else{var U=g.reduce(function(Q,N){var vt;return(N==null||(vt=N.visit)===null||vt===void 0?void 0:vt.call(N,Q))||Q},V);Object.keys(U).forEach(function(Q){var N=U[Q];if((0,Nt.Z)(N)==="object"&&N&&(Q!=="animationName"||!N._keyframe)&&!vr(N)){var vt=!1,lt=Q.trim(),Ce=!1;(e||a)&&f?lt.startsWith("@")?vt=!0:lt==="&"?lt=ze("",f,m):lt=ze(Q,f,m):e&&!f&&(lt==="&"||lt==="")&&(lt="",Ce=!0);var $e=i(N,t,{root:Ce,injectHash:vt,parentSelectors:[].concat((0,D.Z)(c),[lt])}),xt=(0,S.Z)($e,2),ut=xt[0],ft=xt[1];I=(0,M.Z)((0,M.Z)({},I),ft),T+="".concat(lt).concat(ut)}else{let Dt=function(St,bt){var te=St.replace(/[A-Z]/g,function(_t){return"-".concat(_t.toLowerCase())}),Tt=bt;!We.Z[St]&&typeof Tt=="number"&&Tt!==0&&(Tt="".concat(Tt,"px")),St==="animationName"&&bt!==null&&bt!==void 0&&bt._keyframe&&(H(bt),Tt=bt.getName(f)),T+="".concat(te,":").concat(Tt,";")};var Bt,zt=(Bt=N==null?void 0:N.value)!==null&&Bt!==void 0?Bt:N;(0,Nt.Z)(N)==="object"&&N!==null&&N!==void 0&&N[Ke]&&Array.isArray(zt)?zt.forEach(function(St){Dt(Q,St)}):Dt(Q,zt)}})}}),e?l&&(T&&(T="@layer ".concat(l.name," {").concat(T,"}")),l.dependencies&&(I["@layer ".concat(l.name)]=l.dependencies.map(function(L){return"@layer ".concat(L,", ").concat(l.name,";")}).join(`
`))):T="{".concat(T,"}"),[T,I]};function _e(i,n){return(0,q.Z)("".concat(i.join("%")).concat(n))}function pr(){return null}var Ge="style";function mr(i,n){var t=i.token,r=i.path,e=i.hashId,a=i.layer,c=i.nonce,f=i.clientOnly,l=i.order,p=l===void 0?0:l,m=w.useContext(wt),C=m.autoClear,g=m.mock,x=m.defaultCache,O=m.hashPriority,T=m.container,I=m.ssrInline,H=m.transformers,A=m.linters,j=m.cache,L=m.layer,V=t._tokenKey,U=[V];L&&U.push("layer"),U.push.apply(U,(0,D.Z)(r));var Q=G,N=se(Ge,U,function(){var xt=U.join("|");if(fr(xt)){var ut=hr(xt),ft=(0,S.Z)(ut,2),Bt=ft[0],zt=ft[1];if(Bt)return[Bt,V,zt,{},f,p]}var Dt=n(),St=gr(Dt,{hashId:e,hashPriority:O,layer:L?a:void 0,path:r.join("-"),transformers:H,linters:A}),bt=(0,S.Z)(St,2),te=bt[0],Tt=bt[1],_t=Se(te),kt=_e(U,_t);return[_t,V,kt,Tt,f,p]},function(xt,ut){var ft=(0,S.Z)(xt,3),Bt=ft[2];(ut||C)&&G&&(0,W.jL)(Bt,{mark:ht})},function(xt){var ut=(0,S.Z)(xt,4),ft=ut[0],Bt=ut[1],zt=ut[2],Dt=ut[3];if(Q&&ft!==De){var St={mark:ht,prepend:L?!1:"queue",attachTo:T,priority:p},bt=typeof c=="function"?c():c;bt&&(St.csp={nonce:bt});var te=[],Tt=[];Object.keys(Dt).forEach(function(kt){kt.startsWith("@layer")?te.push(kt):Tt.push(kt)}),te.forEach(function(kt){(0,W.hq)(Se(Dt[kt]),"_layer-".concat(kt),(0,M.Z)((0,M.Z)({},St),{},{prepend:!0}))});var _t=(0,W.hq)(ft,zt,St);_t[Et]=j.instanceId,_t.setAttribute(gt,V),Tt.forEach(function(kt){(0,W.hq)(Se(Dt[kt]),"_effect-".concat(kt),St)})}}),vt=(0,S.Z)(N,3),lt=vt[0],Ce=vt[1],$e=vt[2];return function(xt){var ut;if(!I||Q||!x)ut=w.createElement(pr,null);else{var ft;ut=w.createElement("style",(0,Xe.Z)({},(ft={},(0,b.Z)(ft,gt,Ce),(0,b.Z)(ft,ht,$e),ft),{dangerouslySetInnerHTML:{__html:lt}}))}return w.createElement(w.Fragment,null,ut,xt)}}var yr=function(n,t,r){var e=(0,S.Z)(n,6),a=e[0],c=e[1],f=e[2],l=e[3],p=e[4],m=e[5],C=r||{},g=C.plain;if(p)return null;var x=a,O={"data-rc-order":"prependQueue","data-rc-priority":"".concat(m)};return x=it(a,c,f,O,g),l&&Object.keys(l).forEach(function(T){if(!t[T]){t[T]=!0;var I=Se(l[T]),H=it(I,c,"_effect-".concat(T),O,g);T.startsWith("@layer")?x=H+x:x+=H}}),[m,f,x]},Ye="cssVar",Sr=function(n,t){var r=n.key,e=n.prefix,a=n.unitless,c=n.ignore,f=n.token,l=n.scope,p=l===void 0?"":l,m=(0,w.useContext)(wt),C=m.cache.instanceId,g=m.container,x=f._tokenKey,O=[].concat((0,D.Z)(n.path),[r,p,x]),T=se(Ye,O,function(){var I=t(),H=mt(I,r,{prefix:e,unitless:a,ignore:c,scope:p}),A=(0,S.Z)(H,2),j=A[0],L=A[1],V=_e(O,L);return[j,L,V,r]},function(I){var H=(0,S.Z)(I,3),A=H[2];G&&(0,W.jL)(A,{mark:ht})},function(I){var H=(0,S.Z)(I,3),A=H[1],j=H[2];if(A){var L=(0,W.hq)(A,j,{mark:ht,prepend:"queue",attachTo:g,priority:-999});L[Et]=C,L.setAttribute(gt,r)}});return T},br=function(n,t,r){var e=(0,S.Z)(n,4),a=e[1],c=e[2],f=e[3],l=r||{},p=l.plain;if(!a)return null;var m=-999,C={"data-rc-order":"prependQueue","data-rc-priority":"".concat(m)},g=it(a,f,c,C,p);return[m,c,g]},Cr=Sr,ce,Zr=(ce={},(0,b.Z)(ce,Ge,yr),(0,b.Z)(ce,me,Qe),(0,b.Z)(ce,Ye,br),ce);function xr(i){return i!==null}function Tr(i,n){var t=typeof n=="boolean"?{plain:n}:n||{},r=t.plain,e=r===void 0?!1:r,a=t.types,c=a===void 0?["style","token","cssVar"]:a,f=new RegExp("^(".concat((typeof c=="string"?[c]:c).join("|"),")%")),l=Array.from(i.cache.keys()).filter(function(g){return f.test(g)}),p={},m={},C="";return l.map(function(g){var x=g.replace(f,"").replace(/%/g,"|"),O=g.split("%"),T=(0,S.Z)(O,1),I=T[0],H=Zr[I],A=H(i.cache.get(g)[1],p,{plain:e});if(!A)return null;var j=(0,S.Z)(A,3),L=j[0],V=j[1],U=j[2];return g.startsWith("style")&&(m[x]=V),[L,U]}).filter(xr).sort(function(g,x){var O=(0,S.Z)(g,1),T=O[0],I=(0,S.Z)(x,1),H=I[0];return T-H}).forEach(function(g){var x=(0,S.Z)(g,2),O=x[1];C+=O}),C+=it(".".concat(oe,'{content:"').concat(lr(m),'";}'),void 0,void 0,(0,b.Z)({},oe,oe),e),C}var kr=function(){function i(n,t){(0,R.Z)(this,i),(0,b.Z)(this,"name",void 0),(0,b.Z)(this,"style",void 0),(0,b.Z)(this,"_keyframe",!0),this.name=n,this.style=t}return(0,$.Z)(i,[{key:"getName",value:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return t?"".concat(t,"-").concat(this.name):this.name}}]),i}(),Er=kr;function Rr(i){if(typeof i=="number")return[[i],!1];var n=String(i).trim(),t=n.match(/(.*)(!important)/),r=(t?t[1]:n).trim().split(/\s+/),e=[],a=0;return[r.reduce(function(c,f){if(f.includes("(")||f.includes(")")){var l=f.split("(").length-1,p=f.split(")").length-1;a+=l-p}return a>=0&&e.push(f),a===0&&(c.push(e.join(" ")),e=[]),c},[]),!!t]}function qt(i){return i.notSplit=!0,i}var Pr={inset:["top","right","bottom","left"],insetBlock:["top","bottom"],insetBlockStart:["top"],insetBlockEnd:["bottom"],insetInline:["left","right"],insetInlineStart:["left"],insetInlineEnd:["right"],marginBlock:["marginTop","marginBottom"],marginBlockStart:["marginTop"],marginBlockEnd:["marginBottom"],marginInline:["marginLeft","marginRight"],marginInlineStart:["marginLeft"],marginInlineEnd:["marginRight"],paddingBlock:["paddingTop","paddingBottom"],paddingBlockStart:["paddingTop"],paddingBlockEnd:["paddingBottom"],paddingInline:["paddingLeft","paddingRight"],paddingInlineStart:["paddingLeft"],paddingInlineEnd:["paddingRight"],borderBlock:qt(["borderTop","borderBottom"]),borderBlockStart:qt(["borderTop"]),borderBlockEnd:qt(["borderBottom"]),borderInline:qt(["borderLeft","borderRight"]),borderInlineStart:qt(["borderLeft"]),borderInlineEnd:qt(["borderRight"]),borderBlockWidth:["borderTopWidth","borderBottomWidth"],borderBlockStartWidth:["borderTopWidth"],borderBlockEndWidth:["borderBottomWidth"],borderInlineWidth:["borderLeftWidth","borderRightWidth"],borderInlineStartWidth:["borderLeftWidth"],borderInlineEndWidth:["borderRightWidth"],borderBlockStyle:["borderTopStyle","borderBottomStyle"],borderBlockStartStyle:["borderTopStyle"],borderBlockEndStyle:["borderBottomStyle"],borderInlineStyle:["borderLeftStyle","borderRightStyle"],borderInlineStartStyle:["borderLeftStyle"],borderInlineEndStyle:["borderRightStyle"],borderBlockColor:["borderTopColor","borderBottomColor"],borderBlockStartColor:["borderTopColor"],borderBlockEndColor:["borderBottomColor"],borderInlineColor:["borderLeftColor","borderRightColor"],borderInlineStartColor:["borderLeftColor"],borderInlineEndColor:["borderRightColor"],borderStartStartRadius:["borderTopLeftRadius"],borderStartEndRadius:["borderTopRightRadius"],borderEndStartRadius:["borderBottomLeftRadius"],borderEndEndRadius:["borderBottomRightRadius"]};function be(i,n){var t=i;return n&&(t="".concat(t," !important")),{_skip_check_:!0,value:t}}var Ir={visit:function(n){var t={};return Object.keys(n).forEach(function(r){var e=n[r],a=Pr[r];if(a&&(typeof e=="number"||typeof e=="string")){var c=Rr(e),f=(0,S.Z)(c,2),l=f[0],p=f[1];a.length&&a.notSplit?a.forEach(function(m){t[m]=be(e,p)}):a.length===1?t[a[0]]=be(l[0],p):a.length===2?a.forEach(function(m,C){var g;t[m]=be((g=l[C])!==null&&g!==void 0?g:l[0],p)}):a.length===4?a.forEach(function(m,C){var g,x;t[m]=be((g=(x=l[C])!==null&&x!==void 0?x:l[C-2])!==null&&g!==void 0?g:l[0],p)}):t[r]=e}else t[r]=e}),t}},Ar=Ir,Ne=/url\([^)]+\)|var\([^)]+\)|(\d*\.?\d+)px/g;function Lr(i,n){var t=Math.pow(10,n+1),r=Math.floor(i*t);return Math.round(r/10)*10/t}var Mr=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=n.rootValue,r=t===void 0?16:t,e=n.precision,a=e===void 0?5:e,c=n.mediaQuery,f=c===void 0?!1:c,l=function(C,g){if(!g)return C;var x=parseFloat(g);if(x<=1)return C;var O=Lr(x/r,a);return"".concat(O,"rem")},p=function(C){var g=(0,M.Z)({},C);return Object.entries(C).forEach(function(x){var O=(0,S.Z)(x,2),T=O[0],I=O[1];if(typeof I=="string"&&I.includes("px")){var H=I.replace(Ne,l);g[T]=H}!We.Z[T]&&typeof I=="number"&&I!==0&&(g[T]="".concat(I,"px").replace(Ne,l));var A=T.trim();if(A.startsWith("@")&&A.includes("px")&&f){var j=T.replace(Ne,l);g[j]=g[T],delete g[T]}}),g};return{visit:p}},jr=Mr,Vr={supportModernCSS:function(){return Y()&&tt()}}},87471:function(Oe,Ht,Z){Z.r(Ht),Z.d(Ht,{FastColor:function(){return W}});var S=Z(94450);const b=Math.round;function D(w,o){const u=w.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],d=u.map(y=>parseFloat(y));for(let y=0;y<3;y+=1)d[y]=o(d[y]||0,u[y]||"",y);return u[3]?d[3]=u[3].includes("%")?d[3]/100:d[3]:d[3]=1,d}const M=(w,o,u)=>u===0?w:w/100;function q(w,o){const u=o||255;return w>u?u:w<0?0:w}class W{constructor(o){(0,S.Z)(this,"isValid",!0),(0,S.Z)(this,"r",0),(0,S.Z)(this,"g",0),(0,S.Z)(this,"b",0),(0,S.Z)(this,"a",1),(0,S.Z)(this,"_h",void 0),(0,S.Z)(this,"_s",void 0),(0,S.Z)(this,"_l",void 0),(0,S.Z)(this,"_v",void 0),(0,S.Z)(this,"_max",void 0),(0,S.Z)(this,"_min",void 0),(0,S.Z)(this,"_brightness",void 0);function u(d){return d[0]in o&&d[1]in o&&d[2]in o}if(o)if(typeof o=="string"){let y=function(R){return d.startsWith(R)};const d=o.trim();/^#?[A-F\d]{3,8}$/i.test(d)?this.fromHexString(d):y("rgb")?this.fromRgbString(d):y("hsl")?this.fromHslString(d):(y("hsv")||y("hsb"))&&this.fromHsvString(d)}else if(o instanceof W)this.r=o.r,this.g=o.g,this.b=o.b,this.a=o.a,this._h=o._h,this._s=o._s,this._l=o._l,this._v=o._v;else if(u("rgb"))this.r=q(o.r),this.g=q(o.g),this.b=q(o.b),this.a=typeof o.a=="number"?q(o.a,1):1;else if(u("hsl"))this.fromHsl(o);else if(u("hsv"))this.fromHsv(o);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(o))}setR(o){return this._sc("r",o)}setG(o){return this._sc("g",o)}setB(o){return this._sc("b",o)}setA(o){return this._sc("a",o,1)}setHue(o){const u=this.toHsv();return u.h=o,this._c(u)}getLuminance(){function o(R){const $=R/255;return $<=.03928?$/12.92:Math.pow(($+.055)/1.055,2.4)}const u=o(this.r),d=o(this.g),y=o(this.b);return .2126*u+.7152*d+.0722*y}getHue(){if(typeof this._h=="undefined"){const o=this.getMax()-this.getMin();o===0?this._h=0:this._h=b(60*(this.r===this.getMax()?(this.g-this.b)/o+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/o+2:(this.r-this.g)/o+4))}return this._h}getSaturation(){if(typeof this._s=="undefined"){const o=this.getMax()-this.getMin();o===0?this._s=0:this._s=o/this.getMax()}return this._s}getLightness(){return typeof this._l=="undefined"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v=="undefined"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness=="undefined"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(o=10){const u=this.getHue(),d=this.getSaturation();let y=this.getLightness()-o/100;return y<0&&(y=0),this._c({h:u,s:d,l:y,a:this.a})}lighten(o=10){const u=this.getHue(),d=this.getSaturation();let y=this.getLightness()+o/100;return y>1&&(y=1),this._c({h:u,s:d,l:y,a:this.a})}mix(o,u=50){const d=this._c(o),y=u/100,R=J=>(d[J]-this[J])*y+this[J],$={r:b(R("r")),g:b(R("g")),b:b(R("b")),a:b(R("a")*100)/100};return this._c($)}tint(o=10){return this.mix({r:255,g:255,b:255,a:1},o)}shade(o=10){return this.mix({r:0,g:0,b:0,a:1},o)}onBackground(o){const u=this._c(o),d=this.a+u.a*(1-this.a),y=R=>b((this[R]*this.a+u[R]*u.a*(1-this.a))/d);return this._c({r:y("r"),g:y("g"),b:y("b"),a:d})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(o){return this.r===o.r&&this.g===o.g&&this.b===o.b&&this.a===o.a}clone(){return this._c(this)}toHexString(){let o="#";const u=(this.r||0).toString(16);o+=u.length===2?u:"0"+u;const d=(this.g||0).toString(16);o+=d.length===2?d:"0"+d;const y=(this.b||0).toString(16);if(o+=y.length===2?y:"0"+y,typeof this.a=="number"&&this.a>=0&&this.a<1){const R=b(this.a*255).toString(16);o+=R.length===2?R:"0"+R}return o}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const o=this.getHue(),u=b(this.getSaturation()*100),d=b(this.getLightness()*100);return this.a!==1?`hsla(${o},${u}%,${d}%,${this.a})`:`hsl(${o},${u}%,${d}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(o,u,d){const y=this.clone();return y[o]=q(u,d),y}_c(o){return new this.constructor(o)}getMax(){return typeof this._max=="undefined"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min=="undefined"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(o){const u=o.replace("#","");function d(y,R){return parseInt(u[y]+u[R||y],16)}u.length<6?(this.r=d(0),this.g=d(1),this.b=d(2),this.a=u[3]?d(3)/255:1):(this.r=d(0,1),this.g=d(2,3),this.b=d(4,5),this.a=u[6]?d(6,7)/255:1)}fromHsl({h:o,s:u,l:d,a:y}){if(this._h=o%360,this._s=u,this._l=d,this.a=typeof y=="number"?y:1,u<=0){const gt=b(d*255);this.r=gt,this.g=gt,this.b=gt}let R=0,$=0,J=0;const F=o/60,K=(1-Math.abs(2*d-1))*u,ot=K*(1-Math.abs(F%2-1));F>=0&&F<1?(R=K,$=ot):F>=1&&F<2?(R=ot,$=K):F>=2&&F<3?($=K,J=ot):F>=3&&F<4?($=ot,J=K):F>=4&&F<5?(R=ot,J=K):F>=5&&F<6&&(R=K,J=ot);const Ct=d-K/2;this.r=b((R+Ct)*255),this.g=b(($+Ct)*255),this.b=b((J+Ct)*255)}fromHsv({h:o,s:u,v:d,a:y}){this._h=o%360,this._s=u,this._v=d,this.a=typeof y=="number"?y:1;const R=b(d*255);if(this.r=R,this.g=R,this.b=R,u<=0)return;const $=o/60,J=Math.floor($),F=$-J,K=b(d*(1-u)*255),ot=b(d*(1-u*F)*255),Ct=b(d*(1-u*(1-F))*255);switch(J){case 0:this.g=Ct,this.b=K;break;case 1:this.r=ot,this.b=K;break;case 2:this.r=K,this.b=Ct;break;case 3:this.r=K,this.g=ot;break;case 4:this.r=Ct,this.g=K;break;case 5:default:this.g=K,this.b=ot;break}}fromHsvString(o){const u=D(o,M);this.fromHsv({h:u[0],s:u[1],v:u[2],a:u[3]})}fromHslString(o){const u=D(o,M);this.fromHsl({h:u[0],s:u[1],l:u[2],a:u[3]})}fromRgbString(o){const u=D(o,(d,y)=>y.includes("%")?b(d/100*255):d);this.r=u[0],this.g=u[1],this.b=u[2],this.a=u[3]}}}}]);
