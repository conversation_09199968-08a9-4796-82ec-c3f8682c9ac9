"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[228],{34249:function($n,mn,K){K.r(mn),K.d(mn,{ARRAY_TYPE:function(){return yn},EPSILON:function(){return C},RANDOM:function(){return xn},equals:function(){return Y},setMatrixArrayType:function(){return dn},toRadian:function(){return An}});var C=1e-6,yn=typeof Float32Array!="undefined"?Float32Array:Array,xn=Math.random;function dn(fn){yn=fn}var un=Math.PI/180;function An(fn){return fn*un}function Y(fn,hn){return Math.abs(fn-hn)<=C*Math.max(1,Math.abs(fn),Math.abs(hn))}Math.hypot||(Math.hypot=function(){for(var fn=0,hn=arguments.length;hn--;)fn+=arguments[hn]*arguments[hn];return Math.sqrt(fn)})},22077:function($n,mn,K){K.r(mn),K.d(mn,{glMatrix:function(){return Y},mat2:function(){return C},mat2d:function(){return yn},mat3:function(){return en},mat4:function(){return xn},quat:function(){return un},quat2:function(){return An},vec2:function(){return ne},vec3:function(){return Rn},vec4:function(){return dn}});var C={};K.r(C),K.d(C,{LDU:function(){return In},add:function(){return Qn},adjoint:function(){return _n},clone:function(){return hn},copy:function(){return Pn},create:function(){return fn},determinant:function(){return Dn},equals:function(){return Xn},exactEquals:function(){return Zn},frob:function(){return jn},fromRotation:function(){return Sn},fromScaling:function(){return Vn},fromValues:function(){return Tn},identity:function(){return Yn},invert:function(){return wn},mul:function(){return Un},multiply:function(){return qn},multiplyScalar:function(){return Bn},multiplyScalarAndAdd:function(){return Cn},rotate:function(){return pn},scale:function(){return On},set:function(){return Ln},str:function(){return Fn},sub:function(){return Wn},subtract:function(){return gn},transpose:function(){return Nn}});var yn={};K.r(yn),K.d(yn,{add:function(){return m},clone:function(){return l},copy:function(){return O},create:function(){return f},determinant:function(){return j},equals:function(){return U},exactEquals:function(){return L},frob:function(){return h},fromRotation:function(){return H},fromScaling:function(){return d},fromTranslation:function(){return z},fromValues:function(){return N},identity:function(){return T},invert:function(){return D},mul:function(){return W},multiply:function(){return Q},multiplyScalar:function(){return p},multiplyScalarAndAdd:function(){return g},rotate:function(){return w},scale:function(){return G},set:function(){return _},str:function(){return P},sub:function(){return b},subtract:function(){return x},translate:function(){return $}});var xn={};K.r(xn),K.d(xn,{add:function(){return q1},adjoint:function(){return Jr},clone:function(){return Mn},copy:function(){return ln},create:function(){return k},determinant:function(){return $r},equals:function(){return g1},exactEquals:function(){return S1},frob:function(){return P1},fromQuat:function(){return l1},fromQuat2:function(){return s1},fromRotation:function(){return i1},fromRotationTranslation:function(){return vr},fromRotationTranslationScale:function(){return h1},fromRotationTranslationScaleOrigin:function(){return M1},fromScaling:function(){return t1},fromTranslation:function(){return a1},fromValues:function(){return En},fromXRotation:function(){return c1},fromYRotation:function(){return f1},fromZRotation:function(){return v1},frustum:function(){return m1},getRotation:function(){return Mr},getScaling:function(){return hr},getTranslation:function(){return sr},identity:function(){return Kn},invert:function(){return Hr},lookAt:function(){return z1},mul:function(){return Y1},multiply:function(){return fr},multiplyScalar:function(){return p1},multiplyScalarAndAdd:function(){return O1},ortho:function(){return u1},orthoNO:function(){return mr},orthoZO:function(){return A1},perspective:function(){return y1},perspectiveFromFieldOfView:function(){return x1},perspectiveNO:function(){return lr},perspectiveZO:function(){return d1},rotate:function(){return or},rotateX:function(){return n1},rotateY:function(){return r1},rotateZ:function(){return e1},scale:function(){return kr},set:function(){return vn},str:function(){return R1},sub:function(){return L1},subtract:function(){return yr},targetTo:function(){return E1},translate:function(){return br},transpose:function(){return Gn}});var dn={};K.r(dn),K.d(dn,{add:function(){return Er},ceil:function(){return I1},clone:function(){return xr},copy:function(){return Ar},create:function(){return dr},cross:function(){return j1},dist:function(){return G1},distance:function(){return Or},div:function(){return K1},divide:function(){return qr},dot:function(){return Yr},equals:function(){return Tr},exactEquals:function(){return Ir},floor:function(){return T1},forEach:function(){return b1},fromValues:function(){return ur},inverse:function(){return F1},len:function(){return J1},length:function(){return nr},lerp:function(){return Lr},max:function(){return w1},min:function(){return N1},mul:function(){return W1},multiply:function(){return Pr},negate:function(){return V1},normalize:function(){return gr},random:function(){return Q1},round:function(){return _1},scale:function(){return pr},scaleAndAdd:function(){return D1},set:function(){return zr},sqrDist:function(){return H1},sqrLen:function(){return $1},squaredDistance:function(){return Sr},squaredLength:function(){return rr},str:function(){return C1},sub:function(){return U1},subtract:function(){return Rr},transformMat4:function(){return Z1},transformQuat:function(){return X1},zero:function(){return B1}});var un={};K.r(un),K.d(un,{add:function(){return M0},calculateW:function(){return r0},clone:function(){return v0},conjugate:function(){return i0},copy:function(){return er},create:function(){return bn},dot:function(){return ar},equals:function(){return u0},exactEquals:function(){return x0},exp:function(){return Fr},fromEuler:function(){return c0},fromMat3:function(){return Qr},fromValues:function(){return s0},getAngle:function(){return n0},getAxisAngle:function(){return o1},identity:function(){return k1},invert:function(){return t0},len:function(){return y0},length:function(){return tr},lerp:function(){return m0},ln:function(){return jr},mul:function(){return l0},multiply:function(){return wr},normalize:function(){return cr},pow:function(){return e0},random:function(){return a0},rotateX:function(){return _r},rotateY:function(){return Dr},rotateZ:function(){return Vr},rotationTo:function(){return A0},scale:function(){return Zr},set:function(){return h0},setAxes:function(){return E0},setAxisAngle:function(){return Nr},slerp:function(){return kn},sqlerp:function(){return z0},sqrLen:function(){return d0},squaredLength:function(){return ir},str:function(){return f0}});var An={};K.r(An),K.d(An,{add:function(){return B0},clone:function(){return P0},conjugate:function(){return G0},copy:function(){return Br},create:function(){return R0},dot:function(){return Ur},equals:function(){return o0},exactEquals:function(){return k0},fromMat4:function(){return g0},fromRotation:function(){return S0},fromRotationTranslation:function(){return Xr},fromRotationTranslationValues:function(){return p0},fromTranslation:function(){return O0},fromValues:function(){return q0},getDual:function(){return T0},getReal:function(){return I0},getTranslation:function(){return _0},identity:function(){return Y0},invert:function(){return K0},len:function(){return H0},length:function(){return Wr},lerp:function(){return W0},mul:function(){return C0},multiply:function(){return Cr},normalize:function(){return $0},rotateAroundAxis:function(){return X0},rotateByQuatAppend:function(){return Q0},rotateByQuatPrepend:function(){return Z0},rotateX:function(){return V0},rotateY:function(){return F0},rotateZ:function(){return j0},scale:function(){return U0},set:function(){return L0},setDual:function(){return w0},setReal:function(){return N0},sqrLen:function(){return J0},squaredLength:function(){return on},str:function(){return b0},translate:function(){return D0}});var Y=K(34249);function fn(){var n=new Y.ARRAY_TYPE(4);return Y.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0),n[0]=1,n[3]=1,n}function hn(n){var r=new Y.ARRAY_TYPE(4);return r[0]=n[0],r[1]=n[1],r[2]=n[2],r[3]=n[3],r}function Pn(n,r){return n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n}function Yn(n){return n[0]=1,n[1]=0,n[2]=0,n[3]=1,n}function Tn(n,r,e,a){var t=new Y.ARRAY_TYPE(4);return t[0]=n,t[1]=r,t[2]=e,t[3]=a,t}function Ln(n,r,e,a,t){return n[0]=r,n[1]=e,n[2]=a,n[3]=t,n}function Nn(n,r){if(n===r){var e=r[1];n[1]=r[2],n[2]=e}else n[0]=r[0],n[1]=r[2],n[2]=r[1],n[3]=r[3];return n}function wn(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=e*i-t*a;return c?(c=1/c,n[0]=i*c,n[1]=-a*c,n[2]=-t*c,n[3]=e*c,n):null}function _n(n,r){var e=r[0];return n[0]=r[3],n[1]=-r[1],n[2]=-r[2],n[3]=e,n}function Dn(n){return n[0]*n[3]-n[2]*n[1]}function qn(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=e[0],s=e[1],M=e[2],y=e[3];return n[0]=a*v+i*s,n[1]=t*v+c*s,n[2]=a*M+i*y,n[3]=t*M+c*y,n}function pn(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=Math.sin(e),s=Math.cos(e);return n[0]=a*s+i*v,n[1]=t*s+c*v,n[2]=a*-v+i*s,n[3]=t*-v+c*s,n}function On(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=e[0],s=e[1];return n[0]=a*v,n[1]=t*v,n[2]=i*s,n[3]=c*s,n}function Sn(n,r){var e=Math.sin(r),a=Math.cos(r);return n[0]=a,n[1]=e,n[2]=-e,n[3]=a,n}function Vn(n,r){return n[0]=r[0],n[1]=0,n[2]=0,n[3]=r[1],n}function Fn(n){return"mat2("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+")"}function jn(n){return Math.hypot(n[0],n[1],n[2],n[3])}function In(n,r,e,a){return n[2]=a[2]/a[0],e[0]=a[0],e[1]=a[1],e[3]=a[3]-n[2]*e[1],[n,r,e]}function Qn(n,r,e){return n[0]=r[0]+e[0],n[1]=r[1]+e[1],n[2]=r[2]+e[2],n[3]=r[3]+e[3],n}function gn(n,r,e){return n[0]=r[0]-e[0],n[1]=r[1]-e[1],n[2]=r[2]-e[2],n[3]=r[3]-e[3],n}function Zn(n,r){return n[0]===r[0]&&n[1]===r[1]&&n[2]===r[2]&&n[3]===r[3]}function Xn(n,r){var e=n[0],a=n[1],t=n[2],i=n[3],c=r[0],v=r[1],s=r[2],M=r[3];return Math.abs(e-c)<=Y.EPSILON*Math.max(1,Math.abs(e),Math.abs(c))&&Math.abs(a-v)<=Y.EPSILON*Math.max(1,Math.abs(a),Math.abs(v))&&Math.abs(t-s)<=Y.EPSILON*Math.max(1,Math.abs(t),Math.abs(s))&&Math.abs(i-M)<=Y.EPSILON*Math.max(1,Math.abs(i),Math.abs(M))}function Bn(n,r,e){return n[0]=r[0]*e,n[1]=r[1]*e,n[2]=r[2]*e,n[3]=r[3]*e,n}function Cn(n,r,e,a){return n[0]=r[0]+e[0]*a,n[1]=r[1]+e[1]*a,n[2]=r[2]+e[2]*a,n[3]=r[3]+e[3]*a,n}var Un=qn,Wn=gn;function f(){var n=new Y.ARRAY_TYPE(6);return Y.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0,n[4]=0,n[5]=0),n[0]=1,n[3]=1,n}function l(n){var r=new Y.ARRAY_TYPE(6);return r[0]=n[0],r[1]=n[1],r[2]=n[2],r[3]=n[3],r[4]=n[4],r[5]=n[5],r}function O(n,r){return n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n[4]=r[4],n[5]=r[5],n}function T(n){return n[0]=1,n[1]=0,n[2]=0,n[3]=1,n[4]=0,n[5]=0,n}function N(n,r,e,a,t,i){var c=new Y.ARRAY_TYPE(6);return c[0]=n,c[1]=r,c[2]=e,c[3]=a,c[4]=t,c[5]=i,c}function _(n,r,e,a,t,i,c){return n[0]=r,n[1]=e,n[2]=a,n[3]=t,n[4]=i,n[5]=c,n}function D(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=r[4],v=r[5],s=e*i-a*t;return s?(s=1/s,n[0]=i*s,n[1]=-a*s,n[2]=-t*s,n[3]=e*s,n[4]=(t*v-i*c)*s,n[5]=(a*c-e*v)*s,n):null}function j(n){return n[0]*n[3]-n[1]*n[2]}function Q(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=r[4],s=r[5],M=e[0],y=e[1],u=e[2],E=e[3],A=e[4],q=e[5];return n[0]=a*M+i*y,n[1]=t*M+c*y,n[2]=a*u+i*E,n[3]=t*u+c*E,n[4]=a*A+i*q+v,n[5]=t*A+c*q+s,n}function w(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=r[4],s=r[5],M=Math.sin(e),y=Math.cos(e);return n[0]=a*y+i*M,n[1]=t*y+c*M,n[2]=a*-M+i*y,n[3]=t*-M+c*y,n[4]=v,n[5]=s,n}function G(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=r[4],s=r[5],M=e[0],y=e[1];return n[0]=a*M,n[1]=t*M,n[2]=i*y,n[3]=c*y,n[4]=v,n[5]=s,n}function $(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=r[4],s=r[5],M=e[0],y=e[1];return n[0]=a,n[1]=t,n[2]=i,n[3]=c,n[4]=a*M+i*y+v,n[5]=t*M+c*y+s,n}function H(n,r){var e=Math.sin(r),a=Math.cos(r);return n[0]=a,n[1]=e,n[2]=-e,n[3]=a,n[4]=0,n[5]=0,n}function d(n,r){return n[0]=r[0],n[1]=0,n[2]=0,n[3]=r[1],n[4]=0,n[5]=0,n}function z(n,r){return n[0]=1,n[1]=0,n[2]=0,n[3]=1,n[4]=r[0],n[5]=r[1],n}function P(n){return"mat2d("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+")"}function h(n){return Math.hypot(n[0],n[1],n[2],n[3],n[4],n[5],1)}function m(n,r,e){return n[0]=r[0]+e[0],n[1]=r[1]+e[1],n[2]=r[2]+e[2],n[3]=r[3]+e[3],n[4]=r[4]+e[4],n[5]=r[5]+e[5],n}function x(n,r,e){return n[0]=r[0]-e[0],n[1]=r[1]-e[1],n[2]=r[2]-e[2],n[3]=r[3]-e[3],n[4]=r[4]-e[4],n[5]=r[5]-e[5],n}function p(n,r,e){return n[0]=r[0]*e,n[1]=r[1]*e,n[2]=r[2]*e,n[3]=r[3]*e,n[4]=r[4]*e,n[5]=r[5]*e,n}function g(n,r,e,a){return n[0]=r[0]+e[0]*a,n[1]=r[1]+e[1]*a,n[2]=r[2]+e[2]*a,n[3]=r[3]+e[3]*a,n[4]=r[4]+e[4]*a,n[5]=r[5]+e[5]*a,n}function L(n,r){return n[0]===r[0]&&n[1]===r[1]&&n[2]===r[2]&&n[3]===r[3]&&n[4]===r[4]&&n[5]===r[5]}function U(n,r){var e=n[0],a=n[1],t=n[2],i=n[3],c=n[4],v=n[5],s=r[0],M=r[1],y=r[2],u=r[3],E=r[4],A=r[5];return Math.abs(e-s)<=Y.EPSILON*Math.max(1,Math.abs(e),Math.abs(s))&&Math.abs(a-M)<=Y.EPSILON*Math.max(1,Math.abs(a),Math.abs(M))&&Math.abs(t-y)<=Y.EPSILON*Math.max(1,Math.abs(t),Math.abs(y))&&Math.abs(i-u)<=Y.EPSILON*Math.max(1,Math.abs(i),Math.abs(u))&&Math.abs(c-E)<=Y.EPSILON*Math.max(1,Math.abs(c),Math.abs(E))&&Math.abs(v-A)<=Y.EPSILON*Math.max(1,Math.abs(v),Math.abs(A))}var W=Q,b=x,en=K(96661);function k(){var n=new Y.ARRAY_TYPE(16);return Y.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[11]=0,n[12]=0,n[13]=0,n[14]=0),n[0]=1,n[5]=1,n[10]=1,n[15]=1,n}function Mn(n){var r=new Y.ARRAY_TYPE(16);return r[0]=n[0],r[1]=n[1],r[2]=n[2],r[3]=n[3],r[4]=n[4],r[5]=n[5],r[6]=n[6],r[7]=n[7],r[8]=n[8],r[9]=n[9],r[10]=n[10],r[11]=n[11],r[12]=n[12],r[13]=n[13],r[14]=n[14],r[15]=n[15],r}function ln(n,r){return n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n[4]=r[4],n[5]=r[5],n[6]=r[6],n[7]=r[7],n[8]=r[8],n[9]=r[9],n[10]=r[10],n[11]=r[11],n[12]=r[12],n[13]=r[13],n[14]=r[14],n[15]=r[15],n}function En(n,r,e,a,t,i,c,v,s,M,y,u,E,A,q,S){var R=new Y.ARRAY_TYPE(16);return R[0]=n,R[1]=r,R[2]=e,R[3]=a,R[4]=t,R[5]=i,R[6]=c,R[7]=v,R[8]=s,R[9]=M,R[10]=y,R[11]=u,R[12]=E,R[13]=A,R[14]=q,R[15]=S,R}function vn(n,r,e,a,t,i,c,v,s,M,y,u,E,A,q,S,R){return n[0]=r,n[1]=e,n[2]=a,n[3]=t,n[4]=i,n[5]=c,n[6]=v,n[7]=s,n[8]=M,n[9]=y,n[10]=u,n[11]=E,n[12]=A,n[13]=q,n[14]=S,n[15]=R,n}function Kn(n){return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=1,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=1,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n}function Gn(n,r){if(n===r){var e=r[1],a=r[2],t=r[3],i=r[6],c=r[7],v=r[11];n[1]=r[4],n[2]=r[8],n[3]=r[12],n[4]=e,n[6]=r[9],n[7]=r[13],n[8]=a,n[9]=i,n[11]=r[14],n[12]=t,n[13]=c,n[14]=v}else n[0]=r[0],n[1]=r[4],n[2]=r[8],n[3]=r[12],n[4]=r[1],n[5]=r[5],n[6]=r[9],n[7]=r[13],n[8]=r[2],n[9]=r[6],n[10]=r[10],n[11]=r[14],n[12]=r[3],n[13]=r[7],n[14]=r[11],n[15]=r[15];return n}function Hr(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=r[4],v=r[5],s=r[6],M=r[7],y=r[8],u=r[9],E=r[10],A=r[11],q=r[12],S=r[13],R=r[14],I=r[15],B=e*v-a*c,X=e*s-t*c,Z=e*M-i*c,V=a*s-t*v,F=a*M-i*v,an=t*M-i*s,o=y*S-u*q,nn=y*R-E*q,rn=y*I-A*q,tn=u*R-E*S,cn=u*I-A*S,sn=E*I-A*R,J=B*sn-X*cn+Z*tn+V*rn-F*nn+an*o;return J?(J=1/J,n[0]=(v*sn-s*cn+M*tn)*J,n[1]=(t*cn-a*sn-i*tn)*J,n[2]=(S*an-R*F+I*V)*J,n[3]=(E*F-u*an-A*V)*J,n[4]=(s*rn-c*sn-M*nn)*J,n[5]=(e*sn-t*rn+i*nn)*J,n[6]=(R*Z-q*an-I*X)*J,n[7]=(y*an-E*Z+A*X)*J,n[8]=(c*cn-v*rn+M*o)*J,n[9]=(a*rn-e*cn-i*o)*J,n[10]=(q*F-S*Z+I*B)*J,n[11]=(u*Z-y*F-A*B)*J,n[12]=(v*nn-c*tn-s*o)*J,n[13]=(e*tn-a*nn+t*o)*J,n[14]=(S*X-q*V-R*B)*J,n[15]=(y*V-u*X+E*B)*J,n):null}function Jr(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=r[4],v=r[5],s=r[6],M=r[7],y=r[8],u=r[9],E=r[10],A=r[11],q=r[12],S=r[13],R=r[14],I=r[15];return n[0]=v*(E*I-A*R)-u*(s*I-M*R)+S*(s*A-M*E),n[1]=-(a*(E*I-A*R)-u*(t*I-i*R)+S*(t*A-i*E)),n[2]=a*(s*I-M*R)-v*(t*I-i*R)+S*(t*M-i*s),n[3]=-(a*(s*A-M*E)-v*(t*A-i*E)+u*(t*M-i*s)),n[4]=-(c*(E*I-A*R)-y*(s*I-M*R)+q*(s*A-M*E)),n[5]=e*(E*I-A*R)-y*(t*I-i*R)+q*(t*A-i*E),n[6]=-(e*(s*I-M*R)-c*(t*I-i*R)+q*(t*M-i*s)),n[7]=e*(s*A-M*E)-c*(t*A-i*E)+y*(t*M-i*s),n[8]=c*(u*I-A*S)-y*(v*I-M*S)+q*(v*A-M*u),n[9]=-(e*(u*I-A*S)-y*(a*I-i*S)+q*(a*A-i*u)),n[10]=e*(v*I-M*S)-c*(a*I-i*S)+q*(a*M-i*v),n[11]=-(e*(v*A-M*u)-c*(a*A-i*u)+y*(a*M-i*v)),n[12]=-(c*(u*R-E*S)-y*(v*R-s*S)+q*(v*E-s*u)),n[13]=e*(u*R-E*S)-y*(a*R-t*S)+q*(a*E-t*u),n[14]=-(e*(v*R-s*S)-c*(a*R-t*S)+q*(a*s-t*v)),n[15]=e*(v*E-s*u)-c*(a*E-t*u)+y*(a*s-t*v),n}function $r(n){var r=n[0],e=n[1],a=n[2],t=n[3],i=n[4],c=n[5],v=n[6],s=n[7],M=n[8],y=n[9],u=n[10],E=n[11],A=n[12],q=n[13],S=n[14],R=n[15],I=r*c-e*i,B=r*v-a*i,X=r*s-t*i,Z=e*v-a*c,V=e*s-t*c,F=a*s-t*v,an=M*q-y*A,o=M*S-u*A,nn=M*R-E*A,rn=y*S-u*q,tn=y*R-E*q,cn=u*R-E*S;return I*cn-B*tn+X*rn+Z*nn-V*o+F*an}function fr(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=r[4],s=r[5],M=r[6],y=r[7],u=r[8],E=r[9],A=r[10],q=r[11],S=r[12],R=r[13],I=r[14],B=r[15],X=e[0],Z=e[1],V=e[2],F=e[3];return n[0]=X*a+Z*v+V*u+F*S,n[1]=X*t+Z*s+V*E+F*R,n[2]=X*i+Z*M+V*A+F*I,n[3]=X*c+Z*y+V*q+F*B,X=e[4],Z=e[5],V=e[6],F=e[7],n[4]=X*a+Z*v+V*u+F*S,n[5]=X*t+Z*s+V*E+F*R,n[6]=X*i+Z*M+V*A+F*I,n[7]=X*c+Z*y+V*q+F*B,X=e[8],Z=e[9],V=e[10],F=e[11],n[8]=X*a+Z*v+V*u+F*S,n[9]=X*t+Z*s+V*E+F*R,n[10]=X*i+Z*M+V*A+F*I,n[11]=X*c+Z*y+V*q+F*B,X=e[12],Z=e[13],V=e[14],F=e[15],n[12]=X*a+Z*v+V*u+F*S,n[13]=X*t+Z*s+V*E+F*R,n[14]=X*i+Z*M+V*A+F*I,n[15]=X*c+Z*y+V*q+F*B,n}function br(n,r,e){var a=e[0],t=e[1],i=e[2],c,v,s,M,y,u,E,A,q,S,R,I;return r===n?(n[12]=r[0]*a+r[4]*t+r[8]*i+r[12],n[13]=r[1]*a+r[5]*t+r[9]*i+r[13],n[14]=r[2]*a+r[6]*t+r[10]*i+r[14],n[15]=r[3]*a+r[7]*t+r[11]*i+r[15]):(c=r[0],v=r[1],s=r[2],M=r[3],y=r[4],u=r[5],E=r[6],A=r[7],q=r[8],S=r[9],R=r[10],I=r[11],n[0]=c,n[1]=v,n[2]=s,n[3]=M,n[4]=y,n[5]=u,n[6]=E,n[7]=A,n[8]=q,n[9]=S,n[10]=R,n[11]=I,n[12]=c*a+y*t+q*i+r[12],n[13]=v*a+u*t+S*i+r[13],n[14]=s*a+E*t+R*i+r[14],n[15]=M*a+A*t+I*i+r[15]),n}function kr(n,r,e){var a=e[0],t=e[1],i=e[2];return n[0]=r[0]*a,n[1]=r[1]*a,n[2]=r[2]*a,n[3]=r[3]*a,n[4]=r[4]*t,n[5]=r[5]*t,n[6]=r[6]*t,n[7]=r[7]*t,n[8]=r[8]*i,n[9]=r[9]*i,n[10]=r[10]*i,n[11]=r[11]*i,n[12]=r[12],n[13]=r[13],n[14]=r[14],n[15]=r[15],n}function or(n,r,e,a){var t=a[0],i=a[1],c=a[2],v=Math.hypot(t,i,c),s,M,y,u,E,A,q,S,R,I,B,X,Z,V,F,an,o,nn,rn,tn,cn,sn,J,zn;return v<Y.EPSILON?null:(v=1/v,t*=v,i*=v,c*=v,s=Math.sin(e),M=Math.cos(e),y=1-M,u=r[0],E=r[1],A=r[2],q=r[3],S=r[4],R=r[5],I=r[6],B=r[7],X=r[8],Z=r[9],V=r[10],F=r[11],an=t*t*y+M,o=i*t*y+c*s,nn=c*t*y-i*s,rn=t*i*y-c*s,tn=i*i*y+M,cn=c*i*y+t*s,sn=t*c*y+i*s,J=i*c*y-t*s,zn=c*c*y+M,n[0]=u*an+S*o+X*nn,n[1]=E*an+R*o+Z*nn,n[2]=A*an+I*o+V*nn,n[3]=q*an+B*o+F*nn,n[4]=u*rn+S*tn+X*cn,n[5]=E*rn+R*tn+Z*cn,n[6]=A*rn+I*tn+V*cn,n[7]=q*rn+B*tn+F*cn,n[8]=u*sn+S*J+X*zn,n[9]=E*sn+R*J+Z*zn,n[10]=A*sn+I*J+V*zn,n[11]=q*sn+B*J+F*zn,r!==n&&(n[12]=r[12],n[13]=r[13],n[14]=r[14],n[15]=r[15]),n)}function n1(n,r,e){var a=Math.sin(e),t=Math.cos(e),i=r[4],c=r[5],v=r[6],s=r[7],M=r[8],y=r[9],u=r[10],E=r[11];return r!==n&&(n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n[12]=r[12],n[13]=r[13],n[14]=r[14],n[15]=r[15]),n[4]=i*t+M*a,n[5]=c*t+y*a,n[6]=v*t+u*a,n[7]=s*t+E*a,n[8]=M*t-i*a,n[9]=y*t-c*a,n[10]=u*t-v*a,n[11]=E*t-s*a,n}function r1(n,r,e){var a=Math.sin(e),t=Math.cos(e),i=r[0],c=r[1],v=r[2],s=r[3],M=r[8],y=r[9],u=r[10],E=r[11];return r!==n&&(n[4]=r[4],n[5]=r[5],n[6]=r[6],n[7]=r[7],n[12]=r[12],n[13]=r[13],n[14]=r[14],n[15]=r[15]),n[0]=i*t-M*a,n[1]=c*t-y*a,n[2]=v*t-u*a,n[3]=s*t-E*a,n[8]=i*a+M*t,n[9]=c*a+y*t,n[10]=v*a+u*t,n[11]=s*a+E*t,n}function e1(n,r,e){var a=Math.sin(e),t=Math.cos(e),i=r[0],c=r[1],v=r[2],s=r[3],M=r[4],y=r[5],u=r[6],E=r[7];return r!==n&&(n[8]=r[8],n[9]=r[9],n[10]=r[10],n[11]=r[11],n[12]=r[12],n[13]=r[13],n[14]=r[14],n[15]=r[15]),n[0]=i*t+M*a,n[1]=c*t+y*a,n[2]=v*t+u*a,n[3]=s*t+E*a,n[4]=M*t-i*a,n[5]=y*t-c*a,n[6]=u*t-v*a,n[7]=E*t-s*a,n}function a1(n,r){return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=1,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=1,n[11]=0,n[12]=r[0],n[13]=r[1],n[14]=r[2],n[15]=1,n}function t1(n,r){return n[0]=r[0],n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=r[1],n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=r[2],n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n}function i1(n,r,e){var a=e[0],t=e[1],i=e[2],c=Math.hypot(a,t,i),v,s,M;return c<Y.EPSILON?null:(c=1/c,a*=c,t*=c,i*=c,v=Math.sin(r),s=Math.cos(r),M=1-s,n[0]=a*a*M+s,n[1]=t*a*M+i*v,n[2]=i*a*M-t*v,n[3]=0,n[4]=a*t*M-i*v,n[5]=t*t*M+s,n[6]=i*t*M+a*v,n[7]=0,n[8]=a*i*M+t*v,n[9]=t*i*M-a*v,n[10]=i*i*M+s,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n)}function c1(n,r){var e=Math.sin(r),a=Math.cos(r);return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=a,n[6]=e,n[7]=0,n[8]=0,n[9]=-e,n[10]=a,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n}function f1(n,r){var e=Math.sin(r),a=Math.cos(r);return n[0]=a,n[1]=0,n[2]=-e,n[3]=0,n[4]=0,n[5]=1,n[6]=0,n[7]=0,n[8]=e,n[9]=0,n[10]=a,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n}function v1(n,r){var e=Math.sin(r),a=Math.cos(r);return n[0]=a,n[1]=e,n[2]=0,n[3]=0,n[4]=-e,n[5]=a,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=1,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n}function vr(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=a+a,s=t+t,M=i+i,y=a*v,u=a*s,E=a*M,A=t*s,q=t*M,S=i*M,R=c*v,I=c*s,B=c*M;return n[0]=1-(A+S),n[1]=u+B,n[2]=E-I,n[3]=0,n[4]=u-B,n[5]=1-(y+S),n[6]=q+R,n[7]=0,n[8]=E+I,n[9]=q-R,n[10]=1-(y+A),n[11]=0,n[12]=e[0],n[13]=e[1],n[14]=e[2],n[15]=1,n}function s1(n,r){var e=new Y.ARRAY_TYPE(3),a=-r[0],t=-r[1],i=-r[2],c=r[3],v=r[4],s=r[5],M=r[6],y=r[7],u=a*a+t*t+i*i+c*c;return u>0?(e[0]=(v*c+y*a+s*i-M*t)*2/u,e[1]=(s*c+y*t+M*a-v*i)*2/u,e[2]=(M*c+y*i+v*t-s*a)*2/u):(e[0]=(v*c+y*a+s*i-M*t)*2,e[1]=(s*c+y*t+M*a-v*i)*2,e[2]=(M*c+y*i+v*t-s*a)*2),vr(n,r,e),n}function sr(n,r){return n[0]=r[12],n[1]=r[13],n[2]=r[14],n}function hr(n,r){var e=r[0],a=r[1],t=r[2],i=r[4],c=r[5],v=r[6],s=r[8],M=r[9],y=r[10];return n[0]=Math.hypot(e,a,t),n[1]=Math.hypot(i,c,v),n[2]=Math.hypot(s,M,y),n}function Mr(n,r){var e=new Y.ARRAY_TYPE(3);hr(e,r);var a=1/e[0],t=1/e[1],i=1/e[2],c=r[0]*a,v=r[1]*t,s=r[2]*i,M=r[4]*a,y=r[5]*t,u=r[6]*i,E=r[8]*a,A=r[9]*t,q=r[10]*i,S=c+y+q,R=0;return S>0?(R=Math.sqrt(S+1)*2,n[3]=.25*R,n[0]=(u-A)/R,n[1]=(E-s)/R,n[2]=(v-M)/R):c>y&&c>q?(R=Math.sqrt(1+c-y-q)*2,n[3]=(u-A)/R,n[0]=.25*R,n[1]=(v+M)/R,n[2]=(E+s)/R):y>q?(R=Math.sqrt(1+y-c-q)*2,n[3]=(E-s)/R,n[0]=(v+M)/R,n[1]=.25*R,n[2]=(u+A)/R):(R=Math.sqrt(1+q-c-y)*2,n[3]=(v-M)/R,n[0]=(E+s)/R,n[1]=(u+A)/R,n[2]=.25*R),n}function h1(n,r,e,a){var t=r[0],i=r[1],c=r[2],v=r[3],s=t+t,M=i+i,y=c+c,u=t*s,E=t*M,A=t*y,q=i*M,S=i*y,R=c*y,I=v*s,B=v*M,X=v*y,Z=a[0],V=a[1],F=a[2];return n[0]=(1-(q+R))*Z,n[1]=(E+X)*Z,n[2]=(A-B)*Z,n[3]=0,n[4]=(E-X)*V,n[5]=(1-(u+R))*V,n[6]=(S+I)*V,n[7]=0,n[8]=(A+B)*F,n[9]=(S-I)*F,n[10]=(1-(u+q))*F,n[11]=0,n[12]=e[0],n[13]=e[1],n[14]=e[2],n[15]=1,n}function M1(n,r,e,a,t){var i=r[0],c=r[1],v=r[2],s=r[3],M=i+i,y=c+c,u=v+v,E=i*M,A=i*y,q=i*u,S=c*y,R=c*u,I=v*u,B=s*M,X=s*y,Z=s*u,V=a[0],F=a[1],an=a[2],o=t[0],nn=t[1],rn=t[2],tn=(1-(S+I))*V,cn=(A+Z)*V,sn=(q-X)*V,J=(A-Z)*F,zn=(1-(E+I))*F,Hn=(R+B)*F,Jn=(q+X)*an,Kr=(R-B)*an,Gr=(1-(E+S))*an;return n[0]=tn,n[1]=cn,n[2]=sn,n[3]=0,n[4]=J,n[5]=zn,n[6]=Hn,n[7]=0,n[8]=Jn,n[9]=Kr,n[10]=Gr,n[11]=0,n[12]=e[0]+o-(tn*o+J*nn+Jn*rn),n[13]=e[1]+nn-(cn*o+zn*nn+Kr*rn),n[14]=e[2]+rn-(sn*o+Hn*nn+Gr*rn),n[15]=1,n}function l1(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=e+e,v=a+a,s=t+t,M=e*c,y=a*c,u=a*v,E=t*c,A=t*v,q=t*s,S=i*c,R=i*v,I=i*s;return n[0]=1-u-q,n[1]=y+I,n[2]=E-R,n[3]=0,n[4]=y-I,n[5]=1-M-q,n[6]=A+S,n[7]=0,n[8]=E+R,n[9]=A-S,n[10]=1-M-u,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n}function m1(n,r,e,a,t,i,c){var v=1/(e-r),s=1/(t-a),M=1/(i-c);return n[0]=i*2*v,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=i*2*s,n[6]=0,n[7]=0,n[8]=(e+r)*v,n[9]=(t+a)*s,n[10]=(c+i)*M,n[11]=-1,n[12]=0,n[13]=0,n[14]=c*i*2*M,n[15]=0,n}function lr(n,r,e,a,t){var i=1/Math.tan(r/2),c;return n[0]=i/e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=i,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[11]=-1,n[12]=0,n[13]=0,n[15]=0,t!=null&&t!==1/0?(c=1/(a-t),n[10]=(t+a)*c,n[14]=2*t*a*c):(n[10]=-1,n[14]=-2*a),n}var y1=lr;function d1(n,r,e,a,t){var i=1/Math.tan(r/2),c;return n[0]=i/e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=i,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[11]=-1,n[12]=0,n[13]=0,n[15]=0,t!=null&&t!==1/0?(c=1/(a-t),n[10]=t*c,n[14]=t*a*c):(n[10]=-1,n[14]=-a),n}function x1(n,r,e,a){var t=Math.tan(r.upDegrees*Math.PI/180),i=Math.tan(r.downDegrees*Math.PI/180),c=Math.tan(r.leftDegrees*Math.PI/180),v=Math.tan(r.rightDegrees*Math.PI/180),s=2/(c+v),M=2/(t+i);return n[0]=s,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=M,n[6]=0,n[7]=0,n[8]=-((c-v)*s*.5),n[9]=(t-i)*M*.5,n[10]=a/(e-a),n[11]=-1,n[12]=0,n[13]=0,n[14]=a*e/(e-a),n[15]=0,n}function mr(n,r,e,a,t,i,c){var v=1/(r-e),s=1/(a-t),M=1/(i-c);return n[0]=-2*v,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=-2*s,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=2*M,n[11]=0,n[12]=(r+e)*v,n[13]=(t+a)*s,n[14]=(c+i)*M,n[15]=1,n}var u1=mr;function A1(n,r,e,a,t,i,c){var v=1/(r-e),s=1/(a-t),M=1/(i-c);return n[0]=-2*v,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=-2*s,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=M,n[11]=0,n[12]=(r+e)*v,n[13]=(t+a)*s,n[14]=i*M,n[15]=1,n}function z1(n,r,e,a){var t,i,c,v,s,M,y,u,E,A,q=r[0],S=r[1],R=r[2],I=a[0],B=a[1],X=a[2],Z=e[0],V=e[1],F=e[2];return Math.abs(q-Z)<Y.EPSILON&&Math.abs(S-V)<Y.EPSILON&&Math.abs(R-F)<Y.EPSILON?Kn(n):(y=q-Z,u=S-V,E=R-F,A=1/Math.hypot(y,u,E),y*=A,u*=A,E*=A,t=B*E-X*u,i=X*y-I*E,c=I*u-B*y,A=Math.hypot(t,i,c),A?(A=1/A,t*=A,i*=A,c*=A):(t=0,i=0,c=0),v=u*c-E*i,s=E*t-y*c,M=y*i-u*t,A=Math.hypot(v,s,M),A?(A=1/A,v*=A,s*=A,M*=A):(v=0,s=0,M=0),n[0]=t,n[1]=v,n[2]=y,n[3]=0,n[4]=i,n[5]=s,n[6]=u,n[7]=0,n[8]=c,n[9]=M,n[10]=E,n[11]=0,n[12]=-(t*q+i*S+c*R),n[13]=-(v*q+s*S+M*R),n[14]=-(y*q+u*S+E*R),n[15]=1,n)}function E1(n,r,e,a){var t=r[0],i=r[1],c=r[2],v=a[0],s=a[1],M=a[2],y=t-e[0],u=i-e[1],E=c-e[2],A=y*y+u*u+E*E;A>0&&(A=1/Math.sqrt(A),y*=A,u*=A,E*=A);var q=s*E-M*u,S=M*y-v*E,R=v*u-s*y;return A=q*q+S*S+R*R,A>0&&(A=1/Math.sqrt(A),q*=A,S*=A,R*=A),n[0]=q,n[1]=S,n[2]=R,n[3]=0,n[4]=u*R-E*S,n[5]=E*q-y*R,n[6]=y*S-u*q,n[7]=0,n[8]=y,n[9]=u,n[10]=E,n[11]=0,n[12]=t,n[13]=i,n[14]=c,n[15]=1,n}function R1(n){return"mat4("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+", "+n[6]+", "+n[7]+", "+n[8]+", "+n[9]+", "+n[10]+", "+n[11]+", "+n[12]+", "+n[13]+", "+n[14]+", "+n[15]+")"}function P1(n){return Math.hypot(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15])}function q1(n,r,e){return n[0]=r[0]+e[0],n[1]=r[1]+e[1],n[2]=r[2]+e[2],n[3]=r[3]+e[3],n[4]=r[4]+e[4],n[5]=r[5]+e[5],n[6]=r[6]+e[6],n[7]=r[7]+e[7],n[8]=r[8]+e[8],n[9]=r[9]+e[9],n[10]=r[10]+e[10],n[11]=r[11]+e[11],n[12]=r[12]+e[12],n[13]=r[13]+e[13],n[14]=r[14]+e[14],n[15]=r[15]+e[15],n}function yr(n,r,e){return n[0]=r[0]-e[0],n[1]=r[1]-e[1],n[2]=r[2]-e[2],n[3]=r[3]-e[3],n[4]=r[4]-e[4],n[5]=r[5]-e[5],n[6]=r[6]-e[6],n[7]=r[7]-e[7],n[8]=r[8]-e[8],n[9]=r[9]-e[9],n[10]=r[10]-e[10],n[11]=r[11]-e[11],n[12]=r[12]-e[12],n[13]=r[13]-e[13],n[14]=r[14]-e[14],n[15]=r[15]-e[15],n}function p1(n,r,e){return n[0]=r[0]*e,n[1]=r[1]*e,n[2]=r[2]*e,n[3]=r[3]*e,n[4]=r[4]*e,n[5]=r[5]*e,n[6]=r[6]*e,n[7]=r[7]*e,n[8]=r[8]*e,n[9]=r[9]*e,n[10]=r[10]*e,n[11]=r[11]*e,n[12]=r[12]*e,n[13]=r[13]*e,n[14]=r[14]*e,n[15]=r[15]*e,n}function O1(n,r,e,a){return n[0]=r[0]+e[0]*a,n[1]=r[1]+e[1]*a,n[2]=r[2]+e[2]*a,n[3]=r[3]+e[3]*a,n[4]=r[4]+e[4]*a,n[5]=r[5]+e[5]*a,n[6]=r[6]+e[6]*a,n[7]=r[7]+e[7]*a,n[8]=r[8]+e[8]*a,n[9]=r[9]+e[9]*a,n[10]=r[10]+e[10]*a,n[11]=r[11]+e[11]*a,n[12]=r[12]+e[12]*a,n[13]=r[13]+e[13]*a,n[14]=r[14]+e[14]*a,n[15]=r[15]+e[15]*a,n}function S1(n,r){return n[0]===r[0]&&n[1]===r[1]&&n[2]===r[2]&&n[3]===r[3]&&n[4]===r[4]&&n[5]===r[5]&&n[6]===r[6]&&n[7]===r[7]&&n[8]===r[8]&&n[9]===r[9]&&n[10]===r[10]&&n[11]===r[11]&&n[12]===r[12]&&n[13]===r[13]&&n[14]===r[14]&&n[15]===r[15]}function g1(n,r){var e=n[0],a=n[1],t=n[2],i=n[3],c=n[4],v=n[5],s=n[6],M=n[7],y=n[8],u=n[9],E=n[10],A=n[11],q=n[12],S=n[13],R=n[14],I=n[15],B=r[0],X=r[1],Z=r[2],V=r[3],F=r[4],an=r[5],o=r[6],nn=r[7],rn=r[8],tn=r[9],cn=r[10],sn=r[11],J=r[12],zn=r[13],Hn=r[14],Jn=r[15];return Math.abs(e-B)<=Y.EPSILON*Math.max(1,Math.abs(e),Math.abs(B))&&Math.abs(a-X)<=Y.EPSILON*Math.max(1,Math.abs(a),Math.abs(X))&&Math.abs(t-Z)<=Y.EPSILON*Math.max(1,Math.abs(t),Math.abs(Z))&&Math.abs(i-V)<=Y.EPSILON*Math.max(1,Math.abs(i),Math.abs(V))&&Math.abs(c-F)<=Y.EPSILON*Math.max(1,Math.abs(c),Math.abs(F))&&Math.abs(v-an)<=Y.EPSILON*Math.max(1,Math.abs(v),Math.abs(an))&&Math.abs(s-o)<=Y.EPSILON*Math.max(1,Math.abs(s),Math.abs(o))&&Math.abs(M-nn)<=Y.EPSILON*Math.max(1,Math.abs(M),Math.abs(nn))&&Math.abs(y-rn)<=Y.EPSILON*Math.max(1,Math.abs(y),Math.abs(rn))&&Math.abs(u-tn)<=Y.EPSILON*Math.max(1,Math.abs(u),Math.abs(tn))&&Math.abs(E-cn)<=Y.EPSILON*Math.max(1,Math.abs(E),Math.abs(cn))&&Math.abs(A-sn)<=Y.EPSILON*Math.max(1,Math.abs(A),Math.abs(sn))&&Math.abs(q-J)<=Y.EPSILON*Math.max(1,Math.abs(q),Math.abs(J))&&Math.abs(S-zn)<=Y.EPSILON*Math.max(1,Math.abs(S),Math.abs(zn))&&Math.abs(R-Hn)<=Y.EPSILON*Math.max(1,Math.abs(R),Math.abs(Hn))&&Math.abs(I-Jn)<=Y.EPSILON*Math.max(1,Math.abs(I),Math.abs(Jn))}var Y1=fr,L1=yr,Rn=K(62329);function dr(){var n=new Y.ARRAY_TYPE(4);return Y.ARRAY_TYPE!=Float32Array&&(n[0]=0,n[1]=0,n[2]=0,n[3]=0),n}function xr(n){var r=new Y.ARRAY_TYPE(4);return r[0]=n[0],r[1]=n[1],r[2]=n[2],r[3]=n[3],r}function ur(n,r,e,a){var t=new Y.ARRAY_TYPE(4);return t[0]=n,t[1]=r,t[2]=e,t[3]=a,t}function Ar(n,r){return n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n}function zr(n,r,e,a,t){return n[0]=r,n[1]=e,n[2]=a,n[3]=t,n}function Er(n,r,e){return n[0]=r[0]+e[0],n[1]=r[1]+e[1],n[2]=r[2]+e[2],n[3]=r[3]+e[3],n}function Rr(n,r,e){return n[0]=r[0]-e[0],n[1]=r[1]-e[1],n[2]=r[2]-e[2],n[3]=r[3]-e[3],n}function Pr(n,r,e){return n[0]=r[0]*e[0],n[1]=r[1]*e[1],n[2]=r[2]*e[2],n[3]=r[3]*e[3],n}function qr(n,r,e){return n[0]=r[0]/e[0],n[1]=r[1]/e[1],n[2]=r[2]/e[2],n[3]=r[3]/e[3],n}function I1(n,r){return n[0]=Math.ceil(r[0]),n[1]=Math.ceil(r[1]),n[2]=Math.ceil(r[2]),n[3]=Math.ceil(r[3]),n}function T1(n,r){return n[0]=Math.floor(r[0]),n[1]=Math.floor(r[1]),n[2]=Math.floor(r[2]),n[3]=Math.floor(r[3]),n}function N1(n,r,e){return n[0]=Math.min(r[0],e[0]),n[1]=Math.min(r[1],e[1]),n[2]=Math.min(r[2],e[2]),n[3]=Math.min(r[3],e[3]),n}function w1(n,r,e){return n[0]=Math.max(r[0],e[0]),n[1]=Math.max(r[1],e[1]),n[2]=Math.max(r[2],e[2]),n[3]=Math.max(r[3],e[3]),n}function _1(n,r){return n[0]=Math.round(r[0]),n[1]=Math.round(r[1]),n[2]=Math.round(r[2]),n[3]=Math.round(r[3]),n}function pr(n,r,e){return n[0]=r[0]*e,n[1]=r[1]*e,n[2]=r[2]*e,n[3]=r[3]*e,n}function D1(n,r,e,a){return n[0]=r[0]+e[0]*a,n[1]=r[1]+e[1]*a,n[2]=r[2]+e[2]*a,n[3]=r[3]+e[3]*a,n}function Or(n,r){var e=r[0]-n[0],a=r[1]-n[1],t=r[2]-n[2],i=r[3]-n[3];return Math.hypot(e,a,t,i)}function Sr(n,r){var e=r[0]-n[0],a=r[1]-n[1],t=r[2]-n[2],i=r[3]-n[3];return e*e+a*a+t*t+i*i}function nr(n){var r=n[0],e=n[1],a=n[2],t=n[3];return Math.hypot(r,e,a,t)}function rr(n){var r=n[0],e=n[1],a=n[2],t=n[3];return r*r+e*e+a*a+t*t}function V1(n,r){return n[0]=-r[0],n[1]=-r[1],n[2]=-r[2],n[3]=-r[3],n}function F1(n,r){return n[0]=1/r[0],n[1]=1/r[1],n[2]=1/r[2],n[3]=1/r[3],n}function gr(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=e*e+a*a+t*t+i*i;return c>0&&(c=1/Math.sqrt(c)),n[0]=e*c,n[1]=a*c,n[2]=t*c,n[3]=i*c,n}function Yr(n,r){return n[0]*r[0]+n[1]*r[1]+n[2]*r[2]+n[3]*r[3]}function j1(n,r,e,a){var t=e[0]*a[1]-e[1]*a[0],i=e[0]*a[2]-e[2]*a[0],c=e[0]*a[3]-e[3]*a[0],v=e[1]*a[2]-e[2]*a[1],s=e[1]*a[3]-e[3]*a[1],M=e[2]*a[3]-e[3]*a[2],y=r[0],u=r[1],E=r[2],A=r[3];return n[0]=u*M-E*s+A*v,n[1]=-(y*M)+E*c-A*i,n[2]=y*s-u*c+A*t,n[3]=-(y*v)+u*i-E*t,n}function Lr(n,r,e,a){var t=r[0],i=r[1],c=r[2],v=r[3];return n[0]=t+a*(e[0]-t),n[1]=i+a*(e[1]-i),n[2]=c+a*(e[2]-c),n[3]=v+a*(e[3]-v),n}function Q1(n,r){r=r||1;var e,a,t,i,c,v;do e=Y.RANDOM()*2-1,a=Y.RANDOM()*2-1,c=e*e+a*a;while(c>=1);do t=Y.RANDOM()*2-1,i=Y.RANDOM()*2-1,v=t*t+i*i;while(v>=1);var s=Math.sqrt((1-c)/v);return n[0]=r*e,n[1]=r*a,n[2]=r*t*s,n[3]=r*i*s,n}function Z1(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3];return n[0]=e[0]*a+e[4]*t+e[8]*i+e[12]*c,n[1]=e[1]*a+e[5]*t+e[9]*i+e[13]*c,n[2]=e[2]*a+e[6]*t+e[10]*i+e[14]*c,n[3]=e[3]*a+e[7]*t+e[11]*i+e[15]*c,n}function X1(n,r,e){var a=r[0],t=r[1],i=r[2],c=e[0],v=e[1],s=e[2],M=e[3],y=M*a+v*i-s*t,u=M*t+s*a-c*i,E=M*i+c*t-v*a,A=-c*a-v*t-s*i;return n[0]=y*M+A*-c+u*-s-E*-v,n[1]=u*M+A*-v+E*-c-y*-s,n[2]=E*M+A*-s+y*-v-u*-c,n[3]=r[3],n}function B1(n){return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n}function C1(n){return"vec4("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+")"}function Ir(n,r){return n[0]===r[0]&&n[1]===r[1]&&n[2]===r[2]&&n[3]===r[3]}function Tr(n,r){var e=n[0],a=n[1],t=n[2],i=n[3],c=r[0],v=r[1],s=r[2],M=r[3];return Math.abs(e-c)<=Y.EPSILON*Math.max(1,Math.abs(e),Math.abs(c))&&Math.abs(a-v)<=Y.EPSILON*Math.max(1,Math.abs(a),Math.abs(v))&&Math.abs(t-s)<=Y.EPSILON*Math.max(1,Math.abs(t),Math.abs(s))&&Math.abs(i-M)<=Y.EPSILON*Math.max(1,Math.abs(i),Math.abs(M))}var U1=Rr,W1=Pr,K1=qr,G1=Or,H1=Sr,J1=nr,$1=rr,b1=function(){var n=dr();return function(r,e,a,t,i,c){var v,s;for(e||(e=4),a||(a=0),t?s=Math.min(t*e+a,r.length):s=r.length,v=a;v<s;v+=e)n[0]=r[v],n[1]=r[v+1],n[2]=r[v+2],n[3]=r[v+3],i(n,n,c),r[v]=n[0],r[v+1]=n[1],r[v+2]=n[2],r[v+3]=n[3];return r}}();function bn(){var n=new Y.ARRAY_TYPE(4);return Y.ARRAY_TYPE!=Float32Array&&(n[0]=0,n[1]=0,n[2]=0),n[3]=1,n}function k1(n){return n[0]=0,n[1]=0,n[2]=0,n[3]=1,n}function Nr(n,r,e){e=e*.5;var a=Math.sin(e);return n[0]=a*r[0],n[1]=a*r[1],n[2]=a*r[2],n[3]=Math.cos(e),n}function o1(n,r){var e=Math.acos(r[3])*2,a=Math.sin(e/2);return a>Y.EPSILON?(n[0]=r[0]/a,n[1]=r[1]/a,n[2]=r[2]/a):(n[0]=1,n[1]=0,n[2]=0),e}function n0(n,r){var e=ar(n,r);return Math.acos(2*e*e-1)}function wr(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=e[0],s=e[1],M=e[2],y=e[3];return n[0]=a*y+c*v+t*M-i*s,n[1]=t*y+c*s+i*v-a*M,n[2]=i*y+c*M+a*s-t*v,n[3]=c*y-a*v-t*s-i*M,n}function _r(n,r,e){e*=.5;var a=r[0],t=r[1],i=r[2],c=r[3],v=Math.sin(e),s=Math.cos(e);return n[0]=a*s+c*v,n[1]=t*s+i*v,n[2]=i*s-t*v,n[3]=c*s-a*v,n}function Dr(n,r,e){e*=.5;var a=r[0],t=r[1],i=r[2],c=r[3],v=Math.sin(e),s=Math.cos(e);return n[0]=a*s-i*v,n[1]=t*s+c*v,n[2]=i*s+a*v,n[3]=c*s-t*v,n}function Vr(n,r,e){e*=.5;var a=r[0],t=r[1],i=r[2],c=r[3],v=Math.sin(e),s=Math.cos(e);return n[0]=a*s+t*v,n[1]=t*s-a*v,n[2]=i*s+c*v,n[3]=c*s-i*v,n}function r0(n,r){var e=r[0],a=r[1],t=r[2];return n[0]=e,n[1]=a,n[2]=t,n[3]=Math.sqrt(Math.abs(1-e*e-a*a-t*t)),n}function Fr(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=Math.sqrt(e*e+a*a+t*t),v=Math.exp(i),s=c>0?v*Math.sin(c)/c:0;return n[0]=e*s,n[1]=a*s,n[2]=t*s,n[3]=v*Math.cos(c),n}function jr(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=Math.sqrt(e*e+a*a+t*t),v=c>0?Math.atan2(c,i)/c:0;return n[0]=e*v,n[1]=a*v,n[2]=t*v,n[3]=.5*Math.log(e*e+a*a+t*t+i*i),n}function e0(n,r,e){return jr(n,r),Zr(n,n,e),Fr(n,n),n}function kn(n,r,e,a){var t=r[0],i=r[1],c=r[2],v=r[3],s=e[0],M=e[1],y=e[2],u=e[3],E,A,q,S,R;return A=t*s+i*M+c*y+v*u,A<0&&(A=-A,s=-s,M=-M,y=-y,u=-u),1-A>Y.EPSILON?(E=Math.acos(A),q=Math.sin(E),S=Math.sin((1-a)*E)/q,R=Math.sin(a*E)/q):(S=1-a,R=a),n[0]=S*t+R*s,n[1]=S*i+R*M,n[2]=S*c+R*y,n[3]=S*v+R*u,n}function a0(n){var r=Y.RANDOM(),e=Y.RANDOM(),a=Y.RANDOM(),t=Math.sqrt(1-r),i=Math.sqrt(r);return n[0]=t*Math.sin(2*Math.PI*e),n[1]=t*Math.cos(2*Math.PI*e),n[2]=i*Math.sin(2*Math.PI*a),n[3]=i*Math.cos(2*Math.PI*a),n}function t0(n,r){var e=r[0],a=r[1],t=r[2],i=r[3],c=e*e+a*a+t*t+i*i,v=c?1/c:0;return n[0]=-e*v,n[1]=-a*v,n[2]=-t*v,n[3]=i*v,n}function i0(n,r){return n[0]=-r[0],n[1]=-r[1],n[2]=-r[2],n[3]=r[3],n}function Qr(n,r){var e=r[0]+r[4]+r[8],a;if(e>0)a=Math.sqrt(e+1),n[3]=.5*a,a=.5/a,n[0]=(r[5]-r[7])*a,n[1]=(r[6]-r[2])*a,n[2]=(r[1]-r[3])*a;else{var t=0;r[4]>r[0]&&(t=1),r[8]>r[t*3+t]&&(t=2);var i=(t+1)%3,c=(t+2)%3;a=Math.sqrt(r[t*3+t]-r[i*3+i]-r[c*3+c]+1),n[t]=.5*a,a=.5/a,n[3]=(r[i*3+c]-r[c*3+i])*a,n[i]=(r[i*3+t]+r[t*3+i])*a,n[c]=(r[c*3+t]+r[t*3+c])*a}return n}function c0(n,r,e,a){var t=.5*Math.PI/180;r*=t,e*=t,a*=t;var i=Math.sin(r),c=Math.cos(r),v=Math.sin(e),s=Math.cos(e),M=Math.sin(a),y=Math.cos(a);return n[0]=i*s*y-c*v*M,n[1]=c*v*y+i*s*M,n[2]=c*s*M-i*v*y,n[3]=c*s*y+i*v*M,n}function f0(n){return"quat("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+")"}var v0=xr,s0=ur,er=Ar,h0=zr,M0=Er,l0=wr,Zr=pr,ar=Yr,m0=Lr,tr=nr,y0=tr,ir=rr,d0=ir,cr=gr,x0=Ir,u0=Tr,A0=function(){var n=Rn.create(),r=Rn.fromValues(1,0,0),e=Rn.fromValues(0,1,0);return function(a,t,i){var c=Rn.dot(t,i);return c<-.999999?(Rn.cross(n,r,t),Rn.len(n)<1e-6&&Rn.cross(n,e,t),Rn.normalize(n,n),Nr(a,n,Math.PI),a):c>.999999?(a[0]=0,a[1]=0,a[2]=0,a[3]=1,a):(Rn.cross(n,t,i),a[0]=n[0],a[1]=n[1],a[2]=n[2],a[3]=1+c,cr(a,a))}}(),z0=function(){var n=bn(),r=bn();return function(e,a,t,i,c,v){return kn(n,a,c,v),kn(r,t,i,v),kn(e,n,r,2*v*(1-v)),e}}(),E0=function(){var n=en.create();return function(r,e,a,t){return n[0]=a[0],n[3]=a[1],n[6]=a[2],n[1]=t[0],n[4]=t[1],n[7]=t[2],n[2]=-e[0],n[5]=-e[1],n[8]=-e[2],cr(r,Qr(r,n))}}();function R0(){var n=new Y.ARRAY_TYPE(8);return Y.ARRAY_TYPE!=Float32Array&&(n[0]=0,n[1]=0,n[2]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0),n[3]=1,n}function P0(n){var r=new Y.ARRAY_TYPE(8);return r[0]=n[0],r[1]=n[1],r[2]=n[2],r[3]=n[3],r[4]=n[4],r[5]=n[5],r[6]=n[6],r[7]=n[7],r}function q0(n,r,e,a,t,i,c,v){var s=new Y.ARRAY_TYPE(8);return s[0]=n,s[1]=r,s[2]=e,s[3]=a,s[4]=t,s[5]=i,s[6]=c,s[7]=v,s}function p0(n,r,e,a,t,i,c){var v=new Y.ARRAY_TYPE(8);v[0]=n,v[1]=r,v[2]=e,v[3]=a;var s=t*.5,M=i*.5,y=c*.5;return v[4]=s*a+M*e-y*r,v[5]=M*a+y*n-s*e,v[6]=y*a+s*r-M*n,v[7]=-s*n-M*r-y*e,v}function Xr(n,r,e){var a=e[0]*.5,t=e[1]*.5,i=e[2]*.5,c=r[0],v=r[1],s=r[2],M=r[3];return n[0]=c,n[1]=v,n[2]=s,n[3]=M,n[4]=a*M+t*s-i*v,n[5]=t*M+i*c-a*s,n[6]=i*M+a*v-t*c,n[7]=-a*c-t*v-i*s,n}function O0(n,r){return n[0]=0,n[1]=0,n[2]=0,n[3]=1,n[4]=r[0]*.5,n[5]=r[1]*.5,n[6]=r[2]*.5,n[7]=0,n}function S0(n,r){return n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n[4]=0,n[5]=0,n[6]=0,n[7]=0,n}function g0(n,r){var e=bn();Mr(e,r);var a=new Y.ARRAY_TYPE(3);return sr(a,r),Xr(n,e,a),n}function Br(n,r){return n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],n[4]=r[4],n[5]=r[5],n[6]=r[6],n[7]=r[7],n}function Y0(n){return n[0]=0,n[1]=0,n[2]=0,n[3]=1,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n}function L0(n,r,e,a,t,i,c,v,s){return n[0]=r,n[1]=e,n[2]=a,n[3]=t,n[4]=i,n[5]=c,n[6]=v,n[7]=s,n}var I0=er;function T0(n,r){return n[0]=r[4],n[1]=r[5],n[2]=r[6],n[3]=r[7],n}var N0=er;function w0(n,r){return n[4]=r[0],n[5]=r[1],n[6]=r[2],n[7]=r[3],n}function _0(n,r){var e=r[4],a=r[5],t=r[6],i=r[7],c=-r[0],v=-r[1],s=-r[2],M=r[3];return n[0]=(e*M+i*c+a*s-t*v)*2,n[1]=(a*M+i*v+t*c-e*s)*2,n[2]=(t*M+i*s+e*v-a*c)*2,n}function D0(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=e[0]*.5,s=e[1]*.5,M=e[2]*.5,y=r[4],u=r[5],E=r[6],A=r[7];return n[0]=a,n[1]=t,n[2]=i,n[3]=c,n[4]=c*v+t*M-i*s+y,n[5]=c*s+i*v-a*M+u,n[6]=c*M+a*s-t*v+E,n[7]=-a*v-t*s-i*M+A,n}function V0(n,r,e){var a=-r[0],t=-r[1],i=-r[2],c=r[3],v=r[4],s=r[5],M=r[6],y=r[7],u=v*c+y*a+s*i-M*t,E=s*c+y*t+M*a-v*i,A=M*c+y*i+v*t-s*a,q=y*c-v*a-s*t-M*i;return _r(n,r,e),a=n[0],t=n[1],i=n[2],c=n[3],n[4]=u*c+q*a+E*i-A*t,n[5]=E*c+q*t+A*a-u*i,n[6]=A*c+q*i+u*t-E*a,n[7]=q*c-u*a-E*t-A*i,n}function F0(n,r,e){var a=-r[0],t=-r[1],i=-r[2],c=r[3],v=r[4],s=r[5],M=r[6],y=r[7],u=v*c+y*a+s*i-M*t,E=s*c+y*t+M*a-v*i,A=M*c+y*i+v*t-s*a,q=y*c-v*a-s*t-M*i;return Dr(n,r,e),a=n[0],t=n[1],i=n[2],c=n[3],n[4]=u*c+q*a+E*i-A*t,n[5]=E*c+q*t+A*a-u*i,n[6]=A*c+q*i+u*t-E*a,n[7]=q*c-u*a-E*t-A*i,n}function j0(n,r,e){var a=-r[0],t=-r[1],i=-r[2],c=r[3],v=r[4],s=r[5],M=r[6],y=r[7],u=v*c+y*a+s*i-M*t,E=s*c+y*t+M*a-v*i,A=M*c+y*i+v*t-s*a,q=y*c-v*a-s*t-M*i;return Vr(n,r,e),a=n[0],t=n[1],i=n[2],c=n[3],n[4]=u*c+q*a+E*i-A*t,n[5]=E*c+q*t+A*a-u*i,n[6]=A*c+q*i+u*t-E*a,n[7]=q*c-u*a-E*t-A*i,n}function Q0(n,r,e){var a=e[0],t=e[1],i=e[2],c=e[3],v=r[0],s=r[1],M=r[2],y=r[3];return n[0]=v*c+y*a+s*i-M*t,n[1]=s*c+y*t+M*a-v*i,n[2]=M*c+y*i+v*t-s*a,n[3]=y*c-v*a-s*t-M*i,v=r[4],s=r[5],M=r[6],y=r[7],n[4]=v*c+y*a+s*i-M*t,n[5]=s*c+y*t+M*a-v*i,n[6]=M*c+y*i+v*t-s*a,n[7]=y*c-v*a-s*t-M*i,n}function Z0(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=e[0],s=e[1],M=e[2],y=e[3];return n[0]=a*y+c*v+t*M-i*s,n[1]=t*y+c*s+i*v-a*M,n[2]=i*y+c*M+a*s-t*v,n[3]=c*y-a*v-t*s-i*M,v=e[4],s=e[5],M=e[6],y=e[7],n[4]=a*y+c*v+t*M-i*s,n[5]=t*y+c*s+i*v-a*M,n[6]=i*y+c*M+a*s-t*v,n[7]=c*y-a*v-t*s-i*M,n}function X0(n,r,e,a){if(Math.abs(a)<Y.EPSILON)return Br(n,r);var t=Math.hypot(e[0],e[1],e[2]);a=a*.5;var i=Math.sin(a),c=i*e[0]/t,v=i*e[1]/t,s=i*e[2]/t,M=Math.cos(a),y=r[0],u=r[1],E=r[2],A=r[3];n[0]=y*M+A*c+u*s-E*v,n[1]=u*M+A*v+E*c-y*s,n[2]=E*M+A*s+y*v-u*c,n[3]=A*M-y*c-u*v-E*s;var q=r[4],S=r[5],R=r[6],I=r[7];return n[4]=q*M+I*c+S*s-R*v,n[5]=S*M+I*v+R*c-q*s,n[6]=R*M+I*s+q*v-S*c,n[7]=I*M-q*c-S*v-R*s,n}function B0(n,r,e){return n[0]=r[0]+e[0],n[1]=r[1]+e[1],n[2]=r[2]+e[2],n[3]=r[3]+e[3],n[4]=r[4]+e[4],n[5]=r[5]+e[5],n[6]=r[6]+e[6],n[7]=r[7]+e[7],n}function Cr(n,r,e){var a=r[0],t=r[1],i=r[2],c=r[3],v=e[4],s=e[5],M=e[6],y=e[7],u=r[4],E=r[5],A=r[6],q=r[7],S=e[0],R=e[1],I=e[2],B=e[3];return n[0]=a*B+c*S+t*I-i*R,n[1]=t*B+c*R+i*S-a*I,n[2]=i*B+c*I+a*R-t*S,n[3]=c*B-a*S-t*R-i*I,n[4]=a*y+c*v+t*M-i*s+u*B+q*S+E*I-A*R,n[5]=t*y+c*s+i*v-a*M+E*B+q*R+A*S-u*I,n[6]=i*y+c*M+a*s-t*v+A*B+q*I+u*R-E*S,n[7]=c*y-a*v-t*s-i*M+q*B-u*S-E*R-A*I,n}var C0=Cr;function U0(n,r,e){return n[0]=r[0]*e,n[1]=r[1]*e,n[2]=r[2]*e,n[3]=r[3]*e,n[4]=r[4]*e,n[5]=r[5]*e,n[6]=r[6]*e,n[7]=r[7]*e,n}var Ur=ar;function W0(n,r,e,a){var t=1-a;return Ur(r,e)<0&&(a=-a),n[0]=r[0]*t+e[0]*a,n[1]=r[1]*t+e[1]*a,n[2]=r[2]*t+e[2]*a,n[3]=r[3]*t+e[3]*a,n[4]=r[4]*t+e[4]*a,n[5]=r[5]*t+e[5]*a,n[6]=r[6]*t+e[6]*a,n[7]=r[7]*t+e[7]*a,n}function K0(n,r){var e=on(r);return n[0]=-r[0]/e,n[1]=-r[1]/e,n[2]=-r[2]/e,n[3]=r[3]/e,n[4]=-r[4]/e,n[5]=-r[5]/e,n[6]=-r[6]/e,n[7]=r[7]/e,n}function G0(n,r){return n[0]=-r[0],n[1]=-r[1],n[2]=-r[2],n[3]=r[3],n[4]=-r[4],n[5]=-r[5],n[6]=-r[6],n[7]=r[7],n}var Wr=tr,H0=Wr,on=ir,J0=on;function $0(n,r){var e=on(r);if(e>0){e=Math.sqrt(e);var a=r[0]/e,t=r[1]/e,i=r[2]/e,c=r[3]/e,v=r[4],s=r[5],M=r[6],y=r[7],u=a*v+t*s+i*M+c*y;n[0]=a,n[1]=t,n[2]=i,n[3]=c,n[4]=(v-a*u)/e,n[5]=(s-t*u)/e,n[6]=(M-i*u)/e,n[7]=(y-c*u)/e}return n}function b0(n){return"quat2("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+", "+n[6]+", "+n[7]+")"}function k0(n,r){return n[0]===r[0]&&n[1]===r[1]&&n[2]===r[2]&&n[3]===r[3]&&n[4]===r[4]&&n[5]===r[5]&&n[6]===r[6]&&n[7]===r[7]}function o0(n,r){var e=n[0],a=n[1],t=n[2],i=n[3],c=n[4],v=n[5],s=n[6],M=n[7],y=r[0],u=r[1],E=r[2],A=r[3],q=r[4],S=r[5],R=r[6],I=r[7];return Math.abs(e-y)<=Y.EPSILON*Math.max(1,Math.abs(e),Math.abs(y))&&Math.abs(a-u)<=Y.EPSILON*Math.max(1,Math.abs(a),Math.abs(u))&&Math.abs(t-E)<=Y.EPSILON*Math.max(1,Math.abs(t),Math.abs(E))&&Math.abs(i-A)<=Y.EPSILON*Math.max(1,Math.abs(i),Math.abs(A))&&Math.abs(c-q)<=Y.EPSILON*Math.max(1,Math.abs(c),Math.abs(q))&&Math.abs(v-S)<=Y.EPSILON*Math.max(1,Math.abs(v),Math.abs(S))&&Math.abs(s-R)<=Y.EPSILON*Math.max(1,Math.abs(s),Math.abs(R))&&Math.abs(M-I)<=Y.EPSILON*Math.max(1,Math.abs(M),Math.abs(I))}var ne=K(69341)},96661:function($n,mn,K){K.r(mn),K.d(mn,{add:function(){return Qn},adjoint:function(){return Yn},clone:function(){return dn},copy:function(){return un},create:function(){return yn},determinant:function(){return Tn},equals:function(){return Cn},exactEquals:function(){return Bn},frob:function(){return In},fromMat2d:function(){return On},fromMat4:function(){return xn},fromQuat:function(){return Sn},fromRotation:function(){return qn},fromScaling:function(){return pn},fromTranslation:function(){return Dn},fromValues:function(){return An},identity:function(){return fn},invert:function(){return Pn},mul:function(){return Un},multiply:function(){return Ln},multiplyScalar:function(){return Zn},multiplyScalarAndAdd:function(){return Xn},normalFromMat4:function(){return Vn},projection:function(){return Fn},rotate:function(){return wn},scale:function(){return _n},set:function(){return Y},str:function(){return jn},sub:function(){return Wn},subtract:function(){return gn},translate:function(){return Nn},transpose:function(){return hn}});var C=K(34249);function yn(){var f=new C.ARRAY_TYPE(9);return C.ARRAY_TYPE!=Float32Array&&(f[1]=0,f[2]=0,f[3]=0,f[5]=0,f[6]=0,f[7]=0),f[0]=1,f[4]=1,f[8]=1,f}function xn(f,l){return f[0]=l[0],f[1]=l[1],f[2]=l[2],f[3]=l[4],f[4]=l[5],f[5]=l[6],f[6]=l[8],f[7]=l[9],f[8]=l[10],f}function dn(f){var l=new C.ARRAY_TYPE(9);return l[0]=f[0],l[1]=f[1],l[2]=f[2],l[3]=f[3],l[4]=f[4],l[5]=f[5],l[6]=f[6],l[7]=f[7],l[8]=f[8],l}function un(f,l){return f[0]=l[0],f[1]=l[1],f[2]=l[2],f[3]=l[3],f[4]=l[4],f[5]=l[5],f[6]=l[6],f[7]=l[7],f[8]=l[8],f}function An(f,l,O,T,N,_,D,j,Q){var w=new C.ARRAY_TYPE(9);return w[0]=f,w[1]=l,w[2]=O,w[3]=T,w[4]=N,w[5]=_,w[6]=D,w[7]=j,w[8]=Q,w}function Y(f,l,O,T,N,_,D,j,Q,w){return f[0]=l,f[1]=O,f[2]=T,f[3]=N,f[4]=_,f[5]=D,f[6]=j,f[7]=Q,f[8]=w,f}function fn(f){return f[0]=1,f[1]=0,f[2]=0,f[3]=0,f[4]=1,f[5]=0,f[6]=0,f[7]=0,f[8]=1,f}function hn(f,l){if(f===l){var O=l[1],T=l[2],N=l[5];f[1]=l[3],f[2]=l[6],f[3]=O,f[5]=l[7],f[6]=T,f[7]=N}else f[0]=l[0],f[1]=l[3],f[2]=l[6],f[3]=l[1],f[4]=l[4],f[5]=l[7],f[6]=l[2],f[7]=l[5],f[8]=l[8];return f}function Pn(f,l){var O=l[0],T=l[1],N=l[2],_=l[3],D=l[4],j=l[5],Q=l[6],w=l[7],G=l[8],$=G*D-j*w,H=-G*_+j*Q,d=w*_-D*Q,z=O*$+T*H+N*d;return z?(z=1/z,f[0]=$*z,f[1]=(-G*T+N*w)*z,f[2]=(j*T-N*D)*z,f[3]=H*z,f[4]=(G*O-N*Q)*z,f[5]=(-j*O+N*_)*z,f[6]=d*z,f[7]=(-w*O+T*Q)*z,f[8]=(D*O-T*_)*z,f):null}function Yn(f,l){var O=l[0],T=l[1],N=l[2],_=l[3],D=l[4],j=l[5],Q=l[6],w=l[7],G=l[8];return f[0]=D*G-j*w,f[1]=N*w-T*G,f[2]=T*j-N*D,f[3]=j*Q-_*G,f[4]=O*G-N*Q,f[5]=N*_-O*j,f[6]=_*w-D*Q,f[7]=T*Q-O*w,f[8]=O*D-T*_,f}function Tn(f){var l=f[0],O=f[1],T=f[2],N=f[3],_=f[4],D=f[5],j=f[6],Q=f[7],w=f[8];return l*(w*_-D*Q)+O*(-w*N+D*j)+T*(Q*N-_*j)}function Ln(f,l,O){var T=l[0],N=l[1],_=l[2],D=l[3],j=l[4],Q=l[5],w=l[6],G=l[7],$=l[8],H=O[0],d=O[1],z=O[2],P=O[3],h=O[4],m=O[5],x=O[6],p=O[7],g=O[8];return f[0]=H*T+d*D+z*w,f[1]=H*N+d*j+z*G,f[2]=H*_+d*Q+z*$,f[3]=P*T+h*D+m*w,f[4]=P*N+h*j+m*G,f[5]=P*_+h*Q+m*$,f[6]=x*T+p*D+g*w,f[7]=x*N+p*j+g*G,f[8]=x*_+p*Q+g*$,f}function Nn(f,l,O){var T=l[0],N=l[1],_=l[2],D=l[3],j=l[4],Q=l[5],w=l[6],G=l[7],$=l[8],H=O[0],d=O[1];return f[0]=T,f[1]=N,f[2]=_,f[3]=D,f[4]=j,f[5]=Q,f[6]=H*T+d*D+w,f[7]=H*N+d*j+G,f[8]=H*_+d*Q+$,f}function wn(f,l,O){var T=l[0],N=l[1],_=l[2],D=l[3],j=l[4],Q=l[5],w=l[6],G=l[7],$=l[8],H=Math.sin(O),d=Math.cos(O);return f[0]=d*T+H*D,f[1]=d*N+H*j,f[2]=d*_+H*Q,f[3]=d*D-H*T,f[4]=d*j-H*N,f[5]=d*Q-H*_,f[6]=w,f[7]=G,f[8]=$,f}function _n(f,l,O){var T=O[0],N=O[1];return f[0]=T*l[0],f[1]=T*l[1],f[2]=T*l[2],f[3]=N*l[3],f[4]=N*l[4],f[5]=N*l[5],f[6]=l[6],f[7]=l[7],f[8]=l[8],f}function Dn(f,l){return f[0]=1,f[1]=0,f[2]=0,f[3]=0,f[4]=1,f[5]=0,f[6]=l[0],f[7]=l[1],f[8]=1,f}function qn(f,l){var O=Math.sin(l),T=Math.cos(l);return f[0]=T,f[1]=O,f[2]=0,f[3]=-O,f[4]=T,f[5]=0,f[6]=0,f[7]=0,f[8]=1,f}function pn(f,l){return f[0]=l[0],f[1]=0,f[2]=0,f[3]=0,f[4]=l[1],f[5]=0,f[6]=0,f[7]=0,f[8]=1,f}function On(f,l){return f[0]=l[0],f[1]=l[1],f[2]=0,f[3]=l[2],f[4]=l[3],f[5]=0,f[6]=l[4],f[7]=l[5],f[8]=1,f}function Sn(f,l){var O=l[0],T=l[1],N=l[2],_=l[3],D=O+O,j=T+T,Q=N+N,w=O*D,G=T*D,$=T*j,H=N*D,d=N*j,z=N*Q,P=_*D,h=_*j,m=_*Q;return f[0]=1-$-z,f[3]=G-m,f[6]=H+h,f[1]=G+m,f[4]=1-w-z,f[7]=d-P,f[2]=H-h,f[5]=d+P,f[8]=1-w-$,f}function Vn(f,l){var O=l[0],T=l[1],N=l[2],_=l[3],D=l[4],j=l[5],Q=l[6],w=l[7],G=l[8],$=l[9],H=l[10],d=l[11],z=l[12],P=l[13],h=l[14],m=l[15],x=O*j-T*D,p=O*Q-N*D,g=O*w-_*D,L=T*Q-N*j,U=T*w-_*j,W=N*w-_*Q,b=G*P-$*z,en=G*h-H*z,k=G*m-d*z,Mn=$*h-H*P,ln=$*m-d*P,En=H*m-d*h,vn=x*En-p*ln+g*Mn+L*k-U*en+W*b;return vn?(vn=1/vn,f[0]=(j*En-Q*ln+w*Mn)*vn,f[1]=(Q*k-D*En-w*en)*vn,f[2]=(D*ln-j*k+w*b)*vn,f[3]=(N*ln-T*En-_*Mn)*vn,f[4]=(O*En-N*k+_*en)*vn,f[5]=(T*k-O*ln-_*b)*vn,f[6]=(P*W-h*U+m*L)*vn,f[7]=(h*g-z*W-m*p)*vn,f[8]=(z*U-P*g+m*x)*vn,f):null}function Fn(f,l,O){return f[0]=2/l,f[1]=0,f[2]=0,f[3]=0,f[4]=-2/O,f[5]=0,f[6]=-1,f[7]=1,f[8]=1,f}function jn(f){return"mat3("+f[0]+", "+f[1]+", "+f[2]+", "+f[3]+", "+f[4]+", "+f[5]+", "+f[6]+", "+f[7]+", "+f[8]+")"}function In(f){return Math.hypot(f[0],f[1],f[2],f[3],f[4],f[5],f[6],f[7],f[8])}function Qn(f,l,O){return f[0]=l[0]+O[0],f[1]=l[1]+O[1],f[2]=l[2]+O[2],f[3]=l[3]+O[3],f[4]=l[4]+O[4],f[5]=l[5]+O[5],f[6]=l[6]+O[6],f[7]=l[7]+O[7],f[8]=l[8]+O[8],f}function gn(f,l,O){return f[0]=l[0]-O[0],f[1]=l[1]-O[1],f[2]=l[2]-O[2],f[3]=l[3]-O[3],f[4]=l[4]-O[4],f[5]=l[5]-O[5],f[6]=l[6]-O[6],f[7]=l[7]-O[7],f[8]=l[8]-O[8],f}function Zn(f,l,O){return f[0]=l[0]*O,f[1]=l[1]*O,f[2]=l[2]*O,f[3]=l[3]*O,f[4]=l[4]*O,f[5]=l[5]*O,f[6]=l[6]*O,f[7]=l[7]*O,f[8]=l[8]*O,f}function Xn(f,l,O,T){return f[0]=l[0]+O[0]*T,f[1]=l[1]+O[1]*T,f[2]=l[2]+O[2]*T,f[3]=l[3]+O[3]*T,f[4]=l[4]+O[4]*T,f[5]=l[5]+O[5]*T,f[6]=l[6]+O[6]*T,f[7]=l[7]+O[7]*T,f[8]=l[8]+O[8]*T,f}function Bn(f,l){return f[0]===l[0]&&f[1]===l[1]&&f[2]===l[2]&&f[3]===l[3]&&f[4]===l[4]&&f[5]===l[5]&&f[6]===l[6]&&f[7]===l[7]&&f[8]===l[8]}function Cn(f,l){var O=f[0],T=f[1],N=f[2],_=f[3],D=f[4],j=f[5],Q=f[6],w=f[7],G=f[8],$=l[0],H=l[1],d=l[2],z=l[3],P=l[4],h=l[5],m=l[6],x=l[7],p=l[8];return Math.abs(O-$)<=C.EPSILON*Math.max(1,Math.abs(O),Math.abs($))&&Math.abs(T-H)<=C.EPSILON*Math.max(1,Math.abs(T),Math.abs(H))&&Math.abs(N-d)<=C.EPSILON*Math.max(1,Math.abs(N),Math.abs(d))&&Math.abs(_-z)<=C.EPSILON*Math.max(1,Math.abs(_),Math.abs(z))&&Math.abs(D-P)<=C.EPSILON*Math.max(1,Math.abs(D),Math.abs(P))&&Math.abs(j-h)<=C.EPSILON*Math.max(1,Math.abs(j),Math.abs(h))&&Math.abs(Q-m)<=C.EPSILON*Math.max(1,Math.abs(Q),Math.abs(m))&&Math.abs(w-x)<=C.EPSILON*Math.max(1,Math.abs(w),Math.abs(x))&&Math.abs(G-p)<=C.EPSILON*Math.max(1,Math.abs(G),Math.abs(p))}var Un=Ln,Wn=gn},69341:function($n,mn,K){K.r(mn),K.d(mn,{add:function(){return Y},angle:function(){return f},ceil:function(){return Yn},clone:function(){return xn},copy:function(){return un},create:function(){return yn},cross:function(){return Qn},dist:function(){return w},distance:function(){return qn},div:function(){return Q},divide:function(){return Pn},dot:function(){return In},equals:function(){return N},exactEquals:function(){return T},floor:function(){return Tn},forEach:function(){return H},fromValues:function(){return dn},inverse:function(){return Fn},len:function(){return _},length:function(){return On},lerp:function(){return gn},max:function(){return Nn},min:function(){return Ln},mul:function(){return j},multiply:function(){return hn},negate:function(){return Vn},normalize:function(){return jn},random:function(){return Zn},rotate:function(){return Wn},round:function(){return wn},scale:function(){return _n},scaleAndAdd:function(){return Dn},set:function(){return An},sqrDist:function(){return G},sqrLen:function(){return $},squaredDistance:function(){return pn},squaredLength:function(){return Sn},str:function(){return O},sub:function(){return D},subtract:function(){return fn},transformMat2:function(){return Xn},transformMat2d:function(){return Bn},transformMat3:function(){return Cn},transformMat4:function(){return Un},zero:function(){return l}});var C=K(34249);function yn(){var d=new C.ARRAY_TYPE(2);return C.ARRAY_TYPE!=Float32Array&&(d[0]=0,d[1]=0),d}function xn(d){var z=new C.ARRAY_TYPE(2);return z[0]=d[0],z[1]=d[1],z}function dn(d,z){var P=new C.ARRAY_TYPE(2);return P[0]=d,P[1]=z,P}function un(d,z){return d[0]=z[0],d[1]=z[1],d}function An(d,z,P){return d[0]=z,d[1]=P,d}function Y(d,z,P){return d[0]=z[0]+P[0],d[1]=z[1]+P[1],d}function fn(d,z,P){return d[0]=z[0]-P[0],d[1]=z[1]-P[1],d}function hn(d,z,P){return d[0]=z[0]*P[0],d[1]=z[1]*P[1],d}function Pn(d,z,P){return d[0]=z[0]/P[0],d[1]=z[1]/P[1],d}function Yn(d,z){return d[0]=Math.ceil(z[0]),d[1]=Math.ceil(z[1]),d}function Tn(d,z){return d[0]=Math.floor(z[0]),d[1]=Math.floor(z[1]),d}function Ln(d,z,P){return d[0]=Math.min(z[0],P[0]),d[1]=Math.min(z[1],P[1]),d}function Nn(d,z,P){return d[0]=Math.max(z[0],P[0]),d[1]=Math.max(z[1],P[1]),d}function wn(d,z){return d[0]=Math.round(z[0]),d[1]=Math.round(z[1]),d}function _n(d,z,P){return d[0]=z[0]*P,d[1]=z[1]*P,d}function Dn(d,z,P,h){return d[0]=z[0]+P[0]*h,d[1]=z[1]+P[1]*h,d}function qn(d,z){var P=z[0]-d[0],h=z[1]-d[1];return Math.hypot(P,h)}function pn(d,z){var P=z[0]-d[0],h=z[1]-d[1];return P*P+h*h}function On(d){var z=d[0],P=d[1];return Math.hypot(z,P)}function Sn(d){var z=d[0],P=d[1];return z*z+P*P}function Vn(d,z){return d[0]=-z[0],d[1]=-z[1],d}function Fn(d,z){return d[0]=1/z[0],d[1]=1/z[1],d}function jn(d,z){var P=z[0],h=z[1],m=P*P+h*h;return m>0&&(m=1/Math.sqrt(m)),d[0]=z[0]*m,d[1]=z[1]*m,d}function In(d,z){return d[0]*z[0]+d[1]*z[1]}function Qn(d,z,P){var h=z[0]*P[1]-z[1]*P[0];return d[0]=d[1]=0,d[2]=h,d}function gn(d,z,P,h){var m=z[0],x=z[1];return d[0]=m+h*(P[0]-m),d[1]=x+h*(P[1]-x),d}function Zn(d,z){z=z||1;var P=C.RANDOM()*2*Math.PI;return d[0]=Math.cos(P)*z,d[1]=Math.sin(P)*z,d}function Xn(d,z,P){var h=z[0],m=z[1];return d[0]=P[0]*h+P[2]*m,d[1]=P[1]*h+P[3]*m,d}function Bn(d,z,P){var h=z[0],m=z[1];return d[0]=P[0]*h+P[2]*m+P[4],d[1]=P[1]*h+P[3]*m+P[5],d}function Cn(d,z,P){var h=z[0],m=z[1];return d[0]=P[0]*h+P[3]*m+P[6],d[1]=P[1]*h+P[4]*m+P[7],d}function Un(d,z,P){var h=z[0],m=z[1];return d[0]=P[0]*h+P[4]*m+P[12],d[1]=P[1]*h+P[5]*m+P[13],d}function Wn(d,z,P,h){var m=z[0]-P[0],x=z[1]-P[1],p=Math.sin(h),g=Math.cos(h);return d[0]=m*g-x*p+P[0],d[1]=m*p+x*g+P[1],d}function f(d,z){var P=d[0],h=d[1],m=z[0],x=z[1],p=Math.sqrt(P*P+h*h)*Math.sqrt(m*m+x*x),g=p&&(P*m+h*x)/p;return Math.acos(Math.min(Math.max(g,-1),1))}function l(d){return d[0]=0,d[1]=0,d}function O(d){return"vec2("+d[0]+", "+d[1]+")"}function T(d,z){return d[0]===z[0]&&d[1]===z[1]}function N(d,z){var P=d[0],h=d[1],m=z[0],x=z[1];return Math.abs(P-m)<=C.EPSILON*Math.max(1,Math.abs(P),Math.abs(m))&&Math.abs(h-x)<=C.EPSILON*Math.max(1,Math.abs(h),Math.abs(x))}var _=On,D=fn,j=hn,Q=Pn,w=qn,G=pn,$=Sn,H=function(){var d=yn();return function(z,P,h,m,x,p){var g,L;for(P||(P=2),h||(h=0),m?L=Math.min(m*P+h,z.length):L=z.length,g=h;g<L;g+=P)d[0]=z[g],d[1]=z[g+1],x(d,d,p),z[g]=d[0],z[g+1]=d[1];return z}}()},62329:function($n,mn,K){K.r(mn),K.d(mn,{add:function(){return fn},angle:function(){return T},bezier:function(){return Xn},ceil:function(){return Tn},clone:function(){return xn},copy:function(){return An},create:function(){return yn},cross:function(){return Qn},dist:function(){return $},distance:function(){return pn},div:function(){return G},divide:function(){return Yn},dot:function(){return In},equals:function(){return j},exactEquals:function(){return D},floor:function(){return Ln},forEach:function(){return P},fromValues:function(){return un},hermite:function(){return Zn},inverse:function(){return Fn},len:function(){return d},length:function(){return dn},lerp:function(){return gn},max:function(){return wn},min:function(){return Nn},mul:function(){return w},multiply:function(){return Pn},negate:function(){return Vn},normalize:function(){return jn},random:function(){return Bn},rotateX:function(){return f},rotateY:function(){return l},rotateZ:function(){return O},round:function(){return _n},scale:function(){return Dn},scaleAndAdd:function(){return qn},set:function(){return Y},sqrDist:function(){return H},sqrLen:function(){return z},squaredDistance:function(){return On},squaredLength:function(){return Sn},str:function(){return _},sub:function(){return Q},subtract:function(){return hn},transformMat3:function(){return Un},transformMat4:function(){return Cn},transformQuat:function(){return Wn},zero:function(){return N}});var C=K(34249);function yn(){var h=new C.ARRAY_TYPE(3);return C.ARRAY_TYPE!=Float32Array&&(h[0]=0,h[1]=0,h[2]=0),h}function xn(h){var m=new C.ARRAY_TYPE(3);return m[0]=h[0],m[1]=h[1],m[2]=h[2],m}function dn(h){var m=h[0],x=h[1],p=h[2];return Math.hypot(m,x,p)}function un(h,m,x){var p=new C.ARRAY_TYPE(3);return p[0]=h,p[1]=m,p[2]=x,p}function An(h,m){return h[0]=m[0],h[1]=m[1],h[2]=m[2],h}function Y(h,m,x,p){return h[0]=m,h[1]=x,h[2]=p,h}function fn(h,m,x){return h[0]=m[0]+x[0],h[1]=m[1]+x[1],h[2]=m[2]+x[2],h}function hn(h,m,x){return h[0]=m[0]-x[0],h[1]=m[1]-x[1],h[2]=m[2]-x[2],h}function Pn(h,m,x){return h[0]=m[0]*x[0],h[1]=m[1]*x[1],h[2]=m[2]*x[2],h}function Yn(h,m,x){return h[0]=m[0]/x[0],h[1]=m[1]/x[1],h[2]=m[2]/x[2],h}function Tn(h,m){return h[0]=Math.ceil(m[0]),h[1]=Math.ceil(m[1]),h[2]=Math.ceil(m[2]),h}function Ln(h,m){return h[0]=Math.floor(m[0]),h[1]=Math.floor(m[1]),h[2]=Math.floor(m[2]),h}function Nn(h,m,x){return h[0]=Math.min(m[0],x[0]),h[1]=Math.min(m[1],x[1]),h[2]=Math.min(m[2],x[2]),h}function wn(h,m,x){return h[0]=Math.max(m[0],x[0]),h[1]=Math.max(m[1],x[1]),h[2]=Math.max(m[2],x[2]),h}function _n(h,m){return h[0]=Math.round(m[0]),h[1]=Math.round(m[1]),h[2]=Math.round(m[2]),h}function Dn(h,m,x){return h[0]=m[0]*x,h[1]=m[1]*x,h[2]=m[2]*x,h}function qn(h,m,x,p){return h[0]=m[0]+x[0]*p,h[1]=m[1]+x[1]*p,h[2]=m[2]+x[2]*p,h}function pn(h,m){var x=m[0]-h[0],p=m[1]-h[1],g=m[2]-h[2];return Math.hypot(x,p,g)}function On(h,m){var x=m[0]-h[0],p=m[1]-h[1],g=m[2]-h[2];return x*x+p*p+g*g}function Sn(h){var m=h[0],x=h[1],p=h[2];return m*m+x*x+p*p}function Vn(h,m){return h[0]=-m[0],h[1]=-m[1],h[2]=-m[2],h}function Fn(h,m){return h[0]=1/m[0],h[1]=1/m[1],h[2]=1/m[2],h}function jn(h,m){var x=m[0],p=m[1],g=m[2],L=x*x+p*p+g*g;return L>0&&(L=1/Math.sqrt(L)),h[0]=m[0]*L,h[1]=m[1]*L,h[2]=m[2]*L,h}function In(h,m){return h[0]*m[0]+h[1]*m[1]+h[2]*m[2]}function Qn(h,m,x){var p=m[0],g=m[1],L=m[2],U=x[0],W=x[1],b=x[2];return h[0]=g*b-L*W,h[1]=L*U-p*b,h[2]=p*W-g*U,h}function gn(h,m,x,p){var g=m[0],L=m[1],U=m[2];return h[0]=g+p*(x[0]-g),h[1]=L+p*(x[1]-L),h[2]=U+p*(x[2]-U),h}function Zn(h,m,x,p,g,L){var U=L*L,W=U*(2*L-3)+1,b=U*(L-2)+L,en=U*(L-1),k=U*(3-2*L);return h[0]=m[0]*W+x[0]*b+p[0]*en+g[0]*k,h[1]=m[1]*W+x[1]*b+p[1]*en+g[1]*k,h[2]=m[2]*W+x[2]*b+p[2]*en+g[2]*k,h}function Xn(h,m,x,p,g,L){var U=1-L,W=U*U,b=L*L,en=W*U,k=3*L*W,Mn=3*b*U,ln=b*L;return h[0]=m[0]*en+x[0]*k+p[0]*Mn+g[0]*ln,h[1]=m[1]*en+x[1]*k+p[1]*Mn+g[1]*ln,h[2]=m[2]*en+x[2]*k+p[2]*Mn+g[2]*ln,h}function Bn(h,m){m=m||1;var x=C.RANDOM()*2*Math.PI,p=C.RANDOM()*2-1,g=Math.sqrt(1-p*p)*m;return h[0]=Math.cos(x)*g,h[1]=Math.sin(x)*g,h[2]=p*m,h}function Cn(h,m,x){var p=m[0],g=m[1],L=m[2],U=x[3]*p+x[7]*g+x[11]*L+x[15];return U=U||1,h[0]=(x[0]*p+x[4]*g+x[8]*L+x[12])/U,h[1]=(x[1]*p+x[5]*g+x[9]*L+x[13])/U,h[2]=(x[2]*p+x[6]*g+x[10]*L+x[14])/U,h}function Un(h,m,x){var p=m[0],g=m[1],L=m[2];return h[0]=p*x[0]+g*x[3]+L*x[6],h[1]=p*x[1]+g*x[4]+L*x[7],h[2]=p*x[2]+g*x[5]+L*x[8],h}function Wn(h,m,x){var p=x[0],g=x[1],L=x[2],U=x[3],W=m[0],b=m[1],en=m[2],k=g*en-L*b,Mn=L*W-p*en,ln=p*b-g*W,En=g*ln-L*Mn,vn=L*k-p*ln,Kn=p*Mn-g*k,Gn=U*2;return k*=Gn,Mn*=Gn,ln*=Gn,En*=2,vn*=2,Kn*=2,h[0]=W+k+En,h[1]=b+Mn+vn,h[2]=en+ln+Kn,h}function f(h,m,x,p){var g=[],L=[];return g[0]=m[0]-x[0],g[1]=m[1]-x[1],g[2]=m[2]-x[2],L[0]=g[0],L[1]=g[1]*Math.cos(p)-g[2]*Math.sin(p),L[2]=g[1]*Math.sin(p)+g[2]*Math.cos(p),h[0]=L[0]+x[0],h[1]=L[1]+x[1],h[2]=L[2]+x[2],h}function l(h,m,x,p){var g=[],L=[];return g[0]=m[0]-x[0],g[1]=m[1]-x[1],g[2]=m[2]-x[2],L[0]=g[2]*Math.sin(p)+g[0]*Math.cos(p),L[1]=g[1],L[2]=g[2]*Math.cos(p)-g[0]*Math.sin(p),h[0]=L[0]+x[0],h[1]=L[1]+x[1],h[2]=L[2]+x[2],h}function O(h,m,x,p){var g=[],L=[];return g[0]=m[0]-x[0],g[1]=m[1]-x[1],g[2]=m[2]-x[2],L[0]=g[0]*Math.cos(p)-g[1]*Math.sin(p),L[1]=g[0]*Math.sin(p)+g[1]*Math.cos(p),L[2]=g[2],h[0]=L[0]+x[0],h[1]=L[1]+x[1],h[2]=L[2]+x[2],h}function T(h,m){var x=h[0],p=h[1],g=h[2],L=m[0],U=m[1],W=m[2],b=Math.sqrt(x*x+p*p+g*g),en=Math.sqrt(L*L+U*U+W*W),k=b*en,Mn=k&&In(h,m)/k;return Math.acos(Math.min(Math.max(Mn,-1),1))}function N(h){return h[0]=0,h[1]=0,h[2]=0,h}function _(h){return"vec3("+h[0]+", "+h[1]+", "+h[2]+")"}function D(h,m){return h[0]===m[0]&&h[1]===m[1]&&h[2]===m[2]}function j(h,m){var x=h[0],p=h[1],g=h[2],L=m[0],U=m[1],W=m[2];return Math.abs(x-L)<=C.EPSILON*Math.max(1,Math.abs(x),Math.abs(L))&&Math.abs(p-U)<=C.EPSILON*Math.max(1,Math.abs(p),Math.abs(U))&&Math.abs(g-W)<=C.EPSILON*Math.max(1,Math.abs(g),Math.abs(W))}var Q=hn,w=Pn,G=Yn,$=pn,H=On,d=dn,z=Sn,P=function(){var h=yn();return function(m,x,p,g,L,U){var W,b;for(x||(x=3),p||(p=0),g?b=Math.min(g*x+p,m.length):b=m.length,W=p;W<b;W+=x)h[0]=m[W],h[1]=m[W+1],h[2]=m[W+2],L(h,h,U),m[W]=h[0],m[W+1]=h[1],m[W+2]=h[2];return m}}()}}]);
