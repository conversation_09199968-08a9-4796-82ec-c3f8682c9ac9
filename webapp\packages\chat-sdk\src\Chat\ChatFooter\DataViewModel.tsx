import React, { useState, useEffect, useRef } from 'react';
import { Form, Button, Modal, Input, message, Tabs, Table, Checkbox, Select } from 'antd';
import { 
  queryDatabaseList, 
  queryDataSetEnum, 
  queryDomainId, 
  getAllModelByDomainId, 
  getDimensionList,
  queryMetric,
  getDataSetDetail
 } from '../../service';
import { isArrayOfValues } from '../../utils/utils';

type Props  = {
  currentAgent: any;
  onCancel: () => void;
  dataModelVisible: boolean;
};

const { TabPane } = Tabs;

const DataViewModel: React.FC<Props> = ({ 
  currentAgent, 
  onCancel, 
  dataModelVisible, 
}) => {
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  const [dataPreviewColumns, setDataPreviewColumns] = useState<any>([]);
  const [databaseList, setDatabaseList] = useState<any>([]);
  const [fieldDetailList, setFieldDetailList] = useState<any>([]);
  const [loadingPreview, setLoadingPreview] = useState(false); 
  const [loadingFieldDetail, setLoadingFieldDetail] = useState(false); 
  const [loadingDimension, setLoadingDimension] = useState(false);
  const [datasetOptions,setDatasetOptions] = useState<any>([]);
  const [activeTabKey, setActiveTabKey] = useState(currentAgent?.dataSetIds[0] || '');
  const [activeTabKey2, setActiveTabKey2] = useState('dataPreview');
  const [domainId, setDomainId] = useState<any>(null);
  const [modelOptions,setModelOptions] = useState<any>([]);
  const [ids,setIds] = useState<any>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  const fieldDetailColumns = [
    { title: '字段含义', dataIndex: 'name' },
    { title: '字段描述', dataIndex: 'description' },
    { title: '维度/指标', dataIndex: 'type', render: (text: string) => (
        <Button size="small" style={{ backgroundColor: text === 'DIMENSION' ? '#548DFF' : '#0AC448', color: '#fff' }}>
          {text === 'DIMENSION' ? '维度' : '指标'}
        </Button>
      ) },
  ];

  const dataSetArr = currentAgent?.dataSetIds || []
  
  // 维度和指标相关状态
  const [selectedItems, setSelectedItems] = useState<string[]>(['维度', '指标']);
  const [allDimensionIndicatorData, setAllDimensionIndicatorData] = useState<any[]>([]);
  const [dimensionIndicatorData, setDimensionIndicatorData] = useState<any[]>([]);
  const [dimensionIndicatorColumns] = useState<any>([
    { title: '维度/指标', dataIndex: 'typeEnum', render: (text: string) => (
        <Button size="small" style={{ backgroundColor: text === 'DIMENSION' ? '#548DFF' : '#0AC448', color: '#fff' }}>
          {text === 'DIMENSION' ? '维度' : '指标'}
        </Button>
      ) },
    { title: '所属表', dataIndex: 'modelName' },
    { title: '字段名称', dataIndex: 'name' },
    { title: '字段描述', dataIndex: 'description' },
  ]);

  const getDataSetOptions = () => {
    let params = dataSetArr?.join(',')
    return queryDataSetEnum(params).then((res) => {
      const result = res as unknown as Result<any>;
      setDatasetOptions(result.data)
    })
  }

  const getDatabaseList = async (modelId: any) => {
    setLoadingPreview(true);
    setLoadingFieldDetail(true);

    try {
      const response = await queryDatabaseList(modelId);
      const { code, data, msg } = response as unknown as Result<any>;

      if (code === 200 && Array.isArray(data?.list)) {
        setDataPreviewColumns(data.typeList)
        setDatabaseList(data.list)
        setFieldDetailList(data.metricList)
      } else {
        // message.error(msg);
      }
    } finally {
      setLoadingPreview(false);
      setLoadingFieldDetail(false);
    }
  };

  useEffect(() => {
    if(dataModelVisible) {
      getDataSetOptions().then(() => {
        if(currentAgent?.dataSetIds.length > 0)
          getDomainId(currentAgent?.dataSetIds[0])
        })
    }
  }, [dataModelVisible]);

  useEffect(() => {
    if(dataModelVisible) {
      getDatabaseList(selectedModelId)
    }
  }, [selectedModelId]);

  const handleCheckboxChange = (checkedValues: string[]) => {
    setSelectedItems(checkedValues);
    
    if (checkedValues.length === 0) {
      setDimensionIndicatorData([]);
      return;
    }
    
    const filteredData = allDimensionIndicatorData.filter(item => {
      if (checkedValues.includes('维度') && checkedValues.includes('指标')) {
        return true;
      }
      if (checkedValues.includes('维度')) {
        return item.typeEnum === 'DIMENSION';
      }
      if (checkedValues.includes('指标')) {
        return item.typeEnum === 'METRIC';
      }
      return false;
    });
    
    setDimensionIndicatorData(filteredData);
  };

  // 刷新- 表的下拉数据
  const getDomainId = async (key) => {
    const res = await queryDomainId(key) as unknown as Result<any>;
    setDomainId(res.data.domainId);
    const modelRes = await getAllModelByDomainId(res.data.domainId) as unknown as Result<any>;
    if(modelRes.code === 200 && modelRes.data.length > 0) {
      const options = modelRes.data.map(item => {
        return { value: item.id, label: item.name };
      });
      setIds(options.map(item => item.value))
      setModelOptions(options);
      // 默认选中第一个
      setSelectedModelId(options[0].value)
    }
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };
  
  const filterColumns = (columns: any[], searchText: string) => {
    if (!searchText) return columns;
    return columns.filter(column => 
      column.title && column.title.toString().toLowerCase().includes(searchText.toLowerCase())
    );
  };

  const initModelData = () => {
    setActiveTabKey('')
    setModelOptions([])
    setSelectedModelId('')
    setSearchValue('')
    setDatabaseList([])
    setFieldDetailList([])
    setDataPreviewColumns([])
    setDimensionIndicatorData([])
    setAllDimensionIndicatorData([])
    setSelectedItems(['维度', '指标'])
  }

  const queryDimensionListByIds = async (ids: number[]) => {
    const { code, data, msg } = await getDimensionList({ ids }) as unknown as Result<any>;
    const dimensionList = data?.list?.map(item => ({ ...item, typeEnum: 'DIMENSION' })) || [];
    setAllDimensionIndicatorData(prevData => [...prevData, ...dimensionList]);
    setDimensionIndicatorData(prevData => [...prevData, ...dimensionList]);
  }

  const queryMetricListByIds = async (ids: number[]) => {
    const { code, data, msg } = await queryMetric({ ids }) as unknown as Result<any>;
    const metricList = data?.list?.map(item => ({ ...item, typeEnum: 'METRIC' })) || [];
    setAllDimensionIndicatorData(prevData => [...prevData, ...metricList]);
    setDimensionIndicatorData(prevData => [...prevData, ...metricList]);
  }

  // 刷新-数据集维度和指标列表
  const getIds = async (dataSetId: any) => {
    setLoadingDimension(true)
    const { code, data, msg } = await getDataSetDetail(dataSetId) as unknown as Result<any>;
    if (code === 200) {
      const dataSetModelConfigs = data?.dataSetDetail?.dataSetModelConfigs;
      if (isArrayOfValues(dataSetModelConfigs)) {
        const allMetrics: number[] = [];
        const allDimensions: number[] = [];
        dataSetModelConfigs.forEach((item: any) => {
          const { metrics, dimensions } = item;
          allMetrics.push(...metrics);
          allDimensions.push(...dimensions);
        });
        setAllDimensionIndicatorData([])
        setDimensionIndicatorData([])
        await queryDimensionListByIds(allDimensions);
        await queryMetricListByIds(allMetrics);
      }
      setLoadingDimension(false)
      return;
    }
  }

  const extraOptions = <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10,  }}>
    <Checkbox.Group
      options={['维度', '指标']}
      value={selectedItems}
      onChange={handleCheckboxChange}
      style={{ marginRight: 10 }}
    />
  </div>

  return (
    <Modal
      key={currentAgent?.id || 'data-view-modal'}
      width={900}
      destroyOnHidden
      title="数据详情"
      open={dataModelVisible}
      onCancel={() =>{
        onCancel();
        initModelData();
      }}
      footer={null}
      styles={{ body: { minHeight: '500px' } }}
    >
      {
        currentAgent?.dataSetIds.length > 0? (
          <div>
            <Tabs 
              defaultActiveKey={currentAgent?.dataSetIds[0]}
              size="large" 
              onChange={(key) => {
                setActiveTabKey(key);
                setSelectedModelId('')
                setSearchValue('')
                getDomainId(key)
            }}>
              {datasetOptions?.map(item => (
                <TabPane tab={item.title} key={item.id}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>
                    <Select
                      style={{ minWidth: 150, textAlign: 'left', marginRight: 10 }}
                      value={selectedModelId}
                      placeholder="请选择模型，获取当前模型下指标维度信息"
                      onChange={(val) => {
                        setSelectedModelId(val)
                        getDatabaseList(val)
                      }}
                      options={modelOptions}
                    />
                    <Input.Search
                      placeholder="按字段名称搜索数据"
                      style={{ width: 220, marginRight: 10 }}
                      onSearch={handleSearch}
                      enterButton
                      allowClear
                    />
                  </div>
                  <Tabs 
                    tabBarExtraContent={activeTabKey2 === '2' ? extraOptions : null}
                    defaultActiveKey="dataPreview" 
                    type="card" 
                    size="small" 
                    tabPosition="top"
                    onChange={(key) => {
                      setActiveTabKey2(key)
                      if(key === '2') {
                        getIds(activeTabKey || currentAgent?.dataSetIds[0])
                      }
                    }}
                  >
                    <TabPane tab="数据预览" key="dataPreview">
                      <Table
                        loading={loadingPreview} 
                        columns={filterColumns(dataPreviewColumns,searchValue).map(col => ({
                          ...col,
                          maxWidth: 150,
                          ellipsis: true,
                          onCell: () => ({
                            style: {
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              textAlign: 'center'
                            }
                          }),
                          title: (
                            <div style={{ 
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              textAlign: 'center',
                            }}>
                              {col.title}
                            </div>
                          )
                        }))}
                        dataSource={databaseList}
                        bordered
                        pagination={false}
                        size='small'
                        scroll={{ x: 'max-content' }}
                      />
                      <div style={{ marginTop: 20, display: 'flex',justifyContent: 'space-between' }}>
                        <span>字段数 {dataPreviewColumns.length}</span>
                        <span>最多预览前10行数据</span>
                      </div>
                    </TabPane>
                    <TabPane tab="字段详情" key="fieldDetail">
                      <div>
                        <Table
                          loading={loadingFieldDetail}
                          columns={fieldDetailColumns.map(col => ({
                            ...col,
                            maxWidth: 150,
                            textAlign: 'center',
                            ellipsis: true,
                            onCell: () => ({
                              style: {
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                textAlign: 'center'
                              }
                            }),
                            title: (
                              <div style={{ 
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                textAlign: 'center'
                              }}>
                                {col.title}
                              </div>
                            )
                          }))}
                          dataSource={fieldDetailList}
                          bordered
                          pagination={false}
                          size='small'
                          scroll={{ x: 'max-content' }}
                        />
                        <div style={{ marginTop: 10 }}>
                          <span style={{borderRight: '1px solid #CECECE',paddingRight: '20px',marginRight: '20px'}}>字段数{fieldDetailList.length}</span>
                          <span>
                            <span style={{
                              display: 'inline-block',
                              width: '12px',
                              height: '12px',
                              backgroundColor: '#1890ff',
                              marginRight: '4px'
                            }}></span>
                            维度 {fieldDetailList.filter(item => item.type === 'DIMENSION').length}
                          </span>
                          &nbsp;&nbsp;&nbsp;
                          <span>
                            <span style={{
                              display: 'inline-block',
                              width: '12px',
                              height: '12px',
                              backgroundColor: '#52c41a',
                              marginRight: '4px'
                            }}></span>
                            指标 {fieldDetailList.filter(item => item.type === 'METRIC').length}
                          </span>
                        </div>
                      </div>
                    </TabPane>
                    <TabPane tab="数据集维度和指标" key="2">
                      <Table
                        loading={loadingDimension}
                        columns={dimensionIndicatorColumns}
                        dataSource={dimensionIndicatorData}
                        bordered
                        pagination={false}
                        size='small'
                      />
                      <div style={{ marginTop: 10 }}>
                        <span style={{borderRight: '1px solid #CECECE',paddingRight: '20px',marginRight: '20px'}}>字段数{dimensionIndicatorData.length}</span>
                        <span>
                          <span style={{
                            display: 'inline-block',
                            width: '12px',
                            height: '12px',
                            backgroundColor: '#1890ff',
                            marginRight: '4px'
                          }}></span>
                          维度 {dimensionIndicatorData.filter(item => item.typeEnum === 'DIMENSION').length}
                        </span>
                        &nbsp;&nbsp;&nbsp;
                        <span>
                          <span style={{
                            display: 'inline-block',
                            width: '12px',
                            height: '12px',
                            backgroundColor: '#52c41a',
                            marginRight: '4px'
                          }}></span>
                          指标 {dimensionIndicatorData.filter(item => item.typeEnum === 'METRIC').length}
                        </span>
                      </div>
                    </TabPane>
                  </Tabs>
                </TabPane>
              ))}
            </Tabs>
          </div>
        ): (
          <div style={{ textAlign: 'center', marginTop: 20,height: '500px',lineHeight: '500px' }}>暂无数据</div>
        )
      }
    </Modal>
  )
}

export default DataViewModel;