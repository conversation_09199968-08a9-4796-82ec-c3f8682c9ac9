(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[547],{54830:function(je){(function(Ce){je.exports=Ce(null)})(function Ce(h){"use strict";var Q=/^\0+/g,g=/[\0\r\f]/g,ne=/: */g,fe=/zoo|gra/,M=/([,: ])(transform)/g,O=/,+\s*(?![^(]*[)])/g,I=/ +\s*(?![^(]*[)])/g,V=/ *[\0] */g,j=/,\r+?/g,Ee=/([\t\r\n ])*\f?&/g,ke=/:global\(((?:[^\(\)\[\]]*|\[.*\]|\([^\(\)]*\))*)\)/g,ye=/\W+/g,Te=/@(k\w+)\s*(\S*)\s*/,X=/::(place)/g,o=/:(read-only)/g,_=/\s+(?=[{\];=:>])/g,R=/([[}=:>])\s+/g,E=/(\{[^{]+?);(?=\})/g,de=/\s{2,}/g,ie=/([^\(])(:+) */g,ue=/[svh]\w+-[tblr]{2}/,De=/([\w-]+t\()/g,Me=/\(\s*(.*)\s*\)/g,We=/([\s\S]*?);/g,Fe=/-self|flex-/g,Pe=/[^]*?(:[rp][el]a[\w-]+)[^]*/,Ue=/[ \t]+$/,Ve=/stretch|:\s*\w+\-(?:conte|avail)/,Ie=/([^-])(image-set\()/,l="-webkit-",m="-moz-",A="-ms-",F=59,i=125,S=123,B=40,P=41,k=91,Y=93,J=10,$=13,z=9,y=64,D=32,H=38,ce=45,be=95,le=42,q=44,me=58,he=39,Z=34,se=47,ge=62,Ae=43,pe=126,K=0,pr=12,Sr=11,qe=107,ar=109,tr=115,Cr=112,ir=111,mr=105,Mr=99,Ar=100,Pr=112,Le=1,Ye=1,Be=0,Oe=1,Re=1,or=1,br=0,_r=0,ur=0,lr=[],fr=[],Ke=0,vr=null,Or=-2,r=-1,e=0,t=1,n=2,s=3,c=0,p=1,w="",U="",ee="";function re(d,C,v,x,u){for(var a=0,b=0,f=0,L=0,G=0,oe=0,N=0,xe=0,dr=0,Rr=0,Je=0,ze=0,hr=0,$e=0,W=0,Ne=0,rr=0,cr=0,ae=0,Xe=v.length,sr=Xe-1,_e="",T="",te="",ve="",gr="",Ir="",Ze,Qe;W<Xe;){if(N=v.charCodeAt(W),W===sr&&b+L+f+a!==0&&(b!==0&&(N=b===se?J:se),L=f=a=0,Xe++,sr++),b+L+f+a===0){if(W===sr&&(Ne>0&&(T=T.replace(g,"")),T.trim().length>0)){switch(N){case D:case z:case F:case $:case J:break;default:T+=v.charAt(W)}N=F}if(rr===1)switch(N){case S:case i:case F:case Z:case he:case B:case P:case q:rr=0;case z:case $:case J:case D:break;default:for(rr=0,ae=W,G=N,W--,N=F;ae<Xe;)switch(v.charCodeAt(ae++)){case J:case $:case F:{++W,N=G,ae=Xe;break}case me:Ne>0&&(++W,N=G);case S:ae=Xe}}switch(N){case S:{for(T=T.trim(),G=T.charCodeAt(0),Je=1,ae=++W;W<Xe;){switch(N=v.charCodeAt(W)){case S:{Je++;break}case i:{Je--;break}case se:{switch(oe=v.charCodeAt(W+1)){case le:case se:W=kr(oe,W,sr,v)}break}case k:N++;case B:N++;case Z:case he:for(;W++<sr&&v.charCodeAt(W)!==N;);}if(Je===0)break;W++}switch(te=v.substring(ae,W),G===K&&(G=(T=T.replace(Q,"").trim()).charCodeAt(0)),G){case y:{switch(Ne>0&&(T=T.replace(g,"")),oe=T.charCodeAt(1),oe){case Ar:case ar:case tr:case ce:{Ze=C;break}default:Ze=lr}if(te=re(C,Ze,te,oe,u+1),ae=te.length,ur>0&&ae===0&&(ae=T.length),Ke>0&&(Ze=Se(lr,T,cr),Qe=er(s,te,Ze,C,Ye,Le,ae,oe,u,x),T=Ze.join(""),Qe!==void 0&&(ae=(te=Qe.trim()).length)===0&&(oe=0,te="")),ae>0)switch(oe){case tr:T=T.replace(Me,Er);case Ar:case ar:case ce:{te=T+"{"+te+"}";break}case qe:{T=T.replace(Te,"$1 $2"+(p>0?w:"")),te=T+"{"+te+"}",Re===1||Re===2&&He("@"+te,3)?te="@"+l+te+"@"+te:te="@"+te;break}default:te=T+te,x===Pr&&(te=(ve+=te,""))}else te="";break}default:te=re(C,Se(C,T,cr),te,x,u+1)}gr+=te,ze=0,rr=0,$e=0,Ne=0,cr=0,hr=0,T="",te="",N=v.charCodeAt(++W);break}case i:case F:{if(T=(Ne>0?T.replace(g,""):T).trim(),(ae=T.length)>1)switch($e===0&&(G=T.charCodeAt(0),(G===ce||G>96&&G<123)&&(ae=(T=T.replace(" ",":")).length)),Ke>0&&(Qe=er(t,T,C,d,Ye,Le,ve.length,x,u,x))!==void 0&&(ae=(T=Qe.trim()).length)===0&&(T="\0\0"),G=T.charCodeAt(0),oe=T.charCodeAt(1),G){case K:break;case y:if(oe===mr||oe===Mr){Ir+=T+v.charAt(W);break}default:{if(T.charCodeAt(ae-1)===me)break;ve+=Ge(T,G,oe,T.charCodeAt(2))}}ze=0,rr=0,$e=0,Ne=0,cr=0,T="",N=v.charCodeAt(++W);break}}}switch(N){case $:case J:{if(b+L+f+a+_r===0)switch(Rr){case P:case he:case Z:case y:case pe:case ge:case le:case Ae:case se:case ce:case me:case q:case F:case S:case i:break;default:$e>0&&(rr=1)}b===se?b=0:Oe+ze===0&&x!==qe&&T.length>0&&(Ne=1,T+="\0"),Ke*c>0&&er(e,T,C,d,Ye,Le,ve.length,x,u,x),Le=1,Ye++;break}case F:case i:if(b+L+f+a===0){Le++;break}default:{switch(Le++,_e=v.charAt(W),N){case z:case D:{if(L+a+b===0)switch(xe){case q:case me:case z:case D:{_e="";break}default:N!==D&&(_e=" ")}break}case K:{_e="\\0";break}case pr:{_e="\\f";break}case Sr:{_e="\\v";break}case H:{L+b+a===0&&Oe>0&&(cr=1,Ne=1,_e="\f"+_e);break}case 108:{if(L+b+a+Be===0&&$e>0)switch(W-$e){case 2:xe===Cr&&v.charCodeAt(W-3)===me&&(Be=xe);case 8:dr===ir&&(Be=dr)}break}case me:{L+b+a===0&&($e=W);break}case q:{b+f+L+a===0&&(Ne=1,_e+="\r");break}case Z:case he:{b===0&&(L=L===N?0:L===0?N:L);break}case k:{L+b+f===0&&a++;break}case Y:{L+b+f===0&&a--;break}case P:{L+b+a===0&&f--;break}case B:{if(L+b+a===0){if(ze===0)switch(xe*2+dr*3){case 533:break;default:Je=0,ze=1}f++}break}case y:{b+f+L+a+$e+hr===0&&(hr=1);break}case le:case se:{if(L+a+f>0)break;switch(b){case 0:{switch(N*2+v.charCodeAt(W+1)*3){case 235:{b=se;break}case 220:{ae=W,b=le;break}}break}case le:N===se&&xe===le&&ae+2!==W&&(v.charCodeAt(ae+2)===33&&(ve+=v.substring(ae,W+1)),_e="",b=0)}}}if(b===0){if(Oe+L+a+hr===0&&x!==qe&&N!==F)switch(N){case q:case pe:case ge:case Ae:case P:case B:{if(ze===0){switch(xe){case z:case D:case J:case $:{_e=_e+"\0";break}default:_e="\0"+_e+(N===q?"":"\0")}Ne=1}else switch(N){case B:{$e+7===W&&xe===108&&($e=0),ze=++Je;break}case P:{(ze=--Je)===0&&(Ne=1,_e+="\0");break}}break}case z:case D:switch(xe){case K:case S:case i:case F:case q:case pr:case z:case D:case J:case $:break;default:ze===0&&(Ne=1,_e+="\0")}}T+=_e,N!==D&&N!==z&&(Rr=N)}}}dr=xe,xe=N,W++}if(ae=ve.length,ur>0&&ae===0&&gr.length===0&&C[0].length!==0&&(x!==ar||C.length===1&&(Oe>0?U:ee)===C[0])&&(ae=C.join(",").length+2),ae>0){if(Ze=Oe===0&&x!==qe?Nr(C):C,Ke>0&&(Qe=er(n,ve,Ze,d,Ye,Le,ae,x,u,x),Qe!==void 0&&(ve=Qe).length===0))return Ir+ve+gr;if(ve=Ze.join(",")+"{"+ve+"}",Re*Be!==0){switch(Re===2&&!He(ve,2)&&(Be=0),Be){case ir:{ve=ve.replace(o,":"+m+"$1")+ve;break}case Cr:{ve=ve.replace(X,"::"+l+"input-$1")+ve.replace(X,"::"+m+"$1")+ve.replace(X,":"+A+"input-$1")+ve;break}}Be=0}}return Ir+ve+gr}function Se(d,C,v){var x=C.trim().split(j),u=x,a=x.length,b=d.length;switch(b){case 0:case 1:{for(var f=0,L=b===0?"":d[0]+" ";f<a;++f)u[f]=we(L,u[f],v,b).trim();break}default:for(var f=0,G=0,u=[];f<a;++f)for(var oe=0;oe<b;++oe)u[G++]=we(d[oe]+" ",x[f],v,b).trim()}return u}function we(d,C,v,x){var u=C,a=u.charCodeAt(0);switch(a<33&&(a=(u=u.trim()).charCodeAt(0)),a){case H:{switch(Oe+x){case 0:case 1:if(d.trim().length===0)break;default:return u.replace(Ee,"$1"+d.trim())}break}case me:switch(u.charCodeAt(1)){case 103:{if(or>0&&Oe>0)return u.replace(ke,"$1").replace(Ee,"$1"+ee);break}default:return d.trim()+u.replace(Ee,"$1"+d.trim())}default:if(v*Oe>0&&u.indexOf("\f")>0)return u.replace(Ee,(d.charCodeAt(0)===me?"":"$1")+d.trim())}return d+u}function Ge(d,C,v,x){var u=0,a=d+";",b=C*2+v*3+x*4,f;if(b===944)return xr(a);if(Re===0||Re===2&&!He(a,1))return a;switch(b){case 1015:return a.charCodeAt(10)===97?l+a+a:a;case 951:return a.charCodeAt(3)===116?l+a+a:a;case 963:return a.charCodeAt(5)===110?l+a+a:a;case 1009:if(a.charCodeAt(4)!==100)break;case 969:case 942:return l+a+a;case 978:return l+a+m+a+a;case 1019:case 983:return l+a+m+a+A+a+a;case 883:return a.charCodeAt(8)===ce?l+a+a:a.indexOf("image-set(",11)>0?a.replace(Ie,"$1"+l+"$2")+a:a;case 932:{if(a.charCodeAt(4)===ce)switch(a.charCodeAt(5)){case 103:return l+"box-"+a.replace("-grow","")+l+a+A+a.replace("grow","positive")+a;case 115:return l+a+A+a.replace("shrink","negative")+a;case 98:return l+a+A+a.replace("basis","preferred-size")+a}return l+a+A+a+a}case 964:return l+a+A+"flex-"+a+a;case 1023:{if(a.charCodeAt(8)!==99)break;return f=a.substring(a.indexOf(":",15)).replace("flex-","").replace("space-between","justify"),l+"box-pack"+f+l+a+A+"flex-pack"+f+a}case 1005:return fe.test(a)?a.replace(ne,":"+l)+a.replace(ne,":"+m)+a:a;case 1e3:{switch(f=a.substring(13).trim(),u=f.indexOf("-")+1,f.charCodeAt(0)+f.charCodeAt(u)){case 226:{f=a.replace(ue,"tb");break}case 232:{f=a.replace(ue,"tb-rl");break}case 220:{f=a.replace(ue,"lr");break}default:return a}return l+a+A+f+a}case 1017:if(a.indexOf("sticky",9)===-1)return a;case 975:{switch(u=(a=d).length-10,f=(a.charCodeAt(u)===33?a.substring(0,u):a).substring(d.indexOf(":",7)+1).trim(),b=f.charCodeAt(0)+(f.charCodeAt(7)|0)){case 203:if(f.charCodeAt(8)<111)break;case 115:{a=a.replace(f,l+f)+";"+a;break}case 207:case 102:a=a.replace(f,l+(b>102?"inline-":"")+"box")+";"+a.replace(f,l+f)+";"+a.replace(f,A+f+"box")+";"+a}return a+";"}case 938:{if(a.charCodeAt(5)===ce)switch(a.charCodeAt(6)){case 105:return f=a.replace("-items",""),l+a+l+"box-"+f+A+"flex-"+f+a;case 115:return l+a+A+"flex-item-"+a.replace(Fe,"")+a;default:return l+a+A+"flex-line-pack"+a.replace("align-content","").replace(Fe,"")+a}break}case 973:case 989:if(a.charCodeAt(3)!==ce||a.charCodeAt(4)===122)break;case 931:case 953:{if(Ve.test(d)===!0)return(f=d.substring(d.indexOf(":")+1)).charCodeAt(0)===115?Ge(d.replace("stretch","fill-available"),C,v,x).replace(":fill-available",":stretch"):a.replace(f,l+f)+a.replace(f,m+f.replace("fill-",""))+a;break}case 962:{if(a=l+a+(a.charCodeAt(5)===102?A+a:"")+a,v+x===211&&a.charCodeAt(13)===105&&a.indexOf("transform",10)>0)return a.substring(0,a.indexOf(";",27)+1).replace(M,"$1"+l+"$2")+a;break}}return a}function He(d,C){var v=d.indexOf(C===1?":":"{"),x=d.substring(0,C!==3?v:10),u=d.substring(v+1,d.length-1);return vr(C!==2?x:x.replace(Pe,"$1"),u,C)}function Er(d,C){var v=Ge(C,C.charCodeAt(0),C.charCodeAt(1),C.charCodeAt(2));return v!==C+";"?v.replace(We," or ($1)").substring(4):"("+C+")"}function xr(d){var C=d.length,v=d.indexOf(":",9)+1,x=d.substring(0,v).trim(),u=d.substring(v,C-1).trim();switch(d.charCodeAt(9)*p){case 0:break;case ce:if(d.charCodeAt(10)!==110)break;default:for(var a=u.split((u="",O)),b=0,v=0,C=a.length;b<C;v=0,++b){for(var f=a[b],L=f.split(I);f=L[v];){var G=f.charCodeAt(0);if(p===1&&(G>y&&G<90||G>96&&G<123||G===be||G===ce&&f.charCodeAt(1)!==ce))switch(isNaN(parseFloat(f))+(f.indexOf("(")!==-1)){case 1:switch(f){case"infinite":case"alternate":case"backwards":case"running":case"normal":case"forwards":case"both":case"none":case"linear":case"ease":case"ease-in":case"ease-out":case"ease-in-out":case"paused":case"reverse":case"alternate-reverse":case"inherit":case"initial":case"unset":case"step-start":case"step-end":break;default:f+=w}}L[v++]=f}u+=(b===0?"":",")+L.join(" ")}}return u=x+u+";",Re===1||Re===2&&He(u,1)?l+u+u:u}function Nr(d){for(var C=0,v=d.length,x=Array(v),u,a;C<v;++C){for(var b=d[C].split(V),f="",L=0,G=0,oe=0,N=0,xe=b.length;L<xe;++L)if(!((G=(a=b[L]).length)===0&&xe>1)){if(oe=f.charCodeAt(f.length-1),N=a.charCodeAt(0),u="",L!==0)switch(oe){case le:case pe:case ge:case Ae:case D:case B:break;default:u=" "}switch(N){case H:a=u+U;case pe:case ge:case Ae:case D:case P:case B:break;case k:{a=u+a+U;break}case me:{switch(a.charCodeAt(1)*2+a.charCodeAt(2)*3){case 530:if(or>0){a=u+a.substring(8,G-1);break}default:(L<1||b[L-1].length<1)&&(a=u+U+a)}break}case q:u="";default:G>1&&a.indexOf(":")>0?a=u+a.replace(ie,"$1"+U+"$2"):a=u+a+U}f+=a}x[C]=f.replace(g,"").trim()}return x}function er(d,C,v,x,u,a,b,f,L,G){for(var oe=0,N=C,xe;oe<Ke;++oe)switch(xe=fr[oe].call(nr,d,N,v,x,u,a,b,f,L,G)){case void 0:case!1:case!0:case null:break;default:N=xe}if(N!==C)return N}function kr(d,C,v,x){for(var u=C+1;u<v;++u)switch(x.charCodeAt(u)){case se:{if(d===le&&x.charCodeAt(u-1)===le&&C+2!==u)return u+1;break}case J:if(d===se)return u+1}return u}function Dr(d,C,v,x){for(var u=C+1;u<v;++u)switch(x.charCodeAt(u)){case d:return u}return u}function yr(d){return d.replace(g,"").replace(_,"").replace(R,"$1").replace(E,"$1").replace(de," ")}function Tr(d){switch(d){case void 0:case null:{Ke=fr.length=0;break}default:if(typeof d=="function")fr[Ke++]=d;else if(typeof d=="object")for(var C=0,v=d.length;C<v;++C)Tr(d[C]);else c=!!d|0}return Tr}function wr(d){for(var C in d){var v=d[C];switch(C){case"keyframe":p=v|0;break;case"global":or=v|0;break;case"cascade":Oe=v|0;break;case"compress":br=v|0;break;case"semicolon":_r=v|0;break;case"preserve":ur=v|0;break;case"prefix":vr=null,v?typeof v!="function"?Re=1:(Re=2,vr=v):Re=0}}return wr}function nr(d,C){if(this!==void 0&&this.constructor===nr)return Ce(d);var v=d,x=v.charCodeAt(0);x<33&&(x=(v=v.trim()).charCodeAt(0)),p>0&&(w=v.replace(ye,x===k?"":"-")),x=1,Oe===1?ee=v:U=v;var u=[ee],a;Ke>0&&(a=er(r,C,u,u,Ye,Le,0,0,0,0),a!==void 0&&typeof a=="string"&&(C=a));var b=re(lr,u,C,0,0);return Ke>0&&(a=er(Or,b,u,u,Ye,Le,b.length,0,0,0),a!==void 0&&typeof(b=a)!="string"&&(x=0)),w="",ee="",U="",Be=0,Ye=1,Le=1,br*x===0?b:yr(b)}return nr.use=Tr,nr.set=wr,h!==void 0&&wr(h),nr})},90810:function(je,Ce,h){"use strict";var Q=h(28659),g=h(56840),ne=h(46509),fe=h(65096),M=h(94450),O=h(44194),I=h(14258),V=h(31549),j=O.createContext(null);j.displayName="InsightsFlowAppContext";var Ee=function(){var E=React.useRef(!0);return React.useEffect(function(){return function(){E.current=!1}},[]),E},ke=function(){var E=React.useContext(j);return E&&E.getApp?E.getApp():new InsightsFlowCore},ye=function(){var E=React.useContext(j);if(!E)return{};var de=E.tagRelations,ie=E.selectedTagValuesMap,ue=E.selectedTagValuesHashMap;return{tagRelations:de,selectedTagValuesMap:ie,selectedTagValuesHashMap:ue}},Te=function(){return O.useContext(j)},X=(0,ne.Z)(function R(){var E=this,de=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;(0,fe.Z)(this,R),(0,M.Z)(this,"cache",null),(0,M.Z)(this,"isUserDefinedFlag",void 0),(0,M.Z)(this,"hasWatchState",void 0),(0,M.Z)(this,"onAppChangeCallback",void 0),(0,M.Z)(this,"setApp",function(ie){E.cache=ie,E.onAppChangeCallback&&E.onAppChangeCallback(ie)}),(0,M.Z)(this,"isUserDefined",function(){return E.isUserDefinedFlag}),(0,M.Z)(this,"getApp",function(){return E.cache}),(0,M.Z)(this,"onAppChange",function(ie){E.onAppChangeCallback=ie}),(0,M.Z)(this,"dispose",function(){E.cache=null}),this.isUserDefinedFlag=de,this.hasWatchState=!1}),o=function(E){var de=E.viewId,ie=E.children,ue=Te(),De=(0,O.useState)([]),Me=(0,g.Z)(De,2),We=Me[0],Fe=Me[1],Pe=(0,O.useState)(new Map),Ue=(0,g.Z)(Pe,2),Ve=Ue[0],Ie=Ue[1],l=(0,O.useState)({}),m=(0,g.Z)(l,2),A=m[0],F=m[1],i=O.useState(null),S=(0,g.Z)(i,2),B=S[1],P=O.useMemo(function(){return ue&&ue.isUserDefined()?ue:new X(!1)},[]);return P.isUserDefined()||P.onAppChange(function(){return B(0)}),O.useEffect(function(){if(de){var k=new I.ZP({viewId:de});P.setApp(k),k.event.watch("onTagSelectChange",function(Y){Ie(new Map(Y.selectedTagValuesMap)),F((0,Q.Z)({},Y.selectedTagValuesHashMap))}),k.event.watch("onTagRelationChange",function(Y){Fe(Y.tagRelations)}),k.event.watch("onGroupInfoChange",function(Y){Fe(Y.tagRelations)})}},[]),P.isUserDefined()?(0,V.jsx)(V.Fragment,{children:ie}):(0,V.jsx)(j.Provider,{value:(0,Q.Z)((0,Q.Z)({},P),{},{tagRelations:We,selectedTagValuesMap:Ve,selectedTagValuesHashMap:A}),children:ie})};o.displayName="InsightsFlowAppContextProvider";var _=function(E){var de=E.children,ie=O.useState(null),ue=(0,g.Z)(ie,2),De=ue[1],Me=O.useMemo(function(){return new X(!0)},[]);return Me.onAppChange(function(){return De(0)}),(0,V.jsx)(j.Provider,{value:(0,Q.Z)({},Me),children:de})};_.displayName="InsightsFlowAppProvider"},73771:function(je,Ce,h){"use strict";var Q=h(28659),g=h(81556),ne=h(44194),fe=h(14258),M=function(l,m){return m||(l?"insights-flow-".concat(l):"insights-flow")},O=new fe.ZP,I=ne.createContext({getPrefixCls:M,insightsFlow:O}),V=I.Consumer,j=h(31549),Ee=ne.createContext(!1),ke=function(l){var m=l.children,A=l.disabled,F=ne.useContext(Ee);return(0,j.jsx)(Ee.Provider,{value:A!=null?A:F,children:m})},ye=null,Te=ne.createContext(void 0),X=function(l){var m=l.children,A=l.size;return(0,j.jsx)(Te.Consumer,{children:function(i){return(0,j.jsx)(Te.Provider,{value:A||i,children:m})}})},o=Te,_=null,R=["getTargetContainer","getPopupContainer","pageHeader","input","pagination"],E="insights-flow",de="anticon",ie,ue;function De(){return ie||E}function Me(){return ue||de}var We=function(l){var m=l.prefixCls,A=l.iconPrefixCls;m!==void 0&&(ie=m),A!==void 0&&(ue=A)},Fe=function(){return{getPrefixCls:function(m,A){return A||(m?"".concat(De(),"-").concat(m):De())},getIconPrefixCls:Me,getRootPrefixCls:function(m,A){return m||ie||(A&&A.includes("-")?A.replace(/^(.*)-[^-]*$/,"$1"):De())}}},Pe=function(l){var m=l.children,A=l.autoInsertSpaceInButton,F=l.componentSize,i=l.space,S=l.virtual,B=l.dropdownMatchSelectWidth,P=l.parentContext,k=l.componentDisabled,Y=ne.useCallback(function(y,D){var H=l.prefixCls;if(D)return D;var ce=H||P.getPrefixCls("");return y?"".concat(ce,"-").concat(y):ce},[P.getPrefixCls,l.prefixCls]),J=(0,Q.Z)((0,Q.Z)({},P),{},{autoInsertSpaceInButton:A,space:i,virtual:S,dropdownMatchSelectWidth:B,getPrefixCls:Y});R.forEach(function(y){var D=l[y];D&&(J[y]=D)});var $=(0,g.default)(function(){return J},J,function(y,D){var H=Object.keys(y),ce=Object.keys(D);return H.length!==ce.length||H.some(function(be){return y[be]!==D[be]})}),z=m;return F&&(z=(0,j.jsx)(X,{size:F,children:z})),k!==void 0&&(z=(0,j.jsx)(ke,{disabled:k,children:z})),(0,j.jsx)(I.Provider,{value:$,children:z})},Ue=function(l){return(0,j.jsx)(V,{children:function(A){return(0,j.jsx)(Pe,(0,Q.Z)({parentContext:A},l))}})};Ue.ConfigContext=I,Ue.SizeContext=o,Ue.config=We;var Ve=null},23986:function(je,Ce,h){"use strict";var Q="TME_TOKEN",g="@insight@",ne="\u65E0\u6807\u9898\u5206\u7FA4",fe=1e3,M=function(o){return o.None="\u65E0",o.TenThousand="\u4E07",o.EnTenThousand="w",o.OneHundredMillion="\u4EBF",o.Thousand="k",o.Million="M",o.Giga="G",o}({}),O=function(o){return o.SUM="SUM",o.AVG="AVG",o.COUNT="COUNT",o.MAX="MAX",o.MIN="MIN",o}({}),I=function(o){return o[o.ONLINE=1]="ONLINE",o[o.OFFLINE=0]="OFFLINE",o[o.READY=2]="READY",o}({}),V=function(o){return o.ENUM="ENUM",o.STRING="STRING",o.NUMBER="NUMBER",o.INT="INT",o.LONG="LONG",o.BOOLEAN="BOOLEAN",o.DOUBLE="DOUBLE",o.TIMESTAMP="TIMESTAMP",o.DATE="DATE",o}({}),j=function(o){return o.DETAIL="DETAIL",o.ENUM="ENUM",o.ID="ID",o}({}),Ee=function(o){return o.BETWEEN="between",o.EQUAL="eq",o.GREATER_AND_EQUAL="ge",o.GREATER="gt",o.LESS_AND_EQUAL="le",o.LESS="lt",o.NOT_EQUAL="ne",o.IN="in",o.NOT_IN="not_in",o.TOP_N="top_n",o.LIKE="like",o.IS_NOT_NULL="is_not_null",o.DIFFUSION="diffusion",o.SUBSET="subset",o}({}),ke=function(o){return o.Default="DEFAULT",o.Asc="ASC",o.Desc="DESC",o.Custom="CUSTOM",o}({}),ye=[{tagId:970,tagName:"\u884C\u4E1A"},{tagId:971,tagName:"\u6D3B\u52A8"},{tagId:973,tagName:"\u8282\u65E5"},{tagId:972,tagName:"\u5B63\u8282"},{tagId:969,tagName:"\u573A\u666F"},{tagId:974,tagName:"\u60C5\u611F"},{tagId:736,tagName:"\u6D41\u6D3E\u6807\u7B7E"},{tagId:735,tagName:"\u8BED\u79CD\u6807\u7B7E"}],Te={song:{name:"song",label:"\u6B4C\u66F2",key:"15",viewId:"15",sceneType:"15"},singer:{name:"singer",label:"\u827A\u4EBA",key:"263",viewId:"263",sceneType:"1"}},X={15:{name:"song",label:"\u6B4C\u66F2"},263:{name:"singer",label:"\u827A\u4EBA"},1:{name:"singer",label:"\u827A\u4EBA"}}},17017:function(je,Ce,h){"use strict";h.d(Ce,{h3:function(){return qe},uf:function(){return ar}});var Q=h(23986),g=h(28977),ne=h.n(g),fe=h(80852),M=h.n(fe),O=h(39378),I=h.n(O),V=h(55950),j=h.n(V),Ee=h(44194),ke=h(31549);function ye(r){return localStorage.setItem(TOKEN_KEY,r)}function Te(){return localStorage.getItem(TOKEN_KEY)}function X(){localStorage.removeItem(TOKEN_KEY)}var o=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:8,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:62,n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),s=[],c;if(e)for(c=0;c<e;c++)s[c]=n[Math.floor(Math.random()*t)];else{var p;for(s[8]=s[13]=s[18]=s[23]="-",s[14]="4",c=0;c<36;c++)s[c]||(p=Math.floor(Math.random()*16),s[c]=n[c===19?p%4%8+8:p])}return s.join("")};function _(r,e){if(isNaN(+r)||e<0||e>100)return r;var t=(+r).toFixed(e);if(!/^[0-9.]+$/g.test(t))return"0";for(;t.includes(".")&&(t.endsWith(".")||t.endsWith("0"));)t=t.slice(0,-1);return t}function R(r){if(isNaN(+r))return r;var e=r.toString().split(".");e[0]=e[0].replace(/\B(?=(\d{3})+(?!\d))/g,",");var t=e.join(".");return t}function E(r,e){var t=+r;if(isNaN(t)||e===NumericUnit.None)return r;var n=0;switch(e){case NumericUnit.TenThousand:case NumericUnit.EnTenThousand:n=4;break;case NumericUnit.OneHundredMillion:n=8;break;case NumericUnit.Thousand:n=3;break;case NumericUnit.Million:n=6;break;case NumericUnit.Giga:n=9;break}return t/Math.pow(10,n)}function de(r,e){var t=Number(r)*100;return isFinite(t)?t<0?"-".concat(_(Math.abs(t),e),"%"):"".concat(_(t,e),"%"):r}function ie(r,e){return _jsx("span",{className:"formattedValue",children:de(r,e)})}function ue(r){return"rgb("+parseInt("0x"+r.slice(1,3))+","+parseInt("0x"+r.slice(3,5))+","+parseInt("0x"+r.slice(5,7))+")"}function De(r,e){return"rgba("+parseInt("0x"+r.slice(1,3))+","+parseInt("0x"+r.slice(3,5))+","+parseInt("0x"+r.slice(5,7))+","+e+")"}function Me(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",t=new RegExp("[\\&\\?]".concat(e,"=([^&#]+)"),"g");return r.replace(t,"")}function We(){return window.location.href.includes("external")}function Fe(r,e){var t={},n={};return e.forEach(function(s){var c=s.name;t[c]=[],n[c]=-1}),r.forEach(function(s,c){var p=r[c-1];e.forEach(function(w,U){var ee,re=w.name,Se=(ee=e[U-1])===null||ee===void 0?void 0:ee.name;if(n[re]!==c-1&&p&&p[re]===s[re]){t[re][c]=0;return}if(n[Se]===c){t[re][c]=1,n[re]=c;return}for(var we=1;;){var Ge=r[c+we],He=c+we-1;if(Ge&&Ge[re]===s[re]&&!(Se&&He>=n[Se]))we+=1;else{t[re][c]=we,n[re]=He;break}}})}),t}var Pe=function(e,t,n){if(t&&(e===void 0||+e==0))return 0;if(e===void 0)return"-";if(!isFinite(+e))return e;var s=e>=1e8?NumericUnit.OneHundredMillion:e>=1e4?n?NumericUnit.TenThousand:NumericUnit.EnTenThousand:NumericUnit.None,c=E(e,s);return c=_(c,s===NumericUnit.OneHundredMillion?2:e<1?3:1),c=R(c),typeof c=="number"&&isNaN(c)||+c==0?"-":"".concat(c).concat(s===NumericUnit.None?"":s)},Ue=function(e,t){return _jsx("span",{className:"formattedValue",children:Pe(e,t)})},Ve=function(e){if(navigator.clipboard)navigator.clipboard.writeText("".concat(e));else{var t=document.createElement("textarea");document.body.appendChild(t),t.style.position="fixed",t.style.clip="rect(0 0 0 0)",t.style.top="10px",t.value=e,t.select(),document.execCommand("copy",!0),document.body.removeChild(t)}},Ie=function(e,t,n){var s=encodeURIComponent(JSON.stringify(t)),c=window.location,p=c.host,w=c.pathname;window.history.replaceState({},"","//".concat(p).concat(w,"?").concat(n?"type=".concat(n,"&"):"","filter=").concat(s))},l=function(){return window.location.host.includes("-sit")},m=function(e,t){return dayjs(e).format(t||"YYYY-MM-DD")},A=function(e,t){return e.reduce(function(n,s){var c=_objectSpread({},n),p=s[t];return c[p]||(c[p]=[]),c[p].push(s),c},{})},F=function(e,t,n){return e.sort(function(s,c){var p=t.findIndex(function(U){return U===s[n]}),w=t.findIndex(function(U){return U===c[n]});return p-w})},i=function(e){setTimeout(function(){var t=document.getElementById("messageContainer");if(t&&t.scrollHeight>t.clientHeight)if(e){var n=document.getElementById(e);n&&(t.scrollTop=t.scrollHeight-n.clientHeight-130)}else t.scrollTop=t.scrollHeight},100)};function S(r,e){var t=r.fetcher,n=r.updater,s=r.cleanup,c=useState(!1),p=_slicedToArray(c,2),w=p[0],U=p[1];return useEffect(function(){var ee=!1;return U(!0),t().then(function(re){ee||n(re)}).catch(function(){return null}).finally(function(){U(!1)}),function(){s==null||s(),ee=!0}},e),{loading:w,setLoading:U}}function B(r){return Object.keys(r).map(function(e){return{value:e,label:r[e]}})}function P(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",e;r?e=decodeURIComponent(r):e=decodeURIComponent(window.location.href);var t=e.indexOf("?");if(t===-1)return{};var n=e.substr(t+1),s=n.split("&"),c={};return s.forEach(function(p){var w=p.split("="),U=w[0],ee=w[1];Array.isArray(c[U])?c[U].push(ee):c[U]?c[U]=[c[U],ee]:c[U]=ee||!0}),c}var k={string:String,number:Number},Y=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"string";return Object.keys(e).map(function(n){return{value:k[t](n),label:e[n]}})};function J(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return r.map(function(e){var t=_objectSpread({},e);return t.value=t.projectIncreId,t.children&&(t.children=J(t.children)),t})}function $(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1?arguments[1]:void 0,t="EDIT";return r.map(function(n){var s,c=_objectSpread({},n);return typeof e=="function"?c.disabled=e(c.authCodes):((s=c.authCodes)!==null&&s!==void 0?s:[]).includes(t)||(c.disabled=!0),c.children&&(c.children=$(c.children,e)),c})}function z(r){return!!(r&&Array.isArray(r)&&r.length>0)}var y=function(e,t,n){var s=Array.isArray(e)?e:[],c=s.map(function(p){return{value:p[t],text:p[n]}});return c};function D(r){var e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(r);return e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null}function H(r,e){var t;if(r!=null&&r.includes("rgb")){var n=r.match(/\d+/g).map(Number),s=_slicedToArray(n,4),c=s[0],p=s[1],w=s[2],U=s[3];t={r:c,g:p,b:w,a:U}}else t=D(r)||{};return"rgba(".concat(t.r+e,",").concat(t.g+e,",").concat(t.b+e).concat(t.a?",".concat(t.a):"",")")}function ce(r){return H(r,80)}function be(r,e,t){r.forEach(function(n){var s;t.push(n[e]),((s=n.children)===null||s===void 0?void 0:s.length)>0&&be(n.children,e,t)})}function le(r){return r.map(function(e){return Object.keys(e).reduce(function(t,n){return t[n]=isArray(e[n])?e[n][0]:e[n],t},{})})}function q(r,e,t){var n=[],s=e.diff(r,t||"days"),c=t==="months"?"YYYY-MM":"YYYY-MM-DD";n.push(r.format(c));for(var p=1;p<=s;p++)n.push(r.add(p,t||"days").format(c));return n}function me(r){var e=window.open();e.opener=null,e.location.href=r}function he(r,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,n=[],s=function(w){if(r[w].parentId===e){var U=r.filter(function(ee){return ee.id!==r[w].id});r[w].depth=t,r[w].title=r[w].name,r[w].key=r[w].id,r[w].children=he(U,r[w].id,t+1),n.push(r[w])}};for(var c in r)s(c);return n}function Z(r,e){var t=[];return e.forEach(function(n){if(n.children&&n.children.length)if(n.title.indexOf(r)>-1)t.push(n);else{var s=Z(r,n.children),c=_objectSpread(_objectSpread({},n),{},{children:s});s&&s.length&&t.push(c)}else n.title.indexOf(r)>-1&&t.push(n)}),t}function se(r){var e;if(!(r!=null&&(e=r.children)!==null&&e!==void 0&&e.length))return-1;var t=_createForOfIteratorHelper(r.children),n;try{for(t.s();!(n=t.n()).done;){var s,c=n.value;if(c.type===1||!((s=c.children)!==null&&s!==void 0&&s.length))return c.id;var p=se(c);if(p>0)return p}}catch(w){t.e(w)}finally{t.f()}return-1}function ge(r){var e;if(!(r!=null&&(e=r.children)!==null&&e!==void 0&&e.length))return null;var t=_createForOfIteratorHelper(r.children),n;try{for(t.s();!(n=t.n()).done;){var s,c=n.value;if(c.type===1||!((s=c.children)!==null&&s!==void 0&&s.length))return c;var p=se(c);if(p!==null)return p}}catch(w){t.e(w)}finally{t.f()}return null}function Ae(r){if(!r)return"";for(var e="",t=0;t<r.length;t++)e+="\\u"+r.charCodeAt(t).toString(16);return e}function pe(r){if(!r)return"";for(var e="",t=r.split("\\u"),n=1;n<t.length;n++)e+=String.fromCharCode(parseInt(t[n],16));return e}function K(r){var e=/[\u4e00-\u9fa5]/g;return e.test(r)}var pr="0.00%",Sr="0.000000000%",qe="0,0[.]00";function ar(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"0,0";return j()(r).format(e)}var tr=null,Cr=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"16px",n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"normal",s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"DINPro Medium",c=tr||(tr=document.createElement("canvas")),p=c.getContext("2d");p.font="".concat(n," ").concat(t," ").concat(s);var w=p.measureText(e);return Math.ceil(w.width)};function ir(r,e,t){for(var n=0,s=decodeURIComponent(t);n<e.length-1;n++)r[e[n]]===void 0&&(r[decodeURIComponent(e[n])]=e[n+1].match(/^\d+$/)?[]:{}),r=r[decodeURIComponent(e[n])];if(r[decodeURIComponent(e[n])]&&r[decodeURIComponent(e[n])].length){var c=Array.isArray(r[decodeURIComponent(e[n])])&&r[decodeURIComponent(e[n])].includes(s),p=r[decodeURIComponent(e[n])]===s;c||p||(r[decodeURIComponent(e[n])]=[s].concat(r[decodeURIComponent(e[n])]))}else r[decodeURIComponent(e[n])]=s}function mr(r){return r.split("&").reduce(function(e,t){var n=t.split("="),s=_slicedToArray(n,2),c=s[0],p=s[1];return p&&ir(e,c.split(/[\[\]]/g).filter(function(w){return w}),p),e},{})}var Mr=window.navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)!==null,Ar=window.navigator.userAgent.match(/(iPhone|iPod|ios)/i),Pr=window.navigator.userAgent.match(/(Android)/i),Le=function(e,t,n,s,c,p){var w=q(dayjs(s),dayjs(c),p),U=w.map(function(ee){var re=e.find(function(Se){return Se[t]===ee});return _objectSpread(_objectSpread({},re||{}),{},_defineProperty(_defineProperty({},t,ee),n,re?re[n]:0))});return U},Ye=function(e,t){var n,s;dayjs.extend(minMax);var c=e.map(function(p){return dayjs(p[t])});return[(n=dayjs.min(c))===null||n===void 0?void 0:n.format("YYYY-MM-DD"),(s=dayjs.max(c))===null||s===void 0?void 0:s.format("YYYY-MM-DD")]};function Be(r){var e=[];function t(n){!n.children||n.children.length===0?e.push(n):n.children.forEach(function(s){return t(s)})}return r.forEach(function(n){return t(n)}),e}function Oe(r){var e={},t=[];return r.forEach(function(n){e[n.id]=n,n.children=[]}),r.forEach(function(n){if(n.parentId){var s=e[n.parentId];s&&s.children.push(n)}else t.push(n)}),t}function Re(r){var e=Oe(r),t=Be(e);return t}function or(r,e,t){var n=2*Math.PI/t*e,s=r*Math.cos(n),c=r*Math.sin(n);return{x:s,y:c}}function br(r){var e={},t=new URLSearchParams(r[0]==="?"?r.slice(1):r);return t.forEach(function(n,s){e.hasOwnProperty(s)?typeof e[s]=="string"?e[s]=[e[s],n]:e[s].push(n):e[s]=n}),e}var _r=function(){var e=window.location.search;if(e){var t=mr(e.slice(1))||{},n=t.filter;return JSON.parse(decodeURIComponent(n))}return{}},ur=function(e){var t=e.reduce(function(n,s){return n+(isNumber(s)?s:0)},0);return t},lr=function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return e.map(function(n,s){var c=t;return c?n.className="".concat(n.className," ").concat(c):s%2!==0?(c="colSpanHeaderFont",n.className="".concat(n.className," ").concat(c)):(c="colSpanHeader colSpanHeaderFont",n.className="".concat(n.className," ").concat(c)),Array.isArray(n.children)&&(n.children=r(n.children,c)),n})},fr=null;function Ke(r,e){if(!isString(r))return r;if(!r)return e;try{return JSON.parse(r)}catch(t){return console.log(t),e}}var vr=function r(e,t){return e.reduce(function(n,s){var c=s.hideInTable,p=s.title,w=s.downloadTitle,U=s.key,ee=s.children,re=s.disabledExport,Se=s.forceExport;if(ee){var we=r(ee,s);return{groups:[].concat(_toConsumableArray(n.groups),_toConsumableArray(we.groups)),headers:[].concat(_toConsumableArray(n.headers),_toConsumableArray(we.headers))}}return!Se&&(c||!p||re)||(n.groups.push(U),n.headers.push(t?"".concat(t.downloadTitle||t.title,"-").concat(w||p):w||p)),n},{groups:[],headers:[]})};function Or(r,e,t){var n=[],s=r.map(function(we){return we.title});n.push(s.join(","));var c=_createForOfIteratorHelper(e),p;try{var w=function(){var Ge=p.value,He=r.map(function(Er){var xr=(""+Ge[Er.dataIndex]).replace(/"/g,'\\"');return'"'.concat(xr,'"')});n.push(He.join(","))};for(c.s();!(p=c.n()).done;)w()}catch(we){c.e(we)}finally{c.f()}var U=n.join(`
`),ee=new Blob(["\uFEFF",U],{type:"text/csv"}),re=window.URL.createObjectURL(ee),Se=document.createElement("a");Se.href=re,Se.download="".concat(t,".csv"),Se.click(),window.URL.revokeObjectURL(re)}},99087:function(je,Ce,h){"use strict";h.d(Ce,{VT:function(){return ue}});var Q=h(44194),g=h(73771),ne=h(96144),fe=h(31549),M=(0,ne.Z)({scriptUrl:["//at.alicdn.com/t/c/font_3201979_ngs9la147e.js"]}),O=function(m){var A=m.scriptUrl,F=m.type,i=A?createFromIconfontCN({scriptUrl:A}):M;return _jsx(i,{type:F})},I=null,V=null,j=function(m){var A=m.src,F=m.icon,i=_objectWithoutProperties(m,V),S=useState(""),B=_slicedToArray(S,2),P=B[0],k=B[1];return useEffect(function(){var Y=new Image;Y.onload=function(){k(A)},Y.src=A},[A]),_jsx(Avatar,_objectSpread(_objectSpread({shape:"square",size:90,className:"columnAvatar",icon:F||_jsx(IconFont,{type:"icon-zhuanji"})},i),{},{src:P}))},Ee=null,ke=function(m){var A=m.prefixCls,F=m.dataSource,i=F===void 0?[]:F,S=m.width,B=S===void 0?90:S,P=m.size,k=P===void 0?20:P,Y=m.shape,J=Y===void 0?"square":Y,$=m.listStyle,z=$===void 0?{}:$,y=m.onAvatarClick,D="tag-switch",H=React.useContext(ConfigContext),ce=H.getPrefixCls,be=ce(D,A),le=useState([]),q=_slicedToArray(le,2),me=q[0],he=q[1];return useEffect(function(){he(k?i.slice(0,k):i)},[i,k]),_jsx("div",{className:"".concat(be,"-avatar-list"),style:z,children:_jsx(Space,{wrap:!0,size:80,children:me.map(function(Z){var se=Z.src,ge=Z.title,Ae=Z.subTitle;return _jsxs(Space,{direction:"vertical",size:0,children:[_jsx(DelayAvatar,{shape:J,size:90,src:se,alt:ge,className:"columnAvatar",icon:_jsx(MIcon,{type:"icon-zhuanji"}),onClick:function(){y==null||y(Z,"avatar")}}),_jsx(Typography.Title,{level:5,style:{width:B,margin:0,cursor:"pointer",textAlign:J==="circle"?"center":"unset"},onClick:function(){y==null||y(Z,"title")},ellipsis:{tooltip:{title:ge,zIndex:19999}},children:ge}),Ae&&_jsx(Typography.Text,{type:"secondary",style:{width:B,cursor:"pointer"},onClick:function(){y==null||y(Z,"subTitle")},ellipsis:{tooltip:{title:ge,zIndex:19999}},children:Ae})]},"".concat(ge,"-").concat(Ae))})})})},ye=null,Te=h(90810),X=function(m){var A=m.prefixCls,F=m.options,i=m.value,S=m.onChange,B="first-class-segmented",P=React.useContext(ConfigContext),k=P.getPrefixCls,Y=k(B,A),J=useState(i),$=_slicedToArray(J,2),z=$[0],y=$[1];return useEffect(function(){y(i)},[i]),_jsxs(Space,{children:[_jsx("span",{style:{color:"#f87653"},children:"\u4E00\u7EA7\u5206\u7C7B"}),_jsx("div",{className:"".concat("".concat(Y,"-analysis-class-container")),children:_jsx(Segmented,{size:"large",options:F,value:z,onChange:function(H){y(H),S==null||S(H)}})})]})},o=null,_=h(46504),R=_.default.Search,E=function(m){var A=m.prefixCls,F=m.onSearch,i=m.onChange,S=m.loading,B=m.value,P=m.placeholder,k="header-search",Y=React.useContext(ConfigContext),J=Y.getPrefixCls,$=J(k,A),z=useRef(null);return _jsx("div",{className:"".concat($,"-search-box"),children:_jsx("div",{className:"".concat($,"-search"),children:_jsx(R,{placeholder:P||"\u8BF7\u8F93\u5165\u67E5\u8BE2\u5185\u5BB9",enterButton:_jsx(SearchOutlined,{}),autoComplete:"false",loading:S,value:B,onChange:i,className:"".concat($,"-search-control"),ref:z,onSearch:function(D,H){F==null||F(D,H)}})})})},de=null,ie=h(52994),ue=h(73790),De=h(47903),Me=h(95262),We=h(57860),Fe=h(97746),Pe=h(4792),Ue=h(28951),Ve=h(64325),Ie=h(90946)},2421:function(je,Ce,h){"use strict";h.d(Ce,{A7:function(){return X},Ab:function(){return fe},Fr:function(){return M},JM:function(){return de},K$:function(){return j},h5:function(){return O},lK:function(){return o}});var Q="-ms-",g="-moz-",ne="-webkit-",fe="comm",M="rule",O="decl",I="@page",V="@media",j="@import",Ee="@charset",ke="@viewport",ye="@supports",Te="@document",X="@namespace",o="@keyframes",_="@font-face",R="@counter-style",E="@font-feature-values",de="@layer",ie="@scope"},28067:function(je,Ce,h){"use strict";h.d(Ce,{MY:function(){return Ie}});var Q=h(2421),g=h(27079),ne=1,fe=1,M=0,O=0,I=0,V="";function j(i,S,B,P,k,Y,J,$){return{value:i,root:S,parent:B,type:P,props:k,children:Y,line:ne,column:fe,length:J,return:"",siblings:$}}function Ee(i,S){return assign(j("",null,null,"",null,null,0,i.siblings),i,{length:-i.length},S)}function ke(i){for(;i.root;)i=Ee(i.root,{children:[i]});append(i,i.siblings)}function ye(){return I}function Te(){return I=O>0?(0,g.uO)(V,--O):0,fe--,I===10&&(fe=1,ne--),I}function X(){return I=O<M?(0,g.uO)(V,O++):0,fe++,I===10&&(fe=1,ne++),I}function o(){return(0,g.uO)(V,O)}function _(){return O}function R(i,S){return(0,g.tb)(V,i,S)}function E(i){switch(i){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function de(i){return ne=fe=1,M=(0,g.to)(V=i),O=0,[]}function ie(i){return V="",i}function ue(i){return(0,g.fy)(R(O-1,Pe(i===91?i+2:i===40?i+1:i)))}function De(i){return ie(We(de(i)))}function Me(i){for(;(I=o())&&I<33;)X();return E(i)>2||E(I)>3?"":" "}function We(i){for(;X();)switch(E(I)){case 0:append(Ve(O-1),i);break;case 2:append(ue(I),i);break;default:append(from(I),i)}return i}function Fe(i,S){for(;--S&&X()&&!(I<48||I>102||I>57&&I<65||I>70&&I<97););return R(i,_()+(S<6&&o()==32&&X()==32))}function Pe(i){for(;X();)switch(I){case i:return O;case 34:case 39:i!==34&&i!==39&&Pe(I);break;case 40:i===41&&Pe(i);break;case 92:X();break}return O}function Ue(i,S){for(;X()&&i+I!==57;)if(i+I===84&&o()===47)break;return"/*"+R(S,O-1)+"*"+(0,g.Dp)(i===47?i:X())}function Ve(i){for(;!E(o());)X();return R(i,O)}function Ie(i){return ie(l("",null,null,null,[""],i=de(i),0,[0],i))}function l(i,S,B,P,k,Y,J,$,z){for(var y=0,D=0,H=J,ce=0,be=0,le=0,q=1,me=1,he=1,Z=0,se="",ge=k,Ae=Y,pe=P,K=se;me;)switch(le=Z,Z=X()){case 40:if(le!=108&&(0,g.uO)(K,H-1)==58){(0,g.Cw)(K+=(0,g.gx)(ue(Z),"&","&\f"),"&\f",(0,g.Wn)(y?$[y-1]:0))!=-1&&(he=-1);break}case 34:case 39:case 91:K+=ue(Z);break;case 9:case 10:case 13:case 32:K+=Me(le);break;case 92:K+=Fe(_()-1,7);continue;case 47:switch(o()){case 42:case 47:(0,g.R3)(A(Ue(X(),_()),S,B,z),z),(E(le||1)==5||E(o()||1)==5)&&(0,g.to)(K)&&(0,g.tb)(K,-1,void 0)!==" "&&(K+=" ");break;default:K+="/"}break;case 123*q:$[y++]=(0,g.to)(K)*he;case 125*q:case 59:case 0:switch(Z){case 0:case 125:me=0;case 59+D:he==-1&&(K=(0,g.gx)(K,/\f/g,"")),be>0&&((0,g.to)(K)-H||q===0&&le===47)&&(0,g.R3)(be>32?F(K+";",P,B,H-1,z):F((0,g.gx)(K," ","")+";",P,B,H-2,z),z);break;case 59:K+=";";default:if((0,g.R3)(pe=m(K,S,B,y,D,k,$,se,ge=[],Ae=[],H,Y),Y),Z===123)if(D===0)l(K,S,pe,pe,ge,Y,H,$,Ae);else{switch(ce){case 99:if((0,g.uO)(K,3)===110)break;case 108:if((0,g.uO)(K,2)===97)break;default:D=0;case 100:case 109:case 115:}D?l(i,pe,pe,P&&(0,g.R3)(m(i,pe,pe,0,0,k,$,se,k,ge=[],H,Ae),Ae),k,Ae,H,$,P?ge:Ae):l(K,pe,pe,pe,[""],Ae,0,$,Ae)}}y=D=be=0,q=he=1,se=K="",H=J;break;case 58:H=1+(0,g.to)(K),be=le;default:if(q<1){if(Z==123)--q;else if(Z==125&&q++==0&&Te()==125)continue}switch(K+=(0,g.Dp)(Z),Z*q){case 38:he=D>0?1:(K+="\f",-1);break;case 44:$[y++]=((0,g.to)(K)-1)*he,he=1;break;case 64:o()===45&&(K+=ue(X())),ce=o(),D=H=(0,g.to)(se=K+=Ve(_())),Z++;break;case 45:le===45&&(0,g.to)(K)==2&&(q=0)}}return Y}function m(i,S,B,P,k,Y,J,$,z,y,D,H){for(var ce=k-1,be=k===0?Y:[""],le=(0,g.Ei)(be),q=0,me=0,he=0;q<P;++q)for(var Z=0,se=(0,g.tb)(i,ce+1,ce=(0,g.Wn)(me=J[q])),ge=i;Z<le;++Z)(ge=(0,g.fy)(me>0?be[Z]+" "+se:(0,g.gx)(se,/&\f/g,be[Z])))&&(z[he++]=ge);return j(i,S,B,k===0?Q.Fr:$,z,y,D,H)}function A(i,S,B,P){return j(i,S,B,Q.Ab,(0,g.Dp)(ye()),(0,g.tb)(i,2,-2),0,P)}function F(i,S,B,P,k){return j(i,S,B,Q.h5,(0,g.tb)(i,0,P),(0,g.tb)(i,P+1,-1),P,k)}},84055:function(je,Ce,h){"use strict";h.d(Ce,{P:function(){return fe},q:function(){return ne}});var Q=h(2421),g=h(27079);function ne(M,O){for(var I="",V=0;V<M.length;V++)I+=O(M[V],V,M,O)||"";return I}function fe(M,O,I,V){switch(M.type){case Q.JM:if(M.children.length)break;case Q.K$:case Q.A7:case Q.h5:return M.return=M.return||M.value;case Q.Ab:return"";case Q.lK:return M.return=M.value+"{"+ne(M.children,V)+"}";case Q.Fr:if(!(0,g.to)(M.value=M.props.join(",")))return""}return(0,g.to)(I=ne(M.children,V))?M.return=M.value+"{"+I+"}":""}},27079:function(je,Ce,h){"use strict";h.d(Ce,{Cw:function(){return V},Dp:function(){return g},Ei:function(){return ye},R3:function(){return Te},Wn:function(){return Q},fy:function(){return M},gx:function(){return I},tb:function(){return Ee},to:function(){return ke},uO:function(){return j}});var Q=Math.abs,g=String.fromCharCode,ne=Object.assign;function fe(_,R){return j(_,0)^45?(((R<<2^j(_,0))<<2^j(_,1))<<2^j(_,2))<<2^j(_,3):0}function M(_){return _.trim()}function O(_,R){return(_=R.exec(_))?_[0]:_}function I(_,R,E){return _.replace(R,E)}function V(_,R,E){return _.indexOf(R,E)}function j(_,R){return _.charCodeAt(R)|0}function Ee(_,R,E){return _.slice(R,E)}function ke(_){return _.length}function ye(_){return _.length}function Te(_,R){return R.push(_),_}function X(_,R){return _.map(R).join("")}function o(_,R){return _.filter(function(E){return!O(E,R)})}}}]);
