const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
  app.use(
    '/api',
    createProxyMiddleware({
      // target: 'http://localhost:9080',
      target: 'http://***********:9080', //liwei本地调试
      changeOrigin: true,
    })
  );
  app.use(
    '/openapi',
    createProxyMiddleware({
      // target: 'http://localhost:9080',
        target: 'http://***********:9080', //liwei本地调试
      changeOrigin: true,
    })
  );
};
