(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[730],{80480:function(I){I.exports=function(){var x=document.getSelection();if(!x.rangeCount)return function(){};for(var w=document.activeElement,A=[],k=0;k<x.rangeCount;k++)A.push(x.getRangeAt(k));switch(w.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":w.blur();break;default:w=null;break}return x.removeAllRanges(),function(){x.type==="Caret"&&x.removeAllRanges(),x.rangeCount||A.forEach(function(p){x.addRange(p)}),w&&w.focus()}}},29541:function(I,x,w){"use strict";w.d(x,{Z:function(){return A}});function A(p){for(var i=[],d=1;d<arguments.length;d++)i[d-1]=arguments[d];var l=Array.from(typeof p=="string"?[p]:p);l[l.length-1]=l[l.length-1].replace(/\r?\n([\t ]*)$/,"");var h=l.reduce(function(s,v){var _=v.match(/\n([\t ]+|(?!\s).)/g);return _?s.concat(_.map(function(y){var M,R;return(R=(M=y.match(/[\t ]/g))===null||M===void 0?void 0:M.length)!==null&&R!==void 0?R:0})):s},[]);if(h.length){var g=new RegExp(`
[	 ]{`+Math.min.apply(Math,h)+"}","g");l=l.map(function(s){return s.replace(g,`
`)})}l[0]=l[0].replace(/^\r?\n/,"");var u=l[0];return i.forEach(function(s,v){var _=u.match(/(?:^|\n)( *)$/),y=_?_[1]:"",M=s;typeof s=="string"&&s.includes(`
`)&&(M=String(s).split(`
`).map(function(R,N){return N===0?R:""+y+R}).join(`
`)),u+=M+l[v+1]}),u}var k=null},54308:function(I,x,w){"use strict";w.d(x,{D:function(){return k}});function A(p,i,d){var l=d||{},h=l.noTrailing,g=h===void 0?!1:h,u=l.noLeading,s=u===void 0?!1:u,v=l.debounceMode,_=v===void 0?void 0:v,y,M=!1,R=0;function N(){y&&clearTimeout(y)}function V(L){var B=L||{},F=B.upcomingOnly,O=F===void 0?!1:F;N(),M=!O}function G(){for(var L=arguments.length,B=new Array(L),F=0;F<L;F++)B[F]=arguments[F];var O=this,U=Date.now()-R;if(M)return;function D(){R=Date.now(),i.apply(O,B)}function j(){y=void 0}!s&&_&&!y&&D(),N(),_===void 0&&U>p?s?(R=Date.now(),g||(y=setTimeout(_?j:D,p))):D():g!==!0&&(y=setTimeout(_?j:D,_===void 0?p-U:p))}return G.cancel=V,G}function k(p,i,d){var l=d||{},h=l.atBegin,g=h===void 0?!1:h;return A(p,i,{debounceMode:g!==!1})}},94224:function(I,x,w){"use strict";w.d(x,{Z:function(){return i}});function A(e){"@babel/helpers - typeof";return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},A(e)}var k=/^\s+/,p=/\s+$/;function i(e,n){if(e=e||"",n=n||{},e instanceof i)return e;if(!(this instanceof i))return new i(e,n);var t=d(e);this._originalInput=e,this._r=t.r,this._g=t.g,this._b=t.b,this._a=t.a,this._roundA=Math.round(100*this._a)/100,this._format=n.format||t.format,this._gradientType=n.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=t.ok}i.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var n=this.toRgb();return(n.r*299+n.g*587+n.b*114)/1e3},getLuminance:function(){var n=this.toRgb(),t,r,f,a,o,c;return t=n.r/255,r=n.g/255,f=n.b/255,t<=.03928?a=t/12.92:a=Math.pow((t+.055)/1.055,2.4),r<=.03928?o=r/12.92:o=Math.pow((r+.055)/1.055,2.4),f<=.03928?c=f/12.92:c=Math.pow((f+.055)/1.055,2.4),.2126*a+.7152*o+.0722*c},setAlpha:function(n){return this._a=Z(n),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var n=u(this._r,this._g,this._b);return{h:n.h*360,s:n.s,v:n.v,a:this._a}},toHsvString:function(){var n=u(this._r,this._g,this._b),t=Math.round(n.h*360),r=Math.round(n.s*100),f=Math.round(n.v*100);return this._a==1?"hsv("+t+", "+r+"%, "+f+"%)":"hsva("+t+", "+r+"%, "+f+"%, "+this._roundA+")"},toHsl:function(){var n=h(this._r,this._g,this._b);return{h:n.h*360,s:n.s,l:n.l,a:this._a}},toHslString:function(){var n=h(this._r,this._g,this._b),t=Math.round(n.h*360),r=Math.round(n.s*100),f=Math.round(n.l*100);return this._a==1?"hsl("+t+", "+r+"%, "+f+"%)":"hsla("+t+", "+r+"%, "+f+"%, "+this._roundA+")"},toHex:function(n){return v(this._r,this._g,this._b,n)},toHexString:function(n){return"#"+this.toHex(n)},toHex8:function(n){return _(this._r,this._g,this._b,this._a,n)},toHex8String:function(n){return"#"+this.toHex8(n)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return this._a==1?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(b(this._r,255)*100)+"%",g:Math.round(b(this._g,255)*100)+"%",b:Math.round(b(this._b,255)*100)+"%",a:this._a}},toPercentageRgbString:function(){return this._a==1?"rgb("+Math.round(b(this._r,255)*100)+"%, "+Math.round(b(this._g,255)*100)+"%, "+Math.round(b(this._b,255)*100)+"%)":"rgba("+Math.round(b(this._r,255)*100)+"%, "+Math.round(b(this._g,255)*100)+"%, "+Math.round(b(this._b,255)*100)+"%, "+this._roundA+")"},toName:function(){return this._a===0?"transparent":this._a<1?!1:J[v(this._r,this._g,this._b,!0)]||!1},toFilter:function(n){var t="#"+y(this._r,this._g,this._b,this._a),r=t,f=this._gradientType?"GradientType = 1, ":"";if(n){var a=i(n);r="#"+y(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+f+"startColorstr="+t+",endColorstr="+r+")"},toString:function(n){var t=!!n;n=n||this._format;var r=!1,f=this._a<1&&this._a>=0,a=!t&&f&&(n==="hex"||n==="hex6"||n==="hex3"||n==="hex4"||n==="hex8"||n==="name");return a?n==="name"&&this._a===0?this.toName():this.toRgbString():(n==="rgb"&&(r=this.toRgbString()),n==="prgb"&&(r=this.toPercentageRgbString()),(n==="hex"||n==="hex6")&&(r=this.toHexString()),n==="hex3"&&(r=this.toHexString(!0)),n==="hex4"&&(r=this.toHex8String(!0)),n==="hex8"&&(r=this.toHex8String()),n==="name"&&(r=this.toName()),n==="hsl"&&(r=this.toHslString()),n==="hsv"&&(r=this.toHsvString()),r||this.toHexString())},clone:function(){return i(this.toString())},_applyModification:function(n,t){var r=n.apply(null,[this].concat([].slice.call(t)));return this._r=r._r,this._g=r._g,this._b=r._b,this.setAlpha(r._a),this},lighten:function(){return this._applyModification(V,arguments)},brighten:function(){return this._applyModification(G,arguments)},darken:function(){return this._applyModification(L,arguments)},desaturate:function(){return this._applyModification(M,arguments)},saturate:function(){return this._applyModification(R,arguments)},greyscale:function(){return this._applyModification(N,arguments)},spin:function(){return this._applyModification(B,arguments)},_applyCombination:function(n,t){return n.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(D,arguments)},complement:function(){return this._applyCombination(F,arguments)},monochromatic:function(){return this._applyCombination(j,arguments)},splitcomplement:function(){return this._applyCombination(U,arguments)},triad:function(){return this._applyCombination(O,[3])},tetrad:function(){return this._applyCombination(O,[4])}},i.fromRatio=function(e,n){if(A(e)=="object"){var t={};for(var r in e)e.hasOwnProperty(r)&&(r==="a"?t[r]=e[r]:t[r]=$(e[r]));e=t}return i(e,n)};function d(e){var n={r:0,g:0,b:0},t=1,r=null,f=null,a=null,o=!1,c=!1;return typeof e=="string"&&(e=te(e)),A(e)=="object"&&(P(e.r)&&P(e.g)&&P(e.b)?(n=l(e.r,e.g,e.b),o=!0,c=String(e.r).substr(-1)==="%"?"prgb":"rgb"):P(e.h)&&P(e.s)&&P(e.v)?(r=$(e.s),f=$(e.v),n=s(e.h,r,f),o=!0,c="hsv"):P(e.h)&&P(e.s)&&P(e.l)&&(r=$(e.s),a=$(e.l),n=g(e.h,r,a),o=!0,c="hsl"),e.hasOwnProperty("a")&&(t=e.a)),t=Z(t),{ok:o,format:e.format||c,r:Math.min(255,Math.max(n.r,0)),g:Math.min(255,Math.max(n.g,0)),b:Math.min(255,Math.max(n.b,0)),a:t}}function l(e,n,t){return{r:b(e,255)*255,g:b(n,255)*255,b:b(t,255)*255}}function h(e,n,t){e=b(e,255),n=b(n,255),t=b(t,255);var r=Math.max(e,n,t),f=Math.min(e,n,t),a,o,c=(r+f)/2;if(r==f)a=o=0;else{var m=r-f;switch(o=c>.5?m/(2-r-f):m/(r+f),r){case e:a=(n-t)/m+(n<t?6:0);break;case n:a=(t-e)/m+2;break;case t:a=(e-n)/m+4;break}a/=6}return{h:a,s:o,l:c}}function g(e,n,t){var r,f,a;e=b(e,360),n=b(n,100),t=b(t,100);function o(H,z,C){return C<0&&(C+=1),C>1&&(C-=1),C<1/6?H+(z-H)*6*C:C<1/2?z:C<2/3?H+(z-H)*(2/3-C)*6:H}if(n===0)r=f=a=t;else{var c=t<.5?t*(1+n):t+n-t*n,m=2*t-c;r=o(m,c,e+1/3),f=o(m,c,e),a=o(m,c,e-1/3)}return{r:r*255,g:f*255,b:a*255}}function u(e,n,t){e=b(e,255),n=b(n,255),t=b(t,255);var r=Math.max(e,n,t),f=Math.min(e,n,t),a,o,c=r,m=r-f;if(o=r===0?0:m/r,r==f)a=0;else{switch(r){case e:a=(n-t)/m+(n<t?6:0);break;case n:a=(t-e)/m+2;break;case t:a=(e-n)/m+4;break}a/=6}return{h:a,s:o,v:c}}function s(e,n,t){e=b(e,360)*6,n=b(n,100),t=b(t,100);var r=Math.floor(e),f=e-r,a=t*(1-n),o=t*(1-f*n),c=t*(1-(1-f)*n),m=r%6,H=[t,o,a,a,c,t][m],z=[c,t,t,o,a,a][m],C=[a,a,c,t,t,o][m];return{r:H*255,g:z*255,b:C*255}}function v(e,n,t,r){var f=[T(Math.round(e).toString(16)),T(Math.round(n).toString(16)),T(Math.round(t).toString(16))];return r&&f[0].charAt(0)==f[0].charAt(1)&&f[1].charAt(0)==f[1].charAt(1)&&f[2].charAt(0)==f[2].charAt(1)?f[0].charAt(0)+f[1].charAt(0)+f[2].charAt(0):f.join("")}function _(e,n,t,r,f){var a=[T(Math.round(e).toString(16)),T(Math.round(n).toString(16)),T(Math.round(t).toString(16)),T(W(r))];return f&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)&&a[3].charAt(0)==a[3].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}function y(e,n,t,r){var f=[T(W(r)),T(Math.round(e).toString(16)),T(Math.round(n).toString(16)),T(Math.round(t).toString(16))];return f.join("")}i.equals=function(e,n){return!e||!n?!1:i(e).toRgbString()==i(n).toRgbString()},i.random=function(){return i.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})};function M(e,n){n=n===0?0:n||10;var t=i(e).toHsl();return t.s-=n/100,t.s=q(t.s),i(t)}function R(e,n){n=n===0?0:n||10;var t=i(e).toHsl();return t.s+=n/100,t.s=q(t.s),i(t)}function N(e){return i(e).desaturate(100)}function V(e,n){n=n===0?0:n||10;var t=i(e).toHsl();return t.l+=n/100,t.l=q(t.l),i(t)}function G(e,n){n=n===0?0:n||10;var t=i(e).toRgb();return t.r=Math.max(0,Math.min(255,t.r-Math.round(255*-(n/100)))),t.g=Math.max(0,Math.min(255,t.g-Math.round(255*-(n/100)))),t.b=Math.max(0,Math.min(255,t.b-Math.round(255*-(n/100)))),i(t)}function L(e,n){n=n===0?0:n||10;var t=i(e).toHsl();return t.l-=n/100,t.l=q(t.l),i(t)}function B(e,n){var t=i(e).toHsl(),r=(t.h+n)%360;return t.h=r<0?360+r:r,i(t)}function F(e){var n=i(e).toHsl();return n.h=(n.h+180)%360,i(n)}function O(e,n){if(isNaN(n)||n<=0)throw new Error("Argument to polyad must be a positive number");for(var t=i(e).toHsl(),r=[i(e)],f=360/n,a=1;a<n;a++)r.push(i({h:(t.h+a*f)%360,s:t.s,l:t.l}));return r}function U(e){var n=i(e).toHsl(),t=n.h;return[i(e),i({h:(t+72)%360,s:n.s,l:n.l}),i({h:(t+216)%360,s:n.s,l:n.l})]}function D(e,n,t){n=n||6,t=t||30;var r=i(e).toHsl(),f=360/t,a=[i(e)];for(r.h=(r.h-(f*n>>1)+720)%360;--n;)r.h=(r.h+f)%360,a.push(i(r));return a}function j(e,n){n=n||6;for(var t=i(e).toHsv(),r=t.h,f=t.s,a=t.v,o=[],c=1/n;n--;)o.push(i({h:r,s:f,v:a})),a=(a+c)%1;return o}i.mix=function(e,n,t){t=t===0?0:t||50;var r=i(e).toRgb(),f=i(n).toRgb(),a=t/100,o={r:(f.r-r.r)*a+r.r,g:(f.g-r.g)*a+r.g,b:(f.b-r.b)*a+r.b,a:(f.a-r.a)*a+r.a};return i(o)},i.readability=function(e,n){var t=i(e),r=i(n);return(Math.max(t.getLuminance(),r.getLuminance())+.05)/(Math.min(t.getLuminance(),r.getLuminance())+.05)},i.isReadable=function(e,n,t){var r=i.readability(e,n),f,a;switch(a=!1,f=ne(t),f.level+f.size){case"AAsmall":case"AAAlarge":a=r>=4.5;break;case"AAlarge":a=r>=3;break;case"AAAsmall":a=r>=7;break}return a},i.mostReadable=function(e,n,t){var r=null,f=0,a,o,c,m;t=t||{},o=t.includeFallbackColors,c=t.level,m=t.size;for(var H=0;H<n.length;H++)a=i.readability(e,n[H]),a>f&&(f=a,r=i(n[H]));return i.isReadable(e,r,{level:c,size:m})||!o?r:(t.includeFallbackColors=!1,i.mostReadable(e,["#fff","#000"],t))};var X=i.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},J=i.hexNames=Q(X);function Q(e){var n={};for(var t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function Z(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function b(e,n){Y(e)&&(e="100%");var t=ee(e);return e=Math.min(n,Math.max(0,parseFloat(e))),t&&(e=parseInt(e*n,10)/100),Math.abs(e-n)<1e-6?1:e%n/parseFloat(n)}function q(e){return Math.min(1,Math.max(0,e))}function S(e){return parseInt(e,16)}function Y(e){return typeof e=="string"&&e.indexOf(".")!=-1&&parseFloat(e)===1}function ee(e){return typeof e=="string"&&e.indexOf("%")!=-1}function T(e){return e.length==1?"0"+e:""+e}function $(e){return e<=1&&(e=e*100+"%"),e}function W(e){return Math.round(parseFloat(e)*255).toString(16)}function K(e){return S(e)/255}var E=function(){var e="[-\\+]?\\d+%?",n="[-\\+]?\\d*\\.\\d+%?",t="(?:"+n+")|(?:"+e+")",r="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?",f="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?";return{CSS_UNIT:new RegExp(t),rgb:new RegExp("rgb"+r),rgba:new RegExp("rgba"+f),hsl:new RegExp("hsl"+r),hsla:new RegExp("hsla"+f),hsv:new RegExp("hsv"+r),hsva:new RegExp("hsva"+f),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function P(e){return!!E.CSS_UNIT.exec(e)}function te(e){e=e.replace(k,"").replace(p,"").toLowerCase();var n=!1;if(X[e])e=X[e],n=!0;else if(e=="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var t;return(t=E.rgb.exec(e))?{r:t[1],g:t[2],b:t[3]}:(t=E.rgba.exec(e))?{r:t[1],g:t[2],b:t[3],a:t[4]}:(t=E.hsl.exec(e))?{h:t[1],s:t[2],l:t[3]}:(t=E.hsla.exec(e))?{h:t[1],s:t[2],l:t[3],a:t[4]}:(t=E.hsv.exec(e))?{h:t[1],s:t[2],v:t[3]}:(t=E.hsva.exec(e))?{h:t[1],s:t[2],v:t[3],a:t[4]}:(t=E.hex8.exec(e))?{r:S(t[1]),g:S(t[2]),b:S(t[3]),a:K(t[4]),format:n?"name":"hex8"}:(t=E.hex6.exec(e))?{r:S(t[1]),g:S(t[2]),b:S(t[3]),format:n?"name":"hex"}:(t=E.hex4.exec(e))?{r:S(t[1]+""+t[1]),g:S(t[2]+""+t[2]),b:S(t[3]+""+t[3]),a:K(t[4]+""+t[4]),format:n?"name":"hex8"}:(t=E.hex3.exec(e))?{r:S(t[1]+""+t[1]),g:S(t[2]+""+t[2]),b:S(t[3]+""+t[3]),format:n?"name":"hex"}:!1}function ne(e){var n,t;return e=e||{level:"AA",size:"small"},n=(e.level||"AA").toUpperCase(),t=(e.size||"small").toLowerCase(),n!=="AA"&&n!=="AAA"&&(n="AA"),t!=="small"&&t!=="large"&&(t="small"),{level:n,size:t}}},96659:function(I,x,w){"use strict";w.d(x,{j:function(){return p}});const A=9,k=32;function p(d){const l=String(d),h=/\r?\n|\r/g;let g=h.exec(l),u=0;const s=[];for(;g;)s.push(i(l.slice(u,g.index),u>0,!0),g[0]),u=g.index+g[0].length,g=h.exec(l);return s.push(i(l.slice(u),u>0,!1)),s.join("")}function i(d,l,h){let g=0,u=d.length;if(l){let s=d.codePointAt(g);for(;s===A||s===k;)g++,s=d.codePointAt(g)}if(h){let s=d.codePointAt(u-1);for(;s===A||s===k;)u--,s=d.codePointAt(u-1)}return u>g?d.slice(g,u):""}},59521:function(I,x,w){"use strict";w.d(x,{r:function(){return A}});function A(){const p=[],i={run:d,use:l};return i;function d(...h){let g=-1;const u=h.pop();if(typeof u!="function")throw new TypeError("Expected function as last argument, not "+u);s(null,...h);function s(v,..._){const y=p[++g];let M=-1;if(v){u(v);return}for(;++M<h.length;)(_[M]===null||_[M]===void 0)&&(_[M]=h[M]);h=_,y?k(y,s)(..._):u(null,..._)}}function l(h){if(typeof h!="function")throw new TypeError("Expected `middelware` to be a function, not "+h);return p.push(h),i}}function k(p,i){let d;return l;function l(...u){const s=p.length>u.length;let v;s&&u.push(h);try{v=p.apply(this,u)}catch(_){const y=_;if(s&&d)throw y;return h(y)}s||(v&&v.then&&typeof v.then=="function"?v.then(g,h):v instanceof Error?h(v):g(v))}function h(u,...s){d||(d=!0,i(u,...s))}function g(u){h(null,u)}}}}]);
