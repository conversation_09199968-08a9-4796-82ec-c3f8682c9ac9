"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[402],{14258:function(ma,ue,b){b.d(ue,{ZP:function(){return va}});var G={};b.r(G),b.d(G,{addTagInfosToTree:function(){return ke},adjustBetweenOperatorByTagValue:function(){return we},crc32String:function(){return Q},deleteUrlQuery:function(){return Ue},exchangeString2Number:function(){return Ne},filterEmptyNumberList:function(){return Re},findInfoFromList:function(){return Se},findLeafNodes:function(){return x},findTagInfoFromList:function(){return Fe},flatTagInfo:function(){return Ge},formatTagTreeData:function(){return Pe},formatterCamelCase:function(){return be},formatterDataSource:function(){return ye},genUuid:function(){return Te},generatorInsightsDetailPageQueryParams:function(){return Ae},getMapKeyIndex:function(){return Ie},getMetricAggType:function(){return X},getMusicIdType:function(){return $e},getNumericalValueOperatorTagValueMap:function(){return De},getTempGroupName:function(){return We},getUrlParams:function(){return Oe},isArrayOfValues:function(){return je},isDetailTagType:function(){return Le},isEnumTagType:function(){return Ve},isIdTagType:function(){return Me},isNumberTagType:function(){return Ee},isPromise:function(){return J},jsonParse:function(){return Ze},openPage:function(){return Ce},sortTimeStringsMinToMax:function(){return Be}});var B={};b.r(B),b.d(B,{formatterFlatTagInfoListToTagDB:function(){return He}});var f=b(28659),N=b(18191),D=b(40571),U=b(46509),O=b(65096),c=b(94450),oe=b(55679),I=b.n(oe),le=b(80670),ce=b(56840),z=b(68874),$=b(8087),ge=b(83788),de=b(56288),fe=b(45113),pe=b(61120),j,C={ENUM:"ENUM",STRING:"STRING",NUMBER:"NUMBER",INT:"INT",LONG:"LONG",BOOLEAN:"BOOLEAN",DOUBLE:"DOUBLE",TIMESTAMP:"TIMESTAMP",DATE:"DATE"},Ta={STRING:"\u5B57\u7B26\u578B",NUMBER:"\u6570\u503C\u578B",DATE:"\u65E5\u671F\u578B"},W={DETAIL:"DETAIL",ENUM:"ENUM",ID:"ID"},S={BETWEEN:"between",EQUAL:"eq",GREATER_AND_EQUAL:"ge",GREATER:"gt",LESS_AND_EQUAL:"le",LESS:"lt",NOT_EQUAL:"ne",IN:"in",NOT_IN:"not_in",TOP_N:"top_n",LIKE:"like",IS_NOT_NULL:"is_not_null",DIFFUSION:"diffusion",SUBSET:"subset"},H=(j={},(0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)(j,S.IN,"\u5305\u542B"),S.NOT_IN,"\u4E0D\u5305\u542B"),S.SUBSET,"\u53EA\u5305\u542B"),S.BETWEEN,"\u533A\u95F4"),S.NOT_EQUAL,"!="),S.EQUAL,"="),S.GREATER_AND_EQUAL,">="),S.GREATER,">"),S.LESS_AND_EQUAL,"<="),S.LESS,"<"),(0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)(j,S.TOP_N,"TOP_"),S.LIKE,"\u6A21\u7CCA\u5339\u914D"),S.IS_NOT_NULL,"\u4E0D\u4E3A\u7A7A"),S.DIFFUSION,"\u7FA4\u4F53\u6269\u6563")),he=function(l){return l[l.PORTRAIT=1]="PORTRAIT",l[l.MULTI_DIMENSION=2]="MULTI_DIMENSION",l[l.GROUP_COMPARE=3]="GROUP_COMPARE",l[l.GROUP_CROSS=4]="GROUP_CROSS",l}({}),ve=function(l){return l[l.RANGE=1]="RANGE",l[l.LIST=2]="LIST",l[l.ES=4]="ES",l}({}),me=function(l){return l.DAY="DAY",l.WEEK="WEEK",l.MONTH="MONTH",l}({}),F=function(l){return l[l.GROUP_SAVED=0]="GROUP_SAVED",l[l.VIEW=1]="VIEW",l[l.GROUP_PREVIEW=2]="GROUP_PREVIEW",l[l.CUSTOMIZED_TAG=3]="CUSTOMIZED_TAG",l}({}),J=function(u){return!!u&&((0,z.Z)(u)==="object"||typeof u=="function")&&typeof u.then=="function"},Te=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:8,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:62,t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),a=[],r;if(u)for(r=0;r<u;r++)a[r]=t[Math.floor(Math.random()*e)];else{var n,i="-";for(a[8]=i,a[13]=i,a[18]=i,a[23]=i,a[14]="4",r=0;r<36;r++)a[r]||(n=Math.floor(Math.random()*16),a[r]=t[r===19?n%4%8+8:n])}return a.join("")},Ie=function(u,e){var t=[];return e.forEach(function(a,r){t.push(r)}),t.indexOf(u)},X=function(u){var e=new RegExp("\\S+(?=\\()"),t=e.exec(u)||[],a=(t[0]||"").toUpperCase();return a},ye=function(u,e){var t={},a=[],r={};u.forEach(function(i){var g=i.tagId,s=i.name,o=X(s),d="a".concat(g);o&&(d="".concat(o.toLocaleLowerCase(),"(`").concat(d,"`)"));var p=(0,f.Z)((0,f.Z)({},i),{},{camelName:(0,$.Z)(s),dataIndex:d});t[d]=p,a.push(d),r[(0,$.Z)(s)]=p});var n=e.reduce(function(i,g){var s=a.reduce(function(o,d){var p=g[d],h=p||"";return(0,ge.Z)(p)&&(h=p.join(",")),(0,f.Z)((0,f.Z)({},o),{},(0,c.Z)({},t[d].camelName,h))},{});return i.push(s),i},[]);return t={},{columnListMap:r,dataSource:n}};function Ze(l,u){if(!(0,de.Z)(l))return l;if(!l)return u;try{return JSON.parse(l)}catch(e){return console.log(e),u}}function be(l){return(0,$.Z)(l)}var De=function(u){var e=u.numericalValueOperator,t=u.parameterList,a=u.valueName;return a?"".concat(H[e],":").concat(a):e===S.BETWEEN?t.join("-"):H[e]+t.join()},Se=function(u,e,t){var a=(t||[]).filter(function(n){return"".concat(n[e])==="".concat(u)}),r=a[0];return r},Me=function(u,e){return u===C.STRING&&e===W.ID},Ve=function(u,e){return u===C.STRING&&e===W.ENUM},Ee=function(u){return!![C.INT,C.LONG,C.LONG].includes(u)},Le=function(u,e){return u===C.STRING&&e===W.DETAIL},we=function(u,e){if(e===S.BETWEEN){var t=(0,ce.Z)(u,2),a=t[0],r=t[1];if(a&&r)return S.BETWEEN;if(!a&&r)return S.LESS_AND_EQUAL;if(a&&!r)return S.GREATER_AND_EQUAL}return e||S.IN},Ne=function(u){return u.map(function(e){var t=+e;return isNaN(t)?0:t})},Re=function(u){var e=u.reduce(function(t,a){return a===void 0||a===""||(0,fe.Z)(+a)&&t.push(+a),t},[]);return e},Ae=function(u){var e=u.stepKeys,t=u.analysisType,a=u.initGlobalViewId;return(0,f.Z)((0,f.Z)({},u),{},{stepKeys:e||1,analysisType:t||he.MULTI_DIMENSION,initGlobalViewId:a||2})},Ce=function(u){var e=window.open();e.opener=null,e.location.href=u};function Ue(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",e=new RegExp("[\\&\\?]".concat(u,"=([^&#]+)"),"g");return l.replace(e,"")}function Oe(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",u;l?u=decodeURIComponent(l):u=decodeURIComponent(window.location.href);var e=u.indexOf("?");if(e===-1)return{};var t=u.substr(e+1),a=t.split("&"),r={};return a.forEach(function(n){var i=n.split("="),g=i[0],s=i[1];Array.isArray(r[g])?r[g].push(s):r[g]?r[g]=[r[g],s]:r[g]=s||!0}),r}var Pe=function(u){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=u.reduce(function(a,r){if(r!=null&&r.classId){var n=r.classId,i=(0,f.Z)((0,f.Z)({},r),{},{value:"firstClassIdKey-".concat(n),key:"firstClassIdKey-".concat(n),title:r.className,data:{firstClassId:n},children:r.tagSubTreeInfoList.reduce(function(g,s){if(s!=null&&s.classId){var o=(s==null?void 0:s.classId)||"",d=(0,f.Z)((0,f.Z)({},s),{},{value:"secondClassIdKey-".concat(o),key:"secondClassIdKey-".concat(o),title:s.className,name:s.className,data:{firstClassId:n,secondClassId:o},children:e?[]:s.tagDetailInfoList.map(function(p){var h=p.tagId;return(0,f.Z)((0,f.Z)({},p),{},{value:"tagIdKey#".concat(h),key:"tagIdKey#".concat(h),title:p.tagName,name:p.tagName,data:{firstClassId:n,secondClassId:o,tagId:h},type:p.tagFieldType===C.STRING||p.tagFieldType===C.DATE?"category":"value",isLeaf:!0})}),tagDetailInfoList:[]});g.push(d)}return g},[]),tagSubTreeInfoList:[]});a.push(i)}return a},[]);return t};function ke(l,u){return u.forEach(function(e){var t=e.firstClassId,a=e.secondClassId,r=l.find(function(i){return i.classId===t});if(r){var n=r.children.find(function(i){return i.classId===a});n&&(n.children||(n.children=[]),n.children.push((0,f.Z)({},e)))}}),l}function x(l,u){var e=[],t=(0,le.Z)(l),a;try{for(t.s();!(a=t.n()).done;){var r=a.value;if(r.children){var n=x(r.children,u);e.push.apply(e,(0,N.Z)(n))}else(u.tagId&&r.tagId===u.tagId||u.tagName&&r.tagName===u.tagName)&&e.push(r)}}catch(i){t.e(i)}finally{t.f()}return e}function Fe(l,u){return l.filter(function(e){var t,a;return((t=u.tagIdList)===null||t===void 0?void 0:t.includes(e.tagId))||((a=u.tagNameList)===null||a===void 0?void 0:a.includes(e.tagName))})}var Ge=function(u,e){if(!Array.isArray(u))return[];var t=u.reduce(function(a,r){var n=r.children.reduce(function(i,g){var s=g.children;return Array.isArray(s)?i.concat(s.map(function(o){return(0,f.Z)((0,f.Z)({},o),{},{firstClassName:r.className,secondClassName:g.className},e)})):i},[]);return a.concat(n)},[]);return t},Be=function(u){return u.sort(function(e,t){var a=new Date(e).getTime(),r=new Date(t).getTime();return a-r})},$e=function(u){return!u||u===F.GROUP_SAVED?F.VIEW:u===-1?F.GROUP_PREVIEW:F.GROUP_SAVED},Q=function(u){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:16,t=pe.ZP.crc32(u).toString(e);return t};function je(l){return!!(l&&Array.isArray(l)&&l.length>0)}function We(l){var u=l.slice(0,5).map(function(e){var t=e.tagName,a=e.tagSingleFilterList,r=a.slice(0,1).map(function(n){var i=n.parameterList;return i.join(",")});return"".concat(t,"(").concat(r).concat(a.length>1?"...":"",")")}).join("-");return u.slice(0,32)}var Qe=b(58724),q=b.n(Qe),Ye={i8:"1.0.76"},Ke=b(84025),P=b.n(Ke),k=b(33485),Y=b(70647),K=b(63009),ze=b(43206),He=function(u,e,t){return u.map(function(a){var r=e.reduce(function(n,i){var g=a[i];return(0,f.Z)((0,f.Z)({},n),{},(0,c.Z)({},i,g))},{});return(0,f.Z)((0,f.Z)({},r),{},{tagInfo:a},t)})},_=function(l){(0,Y.Z)(e,l);var u=(0,K.Z)(e);function e(t,a){var r;return(0,O.Z)(this,e),r=u.call(this,t),(0,c.Z)((0,k.Z)(r),"utils",void 0),a!==void 0&&r.version(a).stores({}),r.utils=B,r}return(0,U.Z)(e)}(ze.ZP),Ia=null,ee=["tagId","viewId","firstClassId","firstClassName","secondClassId","secondClassName","tagName","tagEnName","tagInfo","insightsFlowDbUpdateTime"],Je=function(l){(0,Y.Z)(e,l);var u=(0,K.Z)(e);function e(){var t;return(0,O.Z)(this,e),t=u.call(this,"insightsFlowTagDataBase"),(0,c.Z)((0,k.Z)(t),"tags",void 0),(0,c.Z)((0,k.Z)(t),"tagTree",void 0),(0,c.Z)((0,k.Z)(t),"registerTableHooks",function(){t.tags.hook("updating",function(a,r,n){n.insightsFlowDbUpdateTime=P()().format("YYYY-MM-DD hh:mm:ss")}),t.tags.hook("creating",function(a,r){r.insightsFlowDbUpdateTime=P()().format("YYYY-MM-DD hh:mm:ss")})}),t.version(1).stores({tags:"".concat(ee.map(function(a){return a==="tagId"||a==="viewId"?"[tagId+viewId]":a}).join(","),",viewId"),tagTree:"&viewId, tagTreeData,insightsFlowDbUpdateTime"}),t.tags=t.table("tags"),t.tagTree=t.table("tagTree"),t.registerTableHooks(),t}return(0,U.Z)(e,[{key:"addTagInfoFromList",value:function(){var t=(0,D.Z)(I().mark(function r(n,i){var g;return I().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return g=this.utils.formatterFlatTagInfoListToTagDB(n,ee,{viewId:i}),o.next=3,this.addTagInfo(g);case 3:return o.abrupt("return",o.sent);case 4:case"end":return o.stop()}},r,this)}));function a(r,n){return t.apply(this,arguments)}return a}()},{key:"addTagTreeInfo",value:function(){var t=(0,D.Z)(I().mark(function r(n,i){return I().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(!(!i||!Array.isArray(n))){s.next=2;break}return s.abrupt("return");case 2:s.prev=2,this.transaction("rw",this.tagTree,(0,D.Z)(I().mark(function o(){return I().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return p.next=2,this.tagTree.put({viewId:i,tagTreeData:n});case 2:return p.abrupt("return",p.sent);case 3:case"end":return p.stop()}},o,this)}))),s.next=10;break;case 6:return s.prev=6,s.t0=s.catch(2),console.error("addTagTreeInfo operation failed:",{module:"TagDatabase",operation:"addTagTreeInfo",errorCode:s.t0.code,errorMessage:s.t0.message,errorStack:s.t0.stack}),s.abrupt("return",!1);case 10:case"end":return s.stop()}},r,this,[[2,6]])}));function a(r,n){return t.apply(this,arguments)}return a}()},{key:"findTagTreeInfo",value:function(){var t=(0,D.Z)(I().mark(function r(n){var i;return I().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(n){s.next=2;break}return s.abrupt("return");case 2:return s.prev=2,s.next=5,this.tagTree.get(n);case 5:return i=s.sent,s.abrupt("return",i?i.tagTreeData:!1);case 9:return s.prev=9,s.t0=s.catch(2),console.error("findTagTreeInfo operation failed:",{module:"TagDatabase",operation:"findTagTreeInfo",errorCode:s.t0.code,errorMessage:s.t0.message,errorStack:s.t0.stack}),s.abrupt("return",!1);case 13:case"end":return s.stop()}},r,this,[[2,9]])}));function a(r){return t.apply(this,arguments)}return a}()},{key:"addTagInfo",value:function(){var t=(0,D.Z)(I().mark(function r(n,i,g){var s,o,d,p;return I().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(m.prev=0,!(typeof n=="number"&&typeof i=="number"&&g)){m.next=8;break}s=n,o=i,d=g,this.transaction("rw",this.tags,(0,D.Z)(I().mark(function T(){return I().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return y.next=2,this.tags.put((0,f.Z)((0,f.Z)({},d),{},{tagId:s,viewId:o}));case 2:return y.abrupt("return",y.sent);case 3:case"end":return y.stop()}},T,this)}))),m.next=14;break;case 8:if(!Array.isArray(n)){m.next=13;break}p=n,this.transaction("rw",this.tags,(0,D.Z)(I().mark(function T(){return I().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return y.next=2,this.tags.bulkPut(p.map(function(Z){var V=Z.tagId,M=Z.viewId,A=Z.tagInfo;return(0,f.Z)((0,f.Z)({},A),{},{tagId:V,viewId:M})}));case 2:return y.abrupt("return",y.sent);case 3:case"end":return y.stop()}},T,this)}))),m.next=14;break;case 13:throw new Error("Invalid arguments");case 14:m.next=19;break;case 16:m.prev=16,m.t0=m.catch(0),console.error("Failed to add tag info:",m.t0);case 19:case"end":return m.stop()}},r,this,[[0,16]])}));function a(r,n,i){return t.apply(this,arguments)}return a}()},{key:"findTagInfo",value:function(){var t=(0,D.Z)(I().mark(function r(n){var i,g,s=arguments;return I().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(i=s.length>1&&s[1]!==void 0?s[1]:{},d.prev=1,g=[],!i.tagIdList){d.next=9;break}return d.next=6,this.tags.where("tagId").anyOf(i.tagIdList).and(function(p){return p.viewId===n}).toArray();case 6:g=d.sent,d.next=18;break;case 9:if(!i.tagNameList){d.next=15;break}return d.next=12,this.tags.where("tagName").anyOf(i.tagNameList).and(function(p){return p.viewId===n}).toArray();case 12:g=d.sent,d.next=18;break;case 15:return d.next=17,this.tags.where("viewId").equals(n).toArray();case 17:g=d.sent;case 18:return d.abrupt("return",g);case 21:return d.prev=21,d.t0=d.catch(1),console.error("Database operation failed:",{module:"TagDatabase",operation:"findTagInfo",errorCode:d.t0.code,errorMessage:d.t0.message,errorStack:d.t0.stack}),d.abrupt("return",!1);case 25:case"end":return d.stop()}},r,this,[[1,21]])}));function a(r){return t.apply(this,arguments)}return a}()}]),e}(_),Xe=new Je,xe=Xe,qe=function(l){(0,Y.Z)(e,l);var u=(0,K.Z)(e);function e(){var t;return(0,O.Z)(this,e),t=u.call(this,"insighsFlowAnalysisDataBase"),(0,c.Z)((0,k.Z)(t),"analysisData",void 0),(0,c.Z)((0,k.Z)(t),"registerTableHooks",function(){t.analysisData.hook("updating",function(a,r,n){n.insightsFlowDbUpdateTime=P()().format("YYYY-MM-DD hh:mm:ss")}),t.analysisData.hook("creating",function(a,r){r.insightsFlowDbUpdateTime=P()().format("YYYY-MM-DD hh:mm:ss")})}),t.version(1).stores({analysisData:"&paramsHash, dataSource,insightsFlowDbUpdateTime"}),t.analysisData=t.table("analysisData"),t.registerTableHooks(),t}return(0,U.Z)(e,[{key:"saveAnalysisQueryData",value:function(){var t=(0,D.Z)(I().mark(function r(n,i){var g;return I().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:if(!(!n||!i)){o.next=2;break}return o.abrupt("return");case 2:return o.prev=2,g=Q(JSON.stringify(n)),o.next=6,this.analysisData.put({paramsHash:g,dataSource:i});case 6:return o.abrupt("return",o.sent);case 9:return o.prev=9,o.t0=o.catch(2),console.error("saveAnalysisQueryData operation failed:",{module:"insighsFlowAnalysisDataBase",operation:"saveAnalysisQueryData",errorCode:o.t0.code,errorMessage:o.t0.message,errorStack:o.t0.stack}),o.abrupt("return",!1);case 13:case"end":return o.stop()}},r,this,[[2,9]])}));function a(r,n){return t.apply(this,arguments)}return a}()},{key:"findAnalysisQueryData",value:function(){var t=(0,D.Z)(I().mark(function r(n){var i,g;return I().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:if(n){o.next=2;break}return o.abrupt("return");case 2:return o.prev=2,i=Q(JSON.stringify(n)),o.next=6,this.analysisData.get(i);case 6:return g=o.sent,o.abrupt("return",g?g.dataSource:!1);case 10:return o.prev=10,o.t0=o.catch(2),console.error("findAnalysisQueryData operation failed:",{module:"insighsFlowAnalysisDataBase",operation:"findAnalysisQueryData",errorCode:o.t0.code,errorMessage:o.t0.message,errorStack:o.t0.stack}),o.abrupt("return",!1);case 14:case"end":return o.stop()}},r,this,[[2,10]])}));function a(r){return t.apply(this,arguments)}return a}()}]),e}(_),_e=new qe,ea=_e,aa={tagDb:xe,analysisDb:ea},ta=aa,ra={preId:"insightsFlowCore-",timeSign:"|-insightsFlowCore-|",status:{SUCCESS:0,FAILURE:1,OVERFLOW:2,TIMEOUT:3},storage:localStorage||window.localStorage,getKey:function(u){return this.preId+u},set:function(u,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:new Date().getTime()+26784e5,a=arguments.length>3?arguments[3]:void 0,r=this.status.SUCCESS,n=this.getKey(u),i;if(t instanceof Date)i=t.getTime();else if(typeof t=="number")i=t;else throw new Error("Invalid time parameter");i===0&&(i=new Date("1970-01-01 00:00:00").getTime());try{this.storage.setItem(n,i+this.timeSign+e)}catch(g){if(r=this.status.OVERFLOW,a)a.call(this,r,n,e);else throw g}a&&a.call(this,r,n,e)},get:function(u,e){var t=this.status.SUCCESS,a=this.getKey(u),r=null,n=this.timeSign.length,i,g,s;try{r=this.storage.getItem(a)}catch(o){return s={status:this.status.FAILURE,value:null},e==null||e.call(this,s.status,s.value),s}return r?(i=r.indexOf(this.timeSign),g=+r.slice(0,i),g>new Date().getTime()||g===0?r=r.slice(i+n):(r=null,t=this.status.TIMEOUT,this.remove(u))):t=this.status.FAILURE,s={status:t,value:r},e==null||e.call(this,s.status,s.value),s},remove:function(u,e){var t=this.status.FAILURE,a=this.getKey(u),r;try{r=this.storage.getItem(a)}catch(i){}if(r)try{this.storage.removeItem(a),t=this.status.SUCCESS}catch(i){}var n=null;t>0?n=null:r&&(n=r.slice(r.indexOf(this.timeSign)+this.timeSign.length)),e&&e.call(this,t,n)}},na=ra,ia={db:ta,storage:na},sa=ia,ae=b(42575),L=["bd.tmeoa.com","bd-pre.tmeoa.com","bd-test.tmeoa.com","bd-dev.tmeoa.com","localhost:8088"].includes(window.location.host)?"/api":"/bdapi/api";ae.Z.interceptors.response.use(function(l){return l.data});var w=function(u,e){var t=localStorage.getItem("TME_TOKEN"),a={Authorization:t?"Bearer ".concat(t):""},r=e.headers;return(0,ae.Z)((0,f.Z)((0,f.Z)({url:u},e),{},{headers:(0,f.Z)((0,f.Z)({},a),r)}))},ua=function(){function l(){var u=this;(0,O.Z)(this,l),(0,c.Z)(this,"getListDefaultParams",function(e){return(0,f.Z)({pageSize:10,current:1,filters:{}},e)}),(0,c.Z)(this,"searchTagValuesWithPage",function(e){var t=u.getListDefaultParams(e);return w("".concat(L,"/polaris/tagManager/searchTagValuesWithPage"),{method:"POST",headers:{noCleanData:"true"},data:t})}),(0,c.Z)(this,"getGroupInfo",function(e){return w("".concat(L,"/polaris/group/getGroupInfo"),{method:"GET",params:{groupId:e}})}),(0,c.Z)(this,"previewGroupInfo",function(e){return w("".concat(L,"/polaris/group/generateGroup"),{method:"POST",headers:{noCleanData:"true"},data:e})}),(0,c.Z)(this,"editGroup",function(e){return w("".concat(L,"/polaris/group/editGroup"),{method:"PUT",headers:{noCleanData:"true"},data:e})}),(0,c.Z)(this,"getInsightCustomizeTag",function(e){var t=e.groupId,a=e.isPublishedStated;return w("".concat(L,"/polaris/insight/getInsightCustomizeTag"),{method:"GET",params:{groupId:t,isPublishedStated:a}})}),(0,c.Z)(this,"downloadMultidimensionalData",function(e){return w("".concat(L,"/polaris/insight/downloadMultidimensionalData"),{method:"POST",data:e,headers:{noCleanData:"true"}})}),(0,c.Z)(this,"saveGroup",function(e){return w("".concat(L,"/polaris/group/saveGroup"),{method:"POST",headers:{noCleanData:"true"},data:e})}),(0,c.Z)(this,"deleteGroup",function(e){return w("".concat(L,"/polaris/group/deleteGroup"),{method:"DELETE",params:{groupId:e}})}),(0,c.Z)(this,"getMultidimensionalData",function(e){return w("".concat(L,"/polaris/insight/getMultidimensionalData"),{method:"POST",data:e})}),(0,c.Z)(this,"getTagTree",function(e){return w("".concat(L,"/polaris/tagMarket/metadata"),{method:"POST",data:{viewId:e}})}),(0,c.Z)(this,"pageTagCustomizedOriginalInfo",function(e){return w("".concat(L,"/polaris/tagManager/pageTagCustomizedOriginalInfo"),{method:"POST",data:(0,f.Z)({current:1,pageSize:500},e)})}),(0,c.Z)(this,"getInsightInfoMultiGroupByTag",function(e){return w("".concat(L,"/polaris/insight/getInsightInfoMultiGroupByTag"),{method:"POST",data:e})})}return(0,U.Z)(l,[{key:"queryGroupInfoList",value:function(){var u=(0,D.Z)(I().mark(function t(a){return I().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",w("".concat(L,"/polaris/group/getGroupInfoList"),{method:"PUT",data:(0,f.Z)((0,f.Z)({current:1},a),{},{filters:(0,f.Z)({groupSourceTypes:["BUTLER"]},(a==null?void 0:a.filters)||{})})}));case 1:case"end":return n.stop()}},t)}));function e(t){return u.apply(this,arguments)}return e}()}]),l}(),oa=b(14408),la=(0,U.Z)(function l(u){var e=this;(0,O.Z)(this,l),(0,c.Z)(this,"triggerWatchDebounceMap",{}),(0,c.Z)(this,"triggerWatchDebounce",this.triggerWatchDebounceMap),(0,c.Z)(this,"instanceUuid",""),(0,c.Z)(this,"registerEventMap",{}),(0,c.Z)(this,"watchMap",{}),(0,c.Z)(this,"subjects",{}),(0,c.Z)(this,"defaultEventWatchState",{afterInsightsCoreInit:"init",onTagTreeInfoInit:"init"}),(0,c.Z)(this,"defaultEventWatch",[{eventName:"afterInsightsCoreInit",eventType:"init"},{eventName:"onTagTreeInfoInit",eventType:"init"},{eventName:"onTagSelectChange",eventType:"change"},{eventName:"onGroupInfoChange",eventType:"change"}]),(0,c.Z)(this,"registerEvent",function(a){var r=a.namespace,n=a.type,i=a.callback,g="".concat(e.instanceUuid,"/").concat(r,"/").concat(n);e.registerEventMap[g]={callback:i}}),(0,c.Z)(this,"dispatchEvent",function(a){var r=a.namespace,n=a.type,i=a.payload,g="".concat(e.instanceUuid,"/").concat(r,"/").concat(n),s=e.registerEventMap[g].callback,o=s(i);return J(o)?new Promise(function(d,p){o.then(function(h){d(h)}).catch(function(h){p(h)})}):o}),(0,c.Z)(this,"watch",function(a,r){return e.subjects[a]||(e.subjects[a]=new oa.xQ),e.subjects[a].subscribe(r)}),(0,c.Z)(this,"unwatch",function(a,r){e.subjects[a]&&r&&(r.unsubscribe(),e.subjects[a].observers.length===0&&delete e.subjects[a])}),(0,c.Z)(this,"unwatchAll",function(a){e.subjects[a]&&(e.subjects[a].unsubscribe(),delete e.subjects[a])}),(0,c.Z)(this,"triggerWatch",function(a,r){e.subjects[a]&&e.subjects[a].next(r)}),(0,c.Z)(this,"initDefaultEventWatch",function(){e.defaultEventWatch.forEach(function(a){var r=a.eventName;e.watchMap=(0,f.Z)((0,f.Z)({},e.watchMap),{},(0,c.Z)({},r,[])),e.triggerWatchDebounceMap=(0,f.Z)((0,f.Z)({},e.triggerWatchDebounceMap),{},(0,c.Z)({},r,function(n){e.triggerWatch(r,n)}))}),e.triggerWatchDebounce=e.triggerWatchDebounceMap}),(0,c.Z)(this,"changeDefaultEventWatchState",function(a){var r=e.defaultEventWatch.find(function(i){return i.eventName===a});if(r){var n=r.eventType;switch(n){case"init":e.defaultEventWatchState[a]="ready";break;default:break}}});var t=u.instanceUuid;this.instanceUuid=t,this.subjects={},this.initDefaultEventWatch()}),ca=b(23991),ga=b(12322),da=b(94431),fa=b(2592),te;(function(l){function u(a){return!!a&&(0,z.Z)(a)==="object"&&"dispose"in a&&typeof a.dispose=="function"}l.is=u;function e(a){return{dispose:a}}l.create=e;var t=l.NULL=e(function(){})})(te||(te={}));var ya=null,re=b(28839);(0,re.MD)(),(0,re.Fl)(!1);var ne;(function(l){var u=l.EMPTY_VALUE=Symbol("EMPTY_MODEL_VALUE");function e(a){return a!==u}l.isValidValue=e;function t(a){return a.pipe((0,ga.x)(),(0,da.h)(function(r){return e(r)}),(0,fa.p)(17,ca.z,{leading:!1,trailing:!0}))}l.defaultPipeFunction=t})(ne||(ne={}));var Za=null,ba=b(44194),pa=function(u){return u==null?void 0:u.current},ie=function(){var u=React.useRef(!0);return React.useEffect(function(){return function(){u.current=!1}},[]),u},ha=function(u){var e=ie(),t=React.useState(u.getValue()),a=_slicedToArray(t,2),r=a[0],n=a[1];React.useEffect(function(){var o=u.watch(function(d){pa(e)&&n(d)});return function(){o.dispose()}},[n]);var i=r,g=NsModel.isValidValue(i),s=React.useCallback(function(o){return u.setValue(o)},[u]);return[i,s,g]},Da=function(u){var e=React.useMemo(function(){return new RxModel(u)},[]),t=ha(e),a=_slicedToArray(t,3),r=a[0],n=a[1],i=a[2];return React.useEffect(function(){return function(){e.dispose()}},[e]),[r,n,e,i]},Sa=function(u){var e=u.getModel,t=u.initialState,a=ie(),r=React.useRef(),n=React.useState(t),i=_slicedToArray(n,2),g=i[0],s=i[1];return React.useEffect(function(){var o;return e().then(function(){var d=_asyncToGenerator(_regeneratorRuntime.mark(function p(h){var m;return _regeneratorRuntime.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return r.current=h,v.next=3,h.getValidValue();case 3:if(m=v.sent,a.current){v.next=6;break}return v.abrupt("return");case 6:s(m),o=h.watch(function(y){a.current&&s(y)});case 8:case"end":return v.stop()}},p)}));return function(p){return d.apply(this,arguments)}}()),function(){var d;(d=o)===null||d===void 0||d.dispose()}},[]),[g,s,r.current]},va=(0,U.Z)(function l(u){var e=this;(0,O.Z)(this,l),(0,c.Z)(this,"proto",{disabledCache:!1}),(0,c.Z)(this,"utils",G),(0,c.Z)(this,"version",Ye.i8),(0,c.Z)(this,"store",sa),(0,c.Z)(this,"service",new ua),(0,c.Z)(this,"groupInfo",{}),(0,c.Z)(this,"instanceUuid",this.utils.genUuid(8,16)),(0,c.Z)(this,"event",new la({instanceUuid:this.instanceUuid})),(0,c.Z)(this,"tagTreeData",[]),(0,c.Z)(this,"tagInfoList",[]),(0,c.Z)(this,"observerModel",{tagRelations:[],selectedTagValuesMap:new Map}),(0,c.Z)(this,"tagRelations",[]),(0,c.Z)(this,"selectedTagValuesMap",new Map),(0,c.Z)(this,"selectedTagValuesHashMap",{}),(0,c.Z)(this,"viewId",0),(0,c.Z)(this,"defaultRelation","AND"),(0,c.Z)(this,"genUuid",function(){return e.utils.genUuid(8,16)}),(0,c.Z)(this,"genTagUuid",function(t,a){return e.utils.crc32String("".concat(e.viewId).concat(t).concat(a))}),(0,c.Z)(this,"init",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i;return I().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:r&&(n=r.viewId,i=r.disabledTagTree,e.initViewId(n),i||e.getTagTreeData(n)),e.groupInfo={},e.initSelectedTag(),e.event.triggerWatchDebounce.afterInsightsCoreInit(e);case 4:case"end":return s.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"initViewId",function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;e.viewId=t}),(0,c.Z)(this,"generateTagValueInfo",function(t){var a=t.tagInfo,r=t.tagValueList,n=t.operator,i=a.tagValueMappingList,g=i===void 0?[]:i,s=a.tagFieldType,o=a.tagTypeInternal;if(e.utils.isEnumTagType(s,o)){var d=r.map(function(v){var y=e.utils.findInfoFromList(v,"tagValue",g);return y});return d}if(e.utils.isDetailTagType(s,o)||e.utils.isIdTagType(s,o)){var p=e.utils.filterEmptyNumberList(r),h=e.generatorNumericalValue({numericalValueOperator:n||S.IN,parameterList:p});return[h]}if(e.utils.isNumberTagType(s)){var m=e.utils.filterEmptyNumberList(r),T=e.generatorNumericalValue({numericalValueOperator:e.utils.adjustBetweenOperatorByTagValue(r,n),parameterList:m});return[T]}return[]}),(0,c.Z)(this,"generatorNumericalValue",function(t){var a=t.numericalValueOperator,r=a===void 0?S.BETWEEN:a,n=t.parameterList,i=e.utils.getNumericalValueOperatorTagValueMap(t),g={parameterList:n,operator:r,tagValue:i,tagValueMap:i};return g}),(0,c.Z)(this,"setTagRelations",function(t){e.tagRelations=Array.isArray(t)?(0,N.Z)(t):[],e.observerModel.tagRelations=(0,N.Z)(e.tagRelations)}),(0,c.Z)(this,"initFromGroupInfo",function(t){if(t!=null&&t.groupId){e.groupInfo=t;var a=t.viewId,r=t.tagFilters,n=t.filterRelations,i=Array.isArray(n)?n:e.utils.jsonParse(n),g=e.utils.jsonParse(r);e.viewId=a,e.setTagRelations(i);var s=e.exchangeTagFilter2SelectedTagValuesMap(g),o=s.selectedTagList,d=s.selectedTagValuesHashMap,p=s.selectedTagValuesMap;e.selectedTagValuesMap=p,e.selectedTagValuesHashMap=d,e.event.triggerWatchDebounce.onGroupInfoChange({insightsFlow:e,tagRelations:i,tagFilters:r,selectedTagList:o,selectedTagValuesHashMap:d,selectedTagValuesMap:p,groupInfo:t})}else e.init(),e.event.triggerWatchDebounce.onGroupInfoChange({insightsFlow:e,groupInfo:t})}),(0,c.Z)(this,"initSelectedTag",function(){e.selectedTagValuesMap=new Map,e.setTagRelations([]),e.selectedTagValuesHashMap={}}),(0,c.Z)(this,"reset",function(){e.init(),e.event.triggerWatchDebounce.onGroupInfoChange({insightsFlow:e}),e.event.triggerWatchDebounce.onTagSelectChange(e)}),(0,c.Z)(this,"resetSelectedTag",function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;e.initSelectedTag(),t||e.event.triggerWatchDebounce.onTagSelectChange(e)}),(0,c.Z)(this,"add",function(t){var a=t;Array.isArray(t)||(a=[t]),a.forEach(function(r){var n=r,i=n.tagInfo,g=n.uuid,s=n.tagValueMappingListItem,o=n.cover,d=i.tagId,p=i.tagName,h=g||e.genTagUuid(d,p),m=e.addSelectedTagValues({tagInfo:i,uuid:h,tagValueMappingListItem:s,cover:o});if(m){var T=m.selectedTagValuesMapItem;e.setSelectedTagValuesMapItemByUuid(h,T)}}),e.syncTagRelationBySelectedTagValueMap(),e.event.triggerWatch("onTagSelectChange",e)}),(0,c.Z)(this,"remove",function(t){var a=t;Array.isArray(t)||(a=[t]),a.forEach(function(r){var n=r,i=n.uuid,g=n.tagValueMappingListItem;e.removeSelectedTagValues({uuid:i,tagValueMappingListItem:g})}),e.syncTagRelationBySelectedTagValueMap(),e.event.triggerWatchDebounce.onTagSelectChange(e)}),(0,c.Z)(this,"generatorGroupRequestBasicParams",function(t){var a=t||{},r=a.groupMode,n=r===void 0?"DYNAMIC":r,i=a.groupModeStaticDate,g=i===void 0?"":i,s=a.groupId,o=s===void 0?-1:s,d=(0,f.Z)({viewId:e.viewId,uniqueId:"qimei",filterRelations:e.tagRelations,tagFilters:e.exchangeSelectedTagValuesMap2TagFilters(e.selectedTagValuesMap),groupMode:n,date:g||"",groupId:o},t);return d}),(0,c.Z)(this,"getInitGroupTagConfigToBdInsights",function(){var t=e.exchangeSelectedTagValuesMap2TagFilters(e.selectedTagValuesMap);return t.reduce(function(a,r){var n=r.tagId,i=r.tagFieldType,g=r.tagTypeInternal,s=r.tagSingleFilterList,o={tagId:n,operator:S.IN,tagValue:[]};if(e.utils.isEnumTagType(i,g))s.forEach(function(m){var T=m.parameterList;o.tagValue=[].concat((0,N.Z)(o.tagValue),(0,N.Z)(T))});else{var d=s[0];if(!d)return a;var p=d.operator,h=d.parameterList;o.operator=p,o.tagValue=h}return a.push(o),a},[])}),(0,c.Z)(this,"saveInsightsGroup",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i,g,s,o,d,p,h,m,T,v;return I().wrap(function(Z){for(;;)switch(Z.prev=Z.next){case 0:if(i=r||{},g=i.groupName,s=g===void 0?"":g,o=i.description,d=o===void 0?"":o,p=i.groupType,h=i.groupAdvancedSetting,m=h===void 0?{}:h,T=e.generatorGroupRequestBasicParams((0,f.Z)((0,f.Z)({},m),{},{groupId:(n=e.groupInfo)===null||n===void 0?void 0:n.groupId})),v=(0,f.Z)((0,f.Z)((0,f.Z)((0,f.Z)((0,f.Z)({},T),r),s?{groupName:s}:{}),d?{description:d}:{}),{},{groupType:p||e.viewId}),!(!v.groupId||v.groupId===-1)){Z.next=7;break}return Z.next=6,e.service.saveGroup((0,f.Z)((0,f.Z)({},v),{},{groupId:void 0}));case 6:return Z.abrupt("return",Z.sent);case 7:return Z.next=9,e.service.editGroup((0,f.Z)((0,f.Z)({},e.groupInfo),v));case 9:return Z.abrupt("return",Z.sent);case 10:case"end":return Z.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"previewInsightsGroup",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i;return I().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return n=e.generatorGroupRequestBasicParams((0,f.Z)({groupId:-1},r)),i=(0,f.Z)({},n),s.next=4,e.service.previewGroupInfo((0,f.Z)({},i));case 4:return s.abrupt("return",s.sent);case 5:case"end":return s.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"getMultidimensionalRequestParams",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i,g,s,o,d,p,h,m;return I().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(n=P()().subtract(7,"days").format("YYYY-MM-DD"),i=e.generatorGroupRequestBasicParams(),g={groupByList:[{tagId:-1,multiValued:0}],dateMode:ve.RANGE,startDate:n,endDate:n,limit:1e3,nativeQuery:!0,updatePeriod:me.DAY,groupCommend:i},s=i.tagFilters,o=s.map(function(y){return y.tagId}),!Array.isArray(r)){v.next=18;break}return d=g.groupByList,p=[],v.next=10,e.store.db.tagDb.findTagInfo(e.viewId,{tagIdList:[].concat((0,N.Z)(r),(0,N.Z)(o))});case 10:if(v.t0=v.sent,v.t0){v.next=13;break}v.t0=[];case 13:return h=v.t0,h.forEach(function(y){var Z=y.tagId,V=y.multiValued,M=y.maxTime;M&&p.push(M),r.includes(Z)&&d.push({tagId:Z,multiValued:V})}),m=e.utils.sortTimeStringsMinToMax(p)[0],m&&(n=P()(m).format("YYYY-MM-DD")),v.abrupt("return",(0,f.Z)((0,f.Z)({},g),{},{groupByList:d,startDate:n,endDate:n}));case 18:return v.abrupt("return",g);case 19:case"end":return v.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"downloadMultidimensionalData",function(){var t=(0,D.Z)(I().mark(function a(r){return I().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",e.getMultidimensionalData((0,f.Z)((0,f.Z)({},r),{},{queryType:"download"})));case 1:case"end":return i.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"getMultidimensionalData",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i,g,s,o,d,p,h;return I().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:return T.next=2,e.getMultidimensionalRequestParams(r.dimensionTagIdList);case 2:if(n=T.sent,i=(0,f.Z)((0,f.Z)({},n),r),r.queryType!=="download"){T.next=9;break}return T.next=7,e.service.downloadMultidimensionalData(i);case 7:return g=T.sent,T.abrupt("return",g);case 9:return s=function(y){var Z=y.data,V=Z.columnList,M=Z.resultList,A=e.utils.formatterDataSource(V,M),E=A.columnListMap,se=A.dataSource,R=(0,f.Z)((0,f.Z)({},y),{},{columnListMap:E,dataSource:se});return R},T.next=12,e.store.db.analysisDb.findAnalysisQueryData(i);case 12:if(o=T.sent,!o){T.next=16;break}return e.service.getMultidimensionalData(i).then(function(){var v=(0,D.Z)(I().mark(function y(Z){var V,M;return I().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:if(V=Z.code,M=Z.data,V!=="0"){E.next=5;break}return e.store.db.analysisDb.saveAnalysisQueryData(i,M),E.next=5,s(Z);case 5:case"end":return E.stop()}},y)}));return function(y){return v.apply(this,arguments)}}()),T.abrupt("return",s({code:"0",data:o}));case 16:return T.next=18,e.service.getMultidimensionalData(i);case 18:if(d=T.sent,p=d.code,h=d.data,p!=="0"){T.next=23;break}return e.store.db.analysisDb.saveAnalysisQueryData(i,h),T.abrupt("return",s(d));case 23:return T.abrupt("return",(0,f.Z)((0,f.Z)({},d),{},{columnListMap:{},dataSource:[]}));case 24:case"end":return T.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"getInsightInfoMultiGroupByTag",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i,g,s,o,d,p,h,m;return I().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return n=r.tagId,i=r.displayNumber,g=i===void 0?5:i,s=e.groupInfo.groupId||-1,o={groupCommend:e.generatorGroupRequestBasicParams(),viewId:e.viewId,groupId:s,musicIdType:e.utils.getMusicIdType(s)},d={insightGroupCommendList:[o],tagId:n,displayNumber:g},v.next=6,e.service.getInsightInfoMultiGroupByTag(d);case 6:return p=v.sent,h=p.code,m=p.data,v.abrupt("return",p);case 9:case"end":return v.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"getTagTreeData",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i,g,s,o,d;return I().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(n=[],e.proto.disabledCache){h.next=5;break}return h.next=4,e.store.db.tagDb.findTagInfo(e.viewId);case 4:n=h.sent;case 5:if(i=function(T,v){e.tagTreeData=T,e.tagInfoList=v},g=function(){var m=(0,D.Z)(I().mark(function T(v){var y,Z,V,M;return I().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return y=v.tagTreeInfoDescriptors,Z=e.utils.formatTagTreeData(y),V=e.utils.flatTagInfo(Z),M=e.utils.formatTagTreeData(y,!0),E.next=6,e.store.db.tagDb.addTagInfoFromList(V,r);case 6:return E.next=8,e.store.db.tagDb.addTagTreeInfo(M,r);case 8:return i(Z,V),E.abrupt("return",{formatTreeData:Z});case 10:case"end":return E.stop()}},T)}));return function(v){return m.apply(this,arguments)}}(),s=function(){var m=(0,D.Z)(I().mark(function T(v){var y,Z,V,M,A,E;return I().wrap(function(R){for(;;)switch(R.prev=R.next){case 0:return R.next=2,e.service.getTagTree(v);case 2:if(y=R.sent,Z=y,V=Z.code,M=Z.data,V!=="0"){R.next=10;break}return R.next=7,g(M);case 7:A=R.sent,E=A.formatTreeData,y=(0,f.Z)((0,f.Z)({},y),{},{data:E});case 10:return e.event.triggerWatchDebounce.onTagTreeInfoInit(e),R.abrupt("return",y);case 12:case"end":return R.stop()}},T)}));return function(v){return m.apply(this,arguments)}}(),!(n&&n.length>0)){h.next=21;break}return h.next=11,e.store.db.tagDb.findTagTreeInfo(r);case 11:if(o=h.sent,!o){h.next=18;break}return d=e.utils.addTagInfosToTree(o,n),e.service.getTagTree(r).then(function(){var m=(0,D.Z)(I().mark(function T(v){var y,Z;return I().wrap(function(M){for(;;)switch(M.prev=M.next){case 0:if(y=v.code,Z=v.data,y!=="0"){M.next=4;break}return M.next=4,g(Z);case 4:case"end":return M.stop()}},T)}));return function(T){return m.apply(this,arguments)}}()),i(d,n),e.event.triggerWatchDebounce.onTagTreeInfoInit(e),h.abrupt("return",{code:"0",data:d});case 18:return h.next=20,s(r);case 20:return h.abrupt("return",h.sent);case 21:return h.next=23,s(r);case 23:return h.abrupt("return",h.sent);case 24:case"end":return h.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"searchTagInfoFromTagTree",function(t){var a=t.tagName,r=t.tagId,n=e.utils.findLeafNodes(e.tagTreeData,{tagId:r,tagName:a});return n}),(0,c.Z)(this,"searchTagInfoFromTaglist",function(){var t=(0,D.Z)(I().mark(function a(r){var n,i,g;return I().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return n=r.tagNameList,i=r.tagIdList,o.next=3,e.store.db.tagDb.findTagInfo(e.viewId,{tagIdList:i,tagNameList:n});case 3:return g=o.sent,o.abrupt("return",g);case 5:case"end":return o.stop()}},a)}));return function(a){return t.apply(this,arguments)}}()),(0,c.Z)(this,"registerEvent",function(t){var a=t.namespace,r=t.type,n=t.callback;e.event.registerEvent({namespace:a,type:r,callback:n})}),(0,c.Z)(this,"dispatchEvent",function(t){var a=t.namespace,r=t.type,n=t.payload;return e.event.dispatchEvent({namespace:a,type:r,payload:n})}),(0,c.Z)(this,"generatorSelectedTagValuesMapItem",function(t){var a=t.tagInfo,r=t.selectedTagValues,n=t.uuid,i=a.tagName,g=a.tagId,s=a.tagTypeInternal,o=a.tagFieldType,d=a.inverted,p=a.tagIsCustomized,h=a.secondClassName,m=a.multiValued;return{uuid:n,selectedTagValues:r,multiValued:m,inverted:d||0,tagName:i,tagId:g,tagTypeInternal:s,tagFieldType:o,secondClassName:h,isCustomizedTag:a.isCustomizedTag||p}}),(0,c.Z)(this,"isShiAndFou",function(t){if(!e.utils.isArrayOfValues(t))return!1;var a=t.map(function(r){return r.tagValue});return!!(a.length===2&&a.includes("\u662F")&&a.includes("\u5426"))}),(0,c.Z)(this,"getSelectedTagValuesMapItemByUuid",function(t){return e.selectedTagValuesMap.get(t)}),(0,c.Z)(this,"setSelectedTagValuesMapItemByUuid",function(t,a){e.selectedTagValuesMap.set(t,a)}),(0,c.Z)(this,"deleteSelectedTagValuesMapItemByUuid",function(t){e.selectedTagValuesMap.delete(t)}),(0,c.Z)(this,"syncTagRelationBySelectedTagValueMap",function(){var t=e.selectedTagValuesMap.size-1,a=e.tagRelations.length;if(t!==a){if(t>a){var r=t-a;e.batchTagRelation(r,function(){e.setTagRelations([].concat((0,N.Z)(e.tagRelations),[e.defaultRelation]))})}if(t<a){var n=a-t;e.batchTagRelation(n,function(){e.setTagRelations((0,N.Z)(e.tagRelations).slice(0,-1))})}}}),(0,c.Z)(this,"batchTagRelation",function(t,a){for(var r=0;r<t;r++)a()}),(0,c.Z)(this,"initTagRelation",function(){var t=e.selectedTagValuesMap.size-1;e.batchTagRelation(t,function(){e.setTagRelations([].concat((0,N.Z)(e.tagRelations),[e.defaultRelation]))})}),(0,c.Z)(this,"addTagRelation",function(t){e.setTagRelations([].concat((0,N.Z)(e.tagRelations),[t]))}),(0,c.Z)(this,"removeTagRelation",function(t){var a=q()(e.tagRelations,{$splice:[[t,1]]});return a}),(0,c.Z)(this,"updateTagRelation",function(t,a){q()(e.tagRelations,{$splice:[[t-1,1,a]]})}),(0,c.Z)(this,"exchangeSelectedTagValuesMap2TagFilters",function(t){var a=[];return t.forEach(function(r){var n=r,i=n,g=i.selectedTagValues,s=i.relation;a.push((0,f.Z)((0,f.Z)({},n),{},{relation:s||"OR",tagSingleFilterList:e.exchangeSelectedTagValues2TagSingleFilterList(g)}))}),a}),(0,c.Z)(this,"addSelectedTagValues",function(t){var a,r=t.tagInfo,n=t.uuid,i=t.tagValueMappingListItem,g=t.cover;if(!(!i||(i==null?void 0:i.tagValue)===void 0)){var s=(a=e.selectedTagValuesHashMap[n])===null||a===void 0?void 0:a.selectedTagValues;s||(e.selectedTagValuesHashMap[n]={selectedTagValues:{}},s={}),g?s=(0,c.Z)({},i.tagValue,i):s[i.tagValue]=i,e.selectedTagValuesHashMap[n].selectedTagValues=s;var o=e.generatorSelectedTagValuesMapItem({uuid:n,tagInfo:r,selectedTagValues:s});return{uuid:n,selectedTagValuesMapItem:o}}}),(0,c.Z)(this,"removeSelectedTagValues",function(t){var a=t.uuid,r=t.tagValueMappingListItem,n=e.getSelectedTagValuesMapItemByUuid(a);if(!n){console.warn("\u5220\u9664selectedTagValues\u5931\u8D25, \u5F53\u524D\u76EE\u6807\u5BF9\u8C61\u5728Map\u4E2D\u4E0D\u5B58\u5728");return}var i=e.selectedTagValuesHashMap[a].selectedTagValues;delete i[r.tagValue];var g=Object.keys(i),s=g.length;if(s===0){var o=e.utils.getMapKeyIndex(a,e.selectedTagValuesMap);e.removeTagRelation(o),e.deleteSelectedTagValuesMapItemByUuid(a)}else e.setSelectedTagValuesMapItemByUuid(a,(0,f.Z)((0,f.Z)({},n),{},{selectedTagValues:i}))}),(0,c.Z)(this,"exchangeTagFilter2SelectedTagValuesMap",function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"tagSingleFilterList",r=[],n={},i=new Map;return t&&Array.isArray(t)&&t.forEach(function(g){var s=g.tagId,o=g.tagName,d=g[a];r.push(s);var p={};d&&Array.isArray(d)&&d.forEach(function(m){var T=m.tagValueId,v=m.value,y=m.parameterList,Z=m.operator;p[T]=(0,f.Z)({id:T,tagName:o,operator:Z,tagValue:v,parameterList:y},m)});var h=g.uuid||e.genTagUuid(s,o);i.set(h,(0,f.Z)((0,f.Z)({},g),{},{uuid:h,selectedTagValues:p})),n[h]={selectedTagValues:p}}),{selectedTagValuesMap:i,selectedTagValuesHashMap:n,selectedTagList:r}}),(0,c.Z)(this,"exchangeSelectedTagValues2TagSingleFilterList",function(t){return Object.keys(t).map(function(a){var r=t[a],n=r,i=n.operator,g=n.parameterList,s=n.tagValue,o=n.isCustomizedTagValue,d=n.tagCustomizedValueId,p=n.originData,h=n.queryTagId,m=n.queryId,T=r.isCustomizedTag||(d?1:0);if(o){var v=p.tagValueId,y=p.valueName,Z=p.tagCustomizedValueSingleCommend,V=Z.parameterList,M=Z.numericalValueOperator;return{isCustomizedTagValue:1,tagValueId:v,value:y,parameterList:V,operator:M,tagCustomizedValueId:d,isCustomizedTag:T,queryTagId:h,queryId:m}}return{isCustomizedTagValue:0,tagValueId:s==null?void 0:s.toString(),value:s==null?void 0:s.toString(),parameterList:g||[s==null?void 0:s.toString()],operator:i||S.EQUAL,tagCustomizedValueId:d,isCustomizedTag:T,queryTagId:h,queryId:m}})}),u&&(this.proto=(0,f.Z)((0,f.Z)({},u),this.proto)),this.init(u),window._INSIGHS_FLOW={version:this.version}})}}]);
