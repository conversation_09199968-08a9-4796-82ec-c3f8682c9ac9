import IconFont from '../../components/IconFont';
import type { MenuProps } from 'antd';
import { getTextWidth, groupByColumn, isMobile } from '../../utils/utils';
import { AutoComplete, Select, Tag, Tooltip, Dropdown, Upload, message } from 'antd';
import classNames from 'classnames';
import { debounce, size } from 'lodash';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type { ForwardRefRenderFunction } from 'react';
import { SemanticTypeEnum, SEMANTIC_TYPE_MAP, HOLDER_TAG } from '../constants';
import { AgentType, ModelType } from '../type';
import { searchRecommend, difyUploadFiles } from '../../service';
import styles from './style.module.less';
import { useComposing } from '../../hooks/useComposing';
import { BorderOutlined, DatabaseOutlined, ProductOutlined, UploadOutlined, AudioOutlined } from '@ant-design/icons';
import { useStreaming } from '../../context/StreamingContext';
import DataViewModel from './DataViewModel';
import FileUploadArea from './FileUploadArea';
import { UploadFile, UploadStatus, SendFileData, UploadResponse, generateUID, getFileExtension } from './types';
import kjtwImg from '../../assets/kjtw.png'
import dataViewImg from '../../assets/dataView.png'
import React from 'react'
import SpeechToText from '../components/SpeechToText'

type Props = {
  inputMsg: string;
  chatId?: number;
  currentAgent?: AgentType;
  agentList: AgentType[];
  onToggleHistoryVisible: () => void;
  onOpenAgents: () => void;
  onInputMsgChange: (value: string) => void;
  onSendMsg: (msg: string, dataSetId?: number, files?: SendFileData[]) => void;
  onAddConversation: (agent?: AgentType) => void;
  onSelectAgent: (agent: AgentType) => void;
  onOpenShowcase: () => void;
  // 新增props
  isLUIAgent?: boolean;
  inputCentered?: boolean;
};

const { OptGroup, Option } = Select;
let isPinyin = false;
let isSelect = false;

const compositionStartEvent = () => {
  isPinyin = true;
};

const compositionEndEvent = () => {
  isPinyin = false;
};

const ChatFooter: ForwardRefRenderFunction<any, Props> = (
  {
    inputMsg,
    chatId,
    currentAgent,
    agentList,
    onToggleHistoryVisible,
    onOpenAgents,
    onInputMsgChange,
    onSendMsg,
    onAddConversation,
    onSelectAgent,
    onOpenShowcase,
    isLUIAgent,
    inputCentered,
  },
  ref
) => {
  const [modelOptions, setModelOptions] = useState<(ModelType | AgentType)[]>([]);
  const [stepOptions, setStepOptions] = useState<Record<string, any[]>>({});
  const [open, setOpen] = useState(false);
  const [focused, setFocused] = useState(false);
  const [dataModelVisible, setDataModelVisible] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadFile[]>([]);
  const inputRef = useRef<any>();
  const fetchRef = useRef(0);
  const uploadAbortControllers = useRef<Map<string, AbortController>>(new Map());
  const { state: streamingState, dispatch: streamingDispatch } = useStreaming();

  const inputFocus = () => {
    inputRef.current?.focus();
  };

  const inputBlur = () => {
    inputRef.current?.blur();
  };

  useImperativeHandle(ref, () => ({
    inputFocus,
    inputBlur,
  }));

  const initEvents = () => {
    const autoCompleteEl = document.getElementById('chatInput');
    autoCompleteEl!.addEventListener('compositionstart', compositionStartEvent);
    autoCompleteEl!.addEventListener('compositionend', compositionEndEvent);
  };

  const removeEvents = () => {
    const autoCompleteEl = document.getElementById('chatInput');
    if (autoCompleteEl) {
      autoCompleteEl.removeEventListener('compositionstart', compositionStartEvent);
      autoCompleteEl.removeEventListener('compositionend', compositionEndEvent);
    }
  };

  useEffect(() => {
    initEvents();
    return () => {
      removeEvents();
    };
  }, []);

  const getStepOptions = (recommends: any[]) => {
    const data = groupByColumn(recommends, 'dataSetName');
    return isMobile && recommends.length > 6
      ? Object.keys(data)
          .slice(0, 4)
          .reduce((result, key) => {
            result[key] = data[key].slice(
              0,
              Object.keys(data).length > 2 ? 2 : Object.keys(data).length > 1 ? 3 : 6
            );
            return result;
          }, {})
      : data;
  };

  const processMsg = (msg: string) => {
    let msgValue = msg;
    let dataSetId: number | undefined;
    if (msg?.[0] === '/') {
      const agent = agentList.find(item => msg.includes(`/${item.name}`));
      msgValue = agent ? msg.replace(`/${agent.name}`, '') : msg;
    }
    return { msgValue, dataSetId };
  };

  const debounceGetWordsFunc = useCallback(() => {
    const getAssociateWords = async (msg: string, chatId?: number, currentAgent?: AgentType) => {
      if (isPinyin) {
        return;
      }
      if (msg === '' || (msg.length === 1 && msg[0] === '@')) {
        return;
      }
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      const { msgValue, dataSetId } = processMsg(msg);
      const res = await searchRecommend(msgValue.trim(), chatId, dataSetId, currentAgent?.id);
      if (fetchId !== fetchRef.current) {
        return;
      }
      const recommends = msgValue ? res.data || [] : [];
      const stepOptionList = recommends.map((item: any) => item.subRecommend);
      if (stepOptionList.length > 0 && stepOptionList.every((item: any) => item !== null)) {
        setStepOptions(getStepOptions(recommends));
      } else {
        setStepOptions({});
      }
      setOpen(recommends.length > 0);
    };
    return debounce(getAssociateWords, 200);
  }, []);

  const [debounceGetWords] = useState<any>(debounceGetWordsFunc);

  useEffect(() => {
    if (inputMsg.length === 1 && inputMsg[0] === '/') {
      setOpen(true);
      setModelOptions(agentList);
      setStepOptions({});
      return;
    }
    if (modelOptions.length > 0) {
      setTimeout(() => {
        setModelOptions([]);
      }, 50);
    }
    if (!isSelect) {
      debounceGetWords(inputMsg, chatId, currentAgent);
    } else {
      isSelect = false;
    }
    if (!inputMsg) {
      setStepOptions({});
      fetchRef.current = 0;
    }
  }, [inputMsg]);

  useEffect(() => {
    if (!focused) {
      setOpen(false);
    }
  }, [focused]);

  useEffect(() => {
    const autoCompleteDropdown = document.querySelector(
      `.${styles.autoCompleteDropdown}`
    ) as HTMLElement;
    if (!autoCompleteDropdown) {
      return;
    }
    const textWidth = getTextWidth(inputMsg);
    if (Object.keys(stepOptions).length > 0) {
      autoCompleteDropdown.style.marginLeft = `${textWidth}px`;
    } else {
      setTimeout(() => {
        autoCompleteDropdown.style.marginLeft = `0px`;
      }, 200);
    }
  }, [stepOptions]);

  const sendMsg = (value: string) => {
    const option = Object.keys(stepOptions)
      .reduce((result: any[], item) => {
        result = result.concat(stepOptions[item]);
        return result;
      }, [])
      .find(item =>
        Object.keys(stepOptions).length === 1
          ? item.recommend === value
          : `${item.dataSetName || ''}${item.recommend}` === value
      );

    // 获取文件数据
    const fileData = getUploadedFileData();

    if (option && isSelect) {
      onSendMsg(option.recommend, option.dataSetIds, fileData);
    } else {
      onSendMsg(value.trim(), option?.dataSetId, fileData);
    }

    // 发送后清空文件列表
    setUploadedFiles([]);
  };

  // 创建上传文件对象
  const createUploadFile = (file: File): UploadFile => {
    const uid = generateUID();
    const extension = getFileExtension(file.name);

    return {
      uid,
      name: file.name,
      size: file.size,
      type: file.type,
      extension,
      status: UploadStatus.UPLOADING,
      progress: 0,
      file
    };
  };

  // 文件上传处理
  const handleFileUpload = async (uploadFile: UploadFile) => {
    const abortController = new AbortController();
    uploadAbortControllers.current.set(uploadFile.uid, abortController);

    let progressInterval: NodeJS.Timeout | null = null;

    try {
      const formData = new FormData();
      formData.append('file', uploadFile.file);
      formData.append('user', localStorage.getItem('user') || '');

      // 模拟上传进度
      progressInterval = setInterval(() => {
        setUploadedFiles(prev =>
          prev.map(f =>
            f.uid === uploadFile.uid
              ? { ...f, progress: Math.min(f.progress + 10, 90) }
              : f
          )
        );
      }, 200);

      const response = await difyUploadFiles(formData);
      if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
      }

      // 检查响应是否成功
      // 处理两种可能的响应格式：axios响应对象或直接的数据对象
      console.log('uploadData',response);

      // 提取实际的上传数据
      const uploadData = (response as any).data || response;

      if (!uploadData || !uploadData.id) {
        throw new Error('上传响应无效');
      }

      // 更新为解析状态
      setUploadedFiles(prev =>
        prev.map(f =>
          f.uid === uploadFile.uid
            ? { ...f, status: UploadStatus.PARSING, progress: 100 }
            : f
        )
      );

      // 模拟解析时间
      setTimeout(() => {
        setUploadedFiles(prev =>
          prev.map(f =>
            f.uid === uploadFile.uid
              ? { ...f, status: UploadStatus.SUCCESS, uploadResponse: uploadData }
              : f
          )
        );
        message.success('文件上传成功');
      }, 1000);

    } catch (error: any) {
      // 清除进度条
      if (progressInterval) {
        clearInterval(progressInterval);
      }

      // 判断错误类型
      let errorMessage = '上传失败';
      if (error.name === 'AbortError') {
        errorMessage = '上传已取消';
      } else if (error.message?.includes('Network')) {
        errorMessage = '网络错误，请检查网络连接';
      } else if (error.message?.includes('timeout')) {
        errorMessage = '上传超时，请重试';
      } else if (error.response?.status === 413) {
        errorMessage = '文件过大，请选择较小的文件';
      } else if (error.response?.status === 415) {
        errorMessage = '文件格式不支持';
      }

      setUploadedFiles(prev =>
        prev.map(f =>
          f.uid === uploadFile.uid
            ? { ...f, status: UploadStatus.ERROR, error: errorMessage }
            : f
        )
      );

      if (error.name !== 'AbortError') {
        message.error(errorMessage);
      }
      console.error('文件上传失败:', error);
    } finally {
      uploadAbortControllers.current.delete(uploadFile.uid);
    }
  };

  // 文件类型检查
  const isValidFileType = (file: File): boolean => {
    const validTypes = [
      'text/plain',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    // 检查MIME类型
    if (validTypes.includes(file.type)) {
      return true;
    }

    // 检查文件扩展名（作为备用检查）
    const extension = getFileExtension(file.name);
    return ['txt', 'xls', 'xlsx'].includes(extension);
  };

  // 文件上传前的校验
  const beforeUpload = (file: File) => {
    // 检查文件类型
    if (!isValidFileType(file)) {
      message.error('只能上传 txt 或 excel 文件!');
      return false;
    }

    // 检查文件大小
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB!');
      return false;
    }

    // 检查是否已经上传了相同的文件
    const isDuplicate = uploadedFiles.some(f =>
      f.name === file.name && f.size === file.size
    );
    if (isDuplicate) {
      message.warning('该文件已经上传过了!');
      return false;
    }

    // 创建上传文件对象并添加到列表
    const uploadFile = createUploadFile(file);
    setUploadedFiles(prev => [...prev, uploadFile]);

    // 开始上传
    handleFileUpload(uploadFile);

    return false; // 阻止默认上传行为
  };

  // 取消/删除文件
  const handleCancelFile = (uid: string) => {
    const file = uploadedFiles.find(f => f.uid === uid);
    if (!file) return;

    // 如果正在上传，取消上传请求
    const abortController = uploadAbortControllers.current.get(uid);
    if (abortController) {
      abortController.abort();
      uploadAbortControllers.current.delete(uid);
    }

    // 从列表中移除文件
    setUploadedFiles(prev => prev.filter(f => f.uid !== uid));
  };

  // 重试上传
  const handleRetryFile = (uid: string) => {
    const file = uploadedFiles.find(f => f.uid === uid);
    if (!file) return;

    // 重置文件状态
    const resetFile = {
      ...file,
      status: UploadStatus.UPLOADING,
      progress: 0,
      error: undefined
    };

    setUploadedFiles(prev =>
      prev.map(f => f.uid === uid ? resetFile : f)
    );

    // 重新上传
    handleFileUpload(resetFile);
  };

  // 获取成功上传的文件数据
  const getUploadedFileData = (): SendFileData[] => {
    return uploadedFiles
      .filter(file => file.status === UploadStatus.SUCCESS && file.uploadResponse)
      .map(file => ({
        type: 'document',
        transfer_method: 'local_file',
        upload_file_id: file.uploadResponse!.id
      }));
  };

  const autoCompleteDropdownClass = classNames(styles.autoCompleteDropdown, {
    [styles.mobile]: isMobile,
    [styles.modelOptions]: modelOptions.length > 0,
  });

  const onSelect = (value: string) => {
    isSelect = true;
    if (modelOptions.length > 0) {
      const agent = agentList.find(item => value.includes(item.name));
      if (agent) {
        if (agent.id !== currentAgent?.id) {
          onSelectAgent(agent);
        }
        onInputMsgChange('');
      }
    } else {
      onInputMsgChange(value.replace(HOLDER_TAG, ''));
    }
    setOpen(false);
    setTimeout(() => {
      isSelect = false;
    }, 200);
  };

  const chatFooterClass = classNames(styles.chatFooter, {
    [styles.mobile]: isMobile,
    [styles.centeredInput]: isLUIAgent && inputCentered,
  });

  const modelOptionNodes = modelOptions.map(model => {
    return (
      <Option key={model.id} value={`/${model.name} `} className={styles.searchOption}>
        {model.name}
      </Option>
    );
  });

  const associateOptionNodes = Object.keys(stepOptions).map(key => {
    return (
      <OptGroup key={key} label={key}>
        {stepOptions[key].map(option => {
          let optionValue =
            Object.keys(stepOptions).length === 1
              ? option.recommend
              : `${option.dataSetName || ''}${option.recommend}`;
          if (inputMsg[0] === '/') {
            const agent = agentList.find(item => inputMsg.includes(item.name));
            optionValue = agent ? `/${agent.name} ${option.recommend}` : optionValue;
          }
          return (
            <Option
              key={`${option.recommend}${option.dataSetName ? `_${option.dataSetName}` : ''}`}
              value={`${optionValue}${HOLDER_TAG}`}
              className={styles.searchOption}
            >
              <div className={styles.optionContent}>
                {option.schemaElementType && (
                  <Tag
                    className={styles.semanticType}
                    color={
                      option.schemaElementType === SemanticTypeEnum.DIMENSION ||
                      option.schemaElementType === SemanticTypeEnum.MODEL
                        ? 'blue'
                        : option.schemaElementType === SemanticTypeEnum.VALUE
                        ? 'geekblue'
                        : 'cyan'
                    }
                  >
                    {SEMANTIC_TYPE_MAP[option.schemaElementType] ||
                      option.schemaElementType ||
                      '维度'}
                  </Tag>
                )}
                {option.subRecommend}
              </div>
            </Option>
          );
        })}
      </OptGroup>
    );
  });

  const fixWidthBug = () => {
    setTimeout(() => {
      const dropdownDom = document.querySelector(
        '.' + styles.autoCompleteDropdown + ' .rc-virtual-list-holder-inner'
      );

      if (!dropdownDom) {
        fixWidthBug();
      } else {
        // 获取popoverDom样式
        const popoverDomStyle = window.getComputedStyle(dropdownDom);
        // 在获取popoverDom中增加样式 width: fit-content
        dropdownDom.setAttribute('style', `${popoverDomStyle.cssText};width: fit-content`);
        // 获取popoverDom的宽度
        const popoverDomWidth = dropdownDom.clientWidth;
        // 将popoverDom的宽度赋值给他的父元素
        const offset = 20; // 预增加20px的宽度，预留空间给虚拟渲染出来的元素
        dropdownDom.parentElement!.style.width = popoverDomWidth + offset + 'px';
      }
    });
  };

  useEffect(() => {
    if (modelOptionNodes.length || associateOptionNodes.length) fixWidthBug();
  }, [modelOptionNodes.length, associateOptionNodes.length]);

  const { isComposing } = useComposing(document.getElementById('chatInput'));

  const onOpenShowDataModel = () => {
    setDataModelVisible(true);
  };

  // 将点击的文字填入到AutoComplete输入框中
  const onFillInputWithText = (text: string) => {
    onInputMsgChange(text);
    setDropdownVisible(false);
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        // 将光标定位到文本末尾
        const inputElement = document.getElementById('chatInput') as HTMLInputElement;
        if (inputElement) {
          inputElement.setSelectionRange(text.length, text.length);
        }
      }
    }, 100);
  };

  const [isInputCentered, setIsInputCentered] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    const el = document.querySelector(`.${styles.composer}`);
    if (!el) return;

    const observer = new ResizeObserver(() => {
      const rect = el.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      setIsInputCentered(rect.top < windowHeight / 2 && rect.bottom > windowHeight / 2);
    });

    observer.observe(el);
    return () => observer.disconnect();
  }, []);

  return (
    <div className={chatFooterClass}>
      {(isLUIAgent && inputCentered) && (
        <div className={styles.agentTitle}>
          XX人工智能LUI交互平台
        </div>
      )
      }
      {/* 只有非LUI智能体或输入框不居中时才显示工具栏 */}
      {(!isLUIAgent || !inputCentered) && (
        <div className={styles.tools}>
        <div className={styles.toolItem}>
          <Dropdown
            menu={{
              items: currentAgent!.examples?.length > 0
                ? currentAgent!.examples.map((example, index) => ({
                    key: index,
                    label: example,
                    className: styles.searchOption,
                    onClick: () => onFillInputWithText(example)
                  }))
                : [{
                    key: 'no-examples',
                    label: currentAgent?.description || '暂无快捷提问示例',
                    className: styles.searchOption,
                    disabled: true
                  }],
              style: {
                maxHeight: '400px',
                overflowY: 'auto'
              }
            }}
            trigger={['click']}
            placement="topLeft"
            getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
            arrow
            open={dropdownVisible}
            onOpenChange={setDropdownVisible}
          >
            <div className={styles.toolItem}>
              <img src={kjtwImg} className={styles.toolIconImg} alt="快捷提问" />
              <span>快捷提问</span>
            </div>
          </Dropdown>
        </div>
        <div className={styles.toolItem} onClick={onOpenShowDataModel}>
          <img src={dataViewImg} className={styles.toolIconImg} alt="数据预览" />
          <div>数据预览</div>
        </div>
        {/* <div
          className={styles.toolItem}
          onClick={() => {
            onAddConversation();
          }}
        >
          <IconFont type="icon-c003xiaoxiduihua" className={styles.toolIcon} />
          <div>新对话</div>
        </div> */}
        {/* {!isMobile && (
          <div className={styles.toolItem} onClick={onToggleHistoryVisible}>
            <IconFont type="icon-lishi" className={styles.toolIcon} />
            <div>历史对话</div>
          </div>
        )} */}
        {/* {agentList?.length > 1 && (
          <div className={styles.toolItem} onClick={onOpenAgents}>
            <IconFont type="icon-zhinengzhuli" className={styles.toolIcon} />
            <div>智能体</div>
          </div>
        )} */}
        {/* <div className={styles.toolItem} onClick={onOpenShowcase}>
          <IconFont type="icon-showcase" className={styles.toolIcon} />
          <div>showcase</div>
        </div> */}
      </div>
      )}

      <div className={styles.composer}>
        {/* 文件上传区域 */}
        <FileUploadArea
          files={uploadedFiles}
          onCancelFile={handleCancelFile}
          onRetryFile={handleRetryFile}
        />

        <div className={styles.composerInputWrapper}>
          <AutoComplete
              className={`${styles.composerInput}`}
            placeholder={
              !currentAgent?.name.includes('LUI')
                ? `【${currentAgent.name}】将与您对话，点击${!isMobile ? '左侧' : ''}【智能体】${
                    !isMobile ? '下拉列表' : ''
                  }可切换`
                : 'XX人工智能LUI交互平台将与您对话，您有什么问题随时提问！'
            }
            value={inputMsg}
            disabled={streamingState.isStreaming}
            onChange={(value: string) => {
              onInputMsgChange(value);
            }}
            onSelect={onSelect}
            autoFocus={!isMobile}
            ref={inputRef}
            id="chatInput"
            onKeyDown={e => {
              if (e.code === 'Enter' || e.code === 'NumpadEnter') {
                const chatInputEl: any = document.getElementById('chatInput');
                const agent = agentList.find(
                  item => chatInputEl.value[0] === '/' && chatInputEl.value.includes(item.name)
                );
                if (agent) {
                  if (agent.id !== currentAgent?.id) {
                    onSelectAgent(agent);
                  }
                  onInputMsgChange('');
                  return;
                }
                if (!isSelect && !isComposing) {
                  sendMsg(chatInputEl.value);
                  setOpen(false);
                }
              }
            }}
            onFocus={() => {
              setFocused(true);
            }}
            onBlur={() => {
              setFocused(false);
            }}
            classNames={{ popup: { root: autoCompleteDropdownClass } }}
            listHeight={500}
            open={open}
            defaultActiveFirstOption={false}
            getPopupContainer={triggerNode => triggerNode.parentNode}
          >
            {modelOptions.length > 0 ? modelOptionNodes : associateOptionNodes}
          </AutoComplete>

          {currentAgent?.name.includes('LUI') && (
            <div className={styles.audioBtn}>
                {/*<AudioOutlined />*/}
                <SpeechToText
                  onResult={(result) => onInputMsgChange(result)}
                  onRecordingStateChange={setIsRecording}
                />
              </div>
          )}

          {/* 语音识别动效 */}
          {isRecording && (
            <div className={styles.voiceRecordingEffect}>
              <div className={styles.recordingText}>正在识别语音</div>
              <div className={styles.voiceWave}>
                <div className={styles.waveBar}></div>
                <div className={styles.waveBar}></div>
                <div className={styles.waveBar}></div>
                <div className={styles.waveBar}></div>
                <div className={styles.waveBar}></div>
              </div>
            </div>
          )}


          {currentAgent?.enableFileUpload === 1 && (
            <Upload
              beforeUpload={beforeUpload}
              showUploadList={false}
              accept=".txt,.xls,.xlsx"
            >
              <Tooltip title="上传文件,文件类型为: txt/excel" placement="top" mouseEnterDelay={0.3}>
                <div className={styles.uploadBtn}>
                  <UploadOutlined />
                </div>
              </Tooltip>
            </Upload>
          )}
          {streamingState.isStreaming ? (
            <Tooltip title="停止生成" placement="top" mouseEnterDelay={0.3}>
              <div
                className={styles.pauseBtn}
                onClick={() => streamingDispatch({ type: 'STOP_STREAMING' })}
              >
                <BorderOutlined />
              </div>
            </Tooltip>
          ) : (
            <div
              className={classNames(styles.sendBtn, {
                [styles.sendBtnActive]: inputMsg?.length > 0,
              })}
              onClick={() => {
                sendMsg(inputMsg);
              }}
            >
              <IconFont type="icon-ios-send" />
            </div>
          )}
        </div>
        {
          (!isLUIAgent || !inputCentered) && (
            <div className={styles.footerAiTip}>内容由 AI 生成，请仔细甄别</div>
          )
        }
      </div>
      <DataViewModel
        currentAgent={currentAgent}
        dataModelVisible={dataModelVisible}
        onCancel={() => {
          setDataModelVisible(false);
        }}
      />
    </div>
  );
};

export default forwardRef(ChatFooter);
