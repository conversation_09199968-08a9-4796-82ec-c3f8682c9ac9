"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[211],{22317:function(_,N,C){C.d(N,{R_:function(){return O}});var m=C(94224),g=2,D=.16,Z=.05,z=.05,G=.15,H=5,E=4,I=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function B(n,c,p){var e;return Math.round(n.h)>=60&&Math.round(n.h)<=240?e=p?Math.round(n.h)-g*c:Math.round(n.h)+g*c:e=p?Math.round(n.h)+g*c:Math.round(n.h)-g*c,e<0?e+=360:e>=360&&(e-=360),e}function P(n,c,p){if(n.h===0&&n.s===0)return n.s;var e;return p?e=n.s-D*c:c===E?e=n.s+D:e=n.s+Z*c,e>1&&(e=1),p&&c===H&&e>.1&&(e=.1),e<.06&&(e=.06),Number(e.toFixed(2))}function k(n,c,p){var e;return p?e=n.v+z*c:e=n.v-G*c,e>1&&(e=1),Number(e.toFixed(2))}function O(n){for(var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},p=[],e=(0,m.Z)(n),r=H;r>0;r-=1){var t=e.toHsv(),o=(0,m.Z)({h:B(t,r,!0),s:P(t,r,!0),v:k(t,r,!0)}).toHexString();p.push(o)}p.push(e.toHexString());for(var a=1;a<=E;a+=1){var l=e.toHsv(),x=(0,m.Z)({h:B(l,a),s:P(l,a),v:k(l,a)}).toHexString();p.push(x)}return c.theme==="dark"?I.map(function(S){var y=S.index,h=S.opacity,J=m.Z.mix(c.backgroundColor||"#141414",p[y],h*100).toHexString();return J}):p}var w={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1890FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},u={},s={};Object.keys(w).forEach(function(n){u[n]=O(w[n]),u[n].primary=u[n][5],s[n]=O(w[n],{theme:"dark",backgroundColor:"#141414"}),s[n].primary=s[n][5]});var F=u.red,M=u.volcano,d=u.gold,A=u.orange,K=u.yellow,L=u.lime,T=u.green,U=u.cyan,V=u.blue,W=u.geekblue,R=u.purple,$=u.magenta,q=u.grey},26274:function(_,N,C){C.d(N,{R_:function(){return F}});var m=C(20117),g=C(76981),D=2,Z=.16,z=.05,G=.05,H=.15,E=5,I=4,B=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function P(r){var t=r.r,o=r.g,a=r.b,l=(0,m.py)(t,o,a);return{h:l.h*360,s:l.s,v:l.v}}function k(r){var t=r.r,o=r.g,a=r.b;return"#".concat((0,m.vq)(t,o,a,!1))}function O(r,t,o){var a=o/100,l={r:(t.r-r.r)*a+r.r,g:(t.g-r.g)*a+r.g,b:(t.b-r.b)*a+r.b};return l}function w(r,t,o){var a;return Math.round(r.h)>=60&&Math.round(r.h)<=240?a=o?Math.round(r.h)-D*t:Math.round(r.h)+D*t:a=o?Math.round(r.h)+D*t:Math.round(r.h)-D*t,a<0?a+=360:a>=360&&(a-=360),a}function u(r,t,o){if(r.h===0&&r.s===0)return r.s;var a;return o?a=r.s-Z*t:t===I?a=r.s+Z:a=r.s+z*t,a>1&&(a=1),o&&t===E&&a>.1&&(a=.1),a<.06&&(a=.06),Number(a.toFixed(2))}function s(r,t,o){var a;return o?a=r.v+G*t:a=r.v-H*t,a>1&&(a=1),Number(a.toFixed(2))}function F(r){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=[],a=(0,g.uA)(r),l=E;l>0;l-=1){var x=P(a),S=k((0,g.uA)({h:w(x,l,!0),s:u(x,l,!0),v:s(x,l,!0)}));o.push(S)}o.push(k(a));for(var y=1;y<=I;y+=1){var h=P(a),J=k((0,g.uA)({h:w(h,y),s:u(h,y),v:s(h,y)}));o.push(J)}return t.theme==="dark"?B.map(function(i){var v=i.index,b=i.opacity,f=k(O((0,g.uA)(t.backgroundColor||"#141414"),(0,g.uA)(o[v]),b*100));return f}):o}var M={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1890FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},d={},A={};Object.keys(M).forEach(function(r){d[r]=F(M[r]),d[r].primary=d[r][5],A[r]=F(M[r],{theme:"dark",backgroundColor:"#141414"}),A[r].primary=A[r][5]});var K=d.red,L=d.volcano,T=d.gold,U=d.orange,V=d.yellow,W=d.lime,R=d.green,$=d.cyan,q=d.blue,n=d.geekblue,c=d.purple,p=d.magenta,e=d.grey},64970:function(_,N,C){C.r(N),C.d(N,{blue:function(){return T},blueDark:function(){return l},cyan:function(){return L},cyanDark:function(){return a},geekblue:function(){return U},geekblueDark:function(){return x},generate:function(){return O},gold:function(){return M},goldDark:function(){return e},gray:function(){return $},green:function(){return K},greenDark:function(){return o},grey:function(){return R},greyDark:function(){return h},lime:function(){return A},limeDark:function(){return t},magenta:function(){return W},magentaDark:function(){return y},orange:function(){return F},orangeDark:function(){return p},presetDarkPalettes:function(){return J},presetPalettes:function(){return q},presetPrimaryColors:function(){return w},purple:function(){return V},purpleDark:function(){return S},red:function(){return u},redDark:function(){return n},volcano:function(){return s},volcanoDark:function(){return c},yellow:function(){return d},yellowDark:function(){return r}});var m=C(87471),g=2,D=.16,Z=.05,z=.05,G=.15,H=5,E=4,I=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function B(i,v,b){var f;return Math.round(i.h)>=60&&Math.round(i.h)<=240?f=b?Math.round(i.h)-g*v:Math.round(i.h)+g*v:f=b?Math.round(i.h)+g*v:Math.round(i.h)-g*v,f<0?f+=360:f>=360&&(f-=360),f}function P(i,v,b){if(i.h===0&&i.s===0)return i.s;var f;return b?f=i.s-D*v:v===E?f=i.s+D:f=i.s+Z*v,f>1&&(f=1),b&&v===H&&f>.1&&(f=.1),f<.06&&(f=.06),Math.round(f*100)/100}function k(i,v,b){var f;return b?f=i.v+z*v:f=i.v-G*v,f=Math.max(0,Math.min(1,f)),Math.round(f*100)/100}function O(i){for(var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},b=[],f=new m.FastColor(i),j=f.toHsv(),Q=H;Q>0;Q-=1){var rr=new m.FastColor({h:B(j,Q,!0),s:P(j,Q,!0),v:k(j,Q,!0)});b.push(rr)}b.push(f);for(var X=1;X<=E;X+=1){var ar=new m.FastColor({h:B(j,X),s:P(j,X),v:k(j,X)});b.push(ar)}return v.theme==="dark"?I.map(function(Y){var er=Y.index,nr=Y.amount;return new m.FastColor(v.backgroundColor||"#141414").mix(b[er],nr).toHexString()}):b.map(function(Y){return Y.toHexString()})}var w={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},u=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];u.primary=u[5];var s=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];s.primary=s[5];var F=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];F.primary=F[5];var M=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];M.primary=M[5];var d=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];d.primary=d[5];var A=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];A.primary=A[5];var K=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];K.primary=K[5];var L=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];L.primary=L[5];var T=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];T.primary=T[5];var U=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];U.primary=U[5];var V=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];V.primary=V[5];var W=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];W.primary=W[5];var R=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];R.primary=R[5];var $=R,q={red:u,volcano:s,orange:F,gold:M,yellow:d,lime:A,green:K,cyan:L,blue:T,geekblue:U,purple:V,magenta:W,grey:R},n=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];n.primary=n[5];var c=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];c.primary=c[5];var p=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];p.primary=p[5];var e=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];e.primary=e[5];var r=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];r.primary=r[5];var t=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];t.primary=t[5];var o=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];o.primary=o[5];var a=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];a.primary=a[5];var l=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];l.primary=l[5];var x=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];x.primary=x[5];var S=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];S.primary=S[5];var y=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];y.primary=y[5];var h=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];h.primary=h[5];var J={red:n,volcano:c,orange:p,gold:e,yellow:r,lime:t,green:o,cyan:a,blue:l,geekblue:x,purple:S,magenta:y,grey:h}}}]);
