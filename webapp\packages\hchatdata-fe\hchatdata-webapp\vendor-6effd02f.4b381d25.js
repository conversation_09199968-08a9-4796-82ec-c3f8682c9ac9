!(function(){"use strict";var Bt=Object.defineProperty,Kt=Object.defineProperties;var Ht=Object.getOwnPropertyDescriptors;var Ut=Object.getOwnPropertySymbols;var zt=Object.prototype.hasOwnProperty,Jt=Object.prototype.propertyIsEnumerable;var xt=(U,O,u)=>O in U?Bt(U,O,{enumerable:!0,configurable:!0,writable:!0,value:u}):U[O]=u,At=(U,O)=>{for(var u in O||(O={}))zt.call(O,u)&&xt(U,u,O[u]);if(Ut)for(var u of Ut(O))Jt.call(O,u)&&xt(U,u,O[u]);return U},Ft=(U,O)=>Kt(U,Ht(O));var Vt=(U,O,u)=>new Promise((r,Q)=>{var n=p=>{try{P(u.next(p))}catch(K){Q(K)}},m=p=>{try{P(u.throw(p))}catch(K){Q(K)}},P=p=>p.done?r(p.value):Promise.resolve(p.value).then(n,m);P((u=u.apply(U,O)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[821],{81687:function(U,O,u){u.d(O,{B:function(){return tt},I:function(){return ut},O:function(){return K},S:function(){return gt},U:function(){return p},a:function(){return H},b:function(){return m},c:function(){return ht},d:function(){return Tt},e:function(){return L},f:function(){return yt},g:function(){return pt},i:function(){return _},m:function(){return q},n:function(){return lt},o:function(){return Et},r:function(){return ft},s:function(){return s},t:function(){return Ot},u:function(){return vt},z:function(){return M}});var r=u(44194),Q=u(46422),n=u(17644);const m=new WeakMap,P=()=>{},p=P(),K=Object,L=t=>t===p,H=t=>typeof t=="function",q=(t,o)=>At(At({},t),o),tt=t=>H(t.then),et={},ct={},at="undefined",_=typeof window!=at,x=typeof document!=at,S=_&&"Deno"in window,T=()=>_&&typeof window.requestAnimationFrame!=at,M=(t,o)=>{const c=m.get(t);return[()=>!L(o)&&t.get(o)||et,h=>{if(!L(o)){const l=t.get(o);o in ct||(ct[o]=l),c[5](o,q(l,h),l||et)}},c[6],()=>!L(o)&&o in ct?ct[o]:!L(o)&&t.get(o)||et]};let V=!0;const k=()=>V,[e,E]=_&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[P,P],A=()=>{const t=x&&document.visibilityState;return L(t)||t!=="hidden"},z=t=>(x&&document.addEventListener("visibilitychange",t),e("focus",t),()=>{x&&document.removeEventListener("visibilitychange",t),E("focus",t)}),X=t=>{const o=()=>{V=!0,t()},c=()=>{V=!1};return e("online",o),e("offline",c),()=>{E("online",o),E("offline",c)}},I={isOnline:k,isVisible:A},J={initFocus:z,initReconnect:X},ut=!r.useId,ft=!_||S,Ot=t=>T()?window.requestAnimationFrame(t):setTimeout(t,1),vt=ft?r.useEffect:r.useLayoutEffect,mt=typeof navigator!="undefined"&&navigator.connection,It=!ft&&mt&&(["slow-2g","2g"].includes(mt.effectiveType)||mt.saveData),nt=new WeakMap,wt=(t,o)=>K.prototype.toString.call(t)===`[object ${o}]`;let ot=0;const Ct=t=>{const o=typeof t,c=wt(t,"Date"),h=wt(t,"RegExp"),l=wt(t,"Object");let i,a;if(K(t)===t&&!c&&!h){if(i=nt.get(t),i)return i;if(i=++ot+"~",nt.set(t,i),Array.isArray(t)){for(i="@",a=0;a<t.length;a++)i+=Ct(t[a])+",";nt.set(t,i)}if(l){i="#";const v=K.keys(t).sort();for(;!L(a=v.pop());)L(t[a])||(i+=a+":"+Ct(t[a])+",");nt.set(t,i)}}else i=c?t.toJSON():o=="symbol"?t.toString():o=="string"?JSON.stringify(t):""+t;return i},s=t=>{if(H(t))try{t=t()}catch(c){t=""}const o=t;return t=typeof t=="string"?t:(Array.isArray(t)?t.length:t)?Ct(t):"",[t,o]};let Lt=0;const Et=()=>++Lt;function lt(...t){return Vt(this,null,function*(){const[o,c,h,l]=t,i=q({populateCache:!0,throwOnError:!0},typeof l=="boolean"?{revalidate:l}:l||{});let a=i.populateCache;const v=i.rollbackOnError;let w=i.optimisticData;const dt=N=>typeof v=="function"?v(N):v!==!1,F=i.throwOnError;if(H(c)){const N=c,W=[],Z=o.keys();for(const j of Z)!/^\$(inf|sub)\$/.test(j)&&N(o.get(j)._k)&&W.push(j);return Promise.all(W.map(B))}return B(c);function B(N){return Vt(this,null,function*(){const[W]=s(N);if(!W)return;const[Z,j]=M(o,W),[Pt,Mt,R,d]=m.get(o),g=()=>{const Y=Pt[W];return(H(i.revalidate)?i.revalidate(Z().data,N):i.revalidate!==!1)&&(delete R[W],delete d[W],Y&&Y[0])?Y[0](Q.QQ).then(()=>Z().data):Z().data};if(t.length<3)return g();let f=h,C;const rt=Et();Mt[W]=[rt,0];const b=!L(w),y=Z(),G=y.data,bt=y._c,_t=L(bt)?G:bt;if(b&&(w=H(w)?w(_t,G):w,j({data:w,_c:_t})),H(f))try{f=f(_t)}catch(Y){C=Y}if(f&&tt(f))if(f=yield f.catch(Y=>{C=Y}),rt!==Mt[W][0]){if(C)throw C;return f}else C&&b&&dt(C)&&(a=!0,j({data:_t,_c:p}));if(a&&!C)if(H(a)){const Y=a(f,_t);j({data:Y,error:p,_c:p})}else j({data:f,error:p,_c:p});if(Mt[W][1]=Et(),Promise.resolve(g()).then(()=>{j({_c:p})}),C){if(F)throw C;return}return f})}})}const Rt=(t,o)=>{for(const c in t)t[c][0]&&t[c][0](o)},Dt=(t,o)=>{if(!m.has(t)){const c=q(J,o),h=Object.create(null),l=lt.bind(p,t);let i=P;const a=Object.create(null),v=(F,B)=>{const N=a[F]||[];return a[F]=N,N.push(B),()=>N.splice(N.indexOf(B),1)},w=(F,B,N)=>{t.set(F,B);const W=a[F];if(W)for(const Z of W)Z(B,N)},dt=()=>{if(!m.has(t)&&(m.set(t,[h,Object.create(null),Object.create(null),Object.create(null),l,w,v]),!ft)){const F=c.initFocus(setTimeout.bind(p,Rt.bind(p,h,Q.N4))),B=c.initReconnect(setTimeout.bind(p,Rt.bind(p,h,Q.l2)));i=()=>{F&&F(),B&&B(),m.delete(t)}}};return dt(),[t,l,dt,i]}return[t,m.get(t)[4]]},St=(t,o,c,h,l)=>{const i=c.errorRetryCount,a=l.retryCount,v=~~((Math.random()+.5)*(1<<(a<8?a:8)))*c.errorRetryInterval;!L(i)&&a>i||setTimeout(h,v,l)},D=n.J,[ht,st]=Dt(new Map),Tt=q({onLoadingSlow:P,onSuccess:P,onError:P,onErrorRetry:St,onDiscarded:P,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:It?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:It?5e3:3e3,compare:D,isPaused:()=>!1,cache:ht,mutate:st,fallback:{}},I),yt=(t,o)=>{const c=q(t,o);if(o){const{use:h,fallback:l}=t,{use:i,fallback:a}=o;h&&i&&(c.use=h.concat(i)),l&&a&&(c.fallback=q(l,a))}return c},gt=(0,r.createContext)({}),pt=t=>{const{value:o}=t,c=(0,r.useContext)(gt),h=H(o),l=(0,r.useMemo)(()=>h?o(c):o,[h,c,o]),i=(0,r.useMemo)(()=>h?l:yt(c,l),[h,c,l]),a=l&&l.provider,v=(0,r.useRef)(p);a&&!v.current&&(v.current=Dt(a(i.cache||ht),l));const w=v.current;return w&&(i.cache=w[0],i.mutate=w[1]),vt(()=>{if(w)return w[2]&&w[2](),w[3]},[]),(0,r.createElement)(gt.Provider,q(t,{value:i}))}},46422:function(U,O,u){u.d(O,{N4:function(){return r},QQ:function(){return n},aU:function(){return m},l2:function(){return Q}});const r=0,Q=1,n=2,m=3},3842:function(U,O,u){u.d(O,{ko:function(){return ct},kY:function(){return L},s6:function(){return et}});var r=u(81687);const Q="$inf$";var n=u(44194);const m=r.i&&window.__SWR_DEVTOOLS_USE__,P=m?window.__SWR_DEVTOOLS_USE__:[],p=()=>{m&&(window.__SWR_DEVTOOLS_REACT__=n)},K=_=>(0,r.a)(_[1])?[_[0],_[1],_[2]||{}]:[_[0],null,(_[1]===null?_[2]:_[1])||{}],L=()=>(0,r.m)(r.d,(0,n.useContext)(r.S)),H=(_,x)=>{const[S,T]=serialize(_),[,,,M]=SWRGlobalState.get(cache);if(M[S])return M[S];const V=x(T);return M[S]=V,V},q=_=>(x,S,T)=>_(x,S&&((...V)=>{const[k]=(0,r.s)(x),[,,,e]=r.b.get(r.c);if(k.startsWith(Q))return S(...V);const E=e[k];return(0,r.e)(E)?S(...V):(delete e[k],E)}),T),tt=P.concat(q),et=_=>function(...S){const T=L(),[M,V,k]=K(S),e=(0,r.f)(T,k);let E=_;const{use:A}=e,z=(A||[]).concat(tt);for(let X=z.length;X--;)E=z[X](E);return E(M,V||e.fetcher||null,e)},ct=(_,x,S)=>{const T=x[_]||(x[_]=[]);return T.push(S),()=>{const M=T.indexOf(S);M>=0&&(T[M]=T[T.length-1],T.pop())}},at=(_,x)=>(...S)=>{const[T,M,V]=K(S),k=(V.use||[]).concat(x);return _(T,M,Ft(At({},V),{use:k}))};p()},21228:function(U,O,u){u.d(O,{J$:function(){return V},ZP:function(){return k}});var r=u(44194),Q=u(94191),n=u(81687),m=u(46422),P=u(3842);const K=void 0,L=null,H=e=>e===K,q=e=>typeof e=="function",tt=new WeakMap,et=(e,E)=>L.prototype.toString.call(e)===`[object ${E}]`;let ct=0;const at=e=>{const E=typeof e,A=et(e,"Date"),z=et(e,"RegExp"),X=et(e,"Object");let I,J;if(L(e)===e&&!A&&!z){if(I=tt.get(e),I)return I;if(I=++ct+"~",tt.set(e,I),Array.isArray(e)){for(I="@",J=0;J<e.length;J++)I+=at(e[J])+",";tt.set(e,I)}if(X){I="#";const ut=L.keys(e).sort();for(;!H(J=ut.pop());)H(e[J])||(I+=J+":"+at(e[J])+",");tt.set(e,I)}}else I=A?e.toJSON():E=="symbol"?e.toString():E=="string"?JSON.stringify(e):""+e;return I},_=e=>{if(q(e))try{e=e()}catch(A){e=""}const E=e;return e=typeof e=="string"?e:(Array.isArray(e)?e.length:e)?at(e):"",[e,E]},x=e=>_(e)[0],S=r.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(E=>{e.status="fulfilled",e.value=E},E=>{e.status="rejected",e.reason=E}),e}}),T={dedupe:!0},M=(e,E,A)=>{const{cache:z,compare:X,suspense:I,fallbackData:J,revalidateOnMount:ut,revalidateIfStale:ft,refreshInterval:Ot,refreshWhenHidden:vt,refreshWhenOffline:mt,keepPreviousData:It}=A,[nt,wt,ot,Ct]=n.b.get(z),[s,Lt]=(0,n.s)(e),Et=(0,r.useRef)(!1),lt=(0,r.useRef)(!1),Rt=(0,r.useRef)(s),Dt=(0,r.useRef)(E),St=(0,r.useRef)(A),D=()=>St.current,ht=()=>D().isVisible()&&D().isOnline(),[st,Tt,yt,gt]=(0,n.z)(z,s),pt=(0,r.useRef)({}).current,t=(0,n.e)(J)?(0,n.e)(A.fallback)?n.U:A.fallback[s]:J,o=(R,d)=>{for(const g in pt){const f=g;if(f==="data"){if(!X(R[f],d[f])&&(!(0,n.e)(R[f])||!X(F,d[f])))return!1}else if(d[f]!==R[f])return!1}return!0},c=(0,r.useMemo)(()=>{const R=!s||!E?!1:(0,n.e)(ut)?D().isPaused()||I?!1:ft!==!1:ut,d=y=>{const G=(0,n.m)(y);return delete G._k,R?At({isValidating:!0,isLoading:!0},G):G},g=st(),f=gt(),C=d(g),rt=g===f?C:d(f);let b=C;return[()=>{const y=d(st());return o(y,b)?(b.data=y.data,b.isLoading=y.isLoading,b.isValidating=y.isValidating,b.error=y.error,b):(b=y,y)},()=>rt]},[z,s]),h=(0,Q.useSyncExternalStore)((0,r.useCallback)(R=>yt(s,(d,g)=>{o(g,d)||R()}),[z,s]),c[0],c[1]),l=!Et.current,i=nt[s]&&nt[s].length>0,a=h.data,v=(0,n.e)(a)?t&&(0,n.B)(t)?S(t):t:a,w=h.error,dt=(0,r.useRef)(v),F=It?(0,n.e)(a)?(0,n.e)(dt.current)?v:dt.current:a:v,B=i&&!(0,n.e)(w)?!1:l&&!(0,n.e)(ut)?ut:D().isPaused()?!1:I?(0,n.e)(v)?!1:ft:(0,n.e)(v)||ft,N=!!(s&&E&&l&&B),W=(0,n.e)(h.isValidating)?N:h.isValidating,Z=(0,n.e)(h.isLoading)?N:h.isLoading,j=(0,r.useCallback)(R=>Vt(this,null,function*(){const d=Dt.current;if(!s||!d||lt.current||D().isPaused())return!1;let g,f,C=!0;const rt=R||{},b=!ot[s]||!rt.dedupe,y=()=>n.I?!lt.current&&s===Rt.current&&Et.current:s===Rt.current,G={isValidating:!1,isLoading:!1},bt=()=>{Tt(G)},_t=()=>{const $=ot[s];$&&$[1]===f&&delete ot[s]},Y={isValidating:!0};(0,n.e)(st().data)&&(Y.isLoading=!0);try{if(b&&(Tt(Y),A.loadingTimeout&&(0,n.e)(st().data)&&setTimeout(()=>{C&&y()&&D().onLoadingSlow(s,A)},A.loadingTimeout),ot[s]=[d(Lt),(0,n.o)()]),[g,f]=ot[s],g=yield g,b&&setTimeout(_t,A.dedupingInterval),!ot[s]||ot[s][1]!==f)return b&&y()&&D().onDiscarded(s),!1;G.error=n.U;const $=wt[s];if(!(0,n.e)($)&&(f<=$[0]||f<=$[1]||$[1]===0))return bt(),b&&y()&&D().onDiscarded(s),!1;const it=st().data;G.data=X(it,g)?it:g,b&&y()&&D().onSuccess(g,s,A)}catch($){_t();const it=D(),{shouldRetryOnError:Nt}=it;it.isPaused()||(G.error=$,b&&y()&&(it.onError($,s,it),(Nt===!0||(0,n.a)(Nt)&&Nt($))&&(!D().revalidateOnFocus||!D().revalidateOnReconnect||ht())&&it.onErrorRetry($,s,it,jt=>{const Wt=nt[s];Wt&&Wt[0]&&Wt[0](m.aU,jt)},{retryCount:(rt.retryCount||0)+1,dedupe:!0})))}return C=!1,bt(),!0}),[s,z]),Pt=(0,r.useCallback)((...R)=>(0,n.n)(z,Rt.current,...R),[]);if((0,n.u)(()=>{Dt.current=E,St.current=A,(0,n.e)(a)||(dt.current=a)}),(0,n.u)(()=>{if(!s)return;const R=j.bind(n.U,T);let d=0;D().revalidateOnFocus&&(d=Date.now()+D().focusThrottleInterval);const g=(C,rt={})=>{if(C==m.N4){const b=Date.now();D().revalidateOnFocus&&b>d&&ht()&&(d=b+D().focusThrottleInterval,R())}else if(C==m.l2)D().revalidateOnReconnect&&ht()&&R();else{if(C==m.QQ)return j();if(C==m.aU)return j(rt)}},f=(0,P.ko)(s,nt,g);return lt.current=!1,Rt.current=s,Et.current=!0,Tt({_k:Lt}),B&&((0,n.e)(v)||n.r?R():(0,n.t)(R)),()=>{lt.current=!0,f()}},[s]),(0,n.u)(()=>{let R;function d(){const f=(0,n.a)(Ot)?Ot(st().data):Ot;f&&R!==-1&&(R=setTimeout(g,f))}function g(){!st().error&&(vt||D().isVisible())&&(mt||D().isOnline())?j(T).then(d):d()}return d(),()=>{R&&(clearTimeout(R),R=-1)}},[Ot,vt,mt,s]),(0,r.useDebugValue)(F),I&&(0,n.e)(v)&&s){if(!n.I&&n.r)throw new Error("Fallback data is required when using Suspense in SSR.");Dt.current=E,St.current=A,lt.current=!1;const R=Ct[s];if(!(0,n.e)(R)){const d=Pt(R);S(d)}if((0,n.e)(w)){const d=j(T);(0,n.e)(F)||(d.status="fulfilled",d.value=!0),S(d)}else throw w}return{mutate:Pt,get data(){return pt.data=!0,F},get error(){return pt.error=!0,w},get isValidating(){return pt.isValidating=!0,W},get isLoading(){return pt.isLoading=!0,Z}}},V=n.O.defineProperty(n.g,"defaultValue",{value:n.d}),k=(0,P.s6)(M)}}]);
}());