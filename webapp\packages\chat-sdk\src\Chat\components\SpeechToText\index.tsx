// SpeetchtoTText.tsx
import React, { useState } from 'react'
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition'
import { AudioOutlined, PauseCircleOutlined } from '@ant-design/icons'

interface SpeetchtoTTextProps {
    onResult: (text: string) => void
    onRecordingStateChange?: (isRecording: boolean) => void
}

const SpeetchtoTText: React.FC<SpeetchtoTTextProps> = ({ onResult, onRecordingStateChange }) => {
    const { transcript, browserSupportsSpeechRecognition, resetTranscript } =
        useSpeechRecognition()

    const [isRecording, setIsRecording] = useState(false)

    if (!browserSupportsSpeechRecognition) {
        return <span>❌ 当前浏览器不支持语音识别（建议用 Chrome）</span>
    }

    const startRecording = () => {
        resetTranscript()
        setIsRecording(true)
        onRecordingStateChange?.(true)
        SpeechRecognition.startListening({ continuous: true, language: 'zh-CN' })
    }

    const stopRecording = () => {
        setIsRecording(false)
        onRecordingStateChange?.(false)
        SpeechRecognition.stopListening()
        onResult(transcript.trim())
    }

    return (
        <div style={{ fontSize: 24, cursor: 'pointer' }}>
            {!isRecording ? (
                <AudioOutlined
                    // style={{ color: '#1890ff' }}
                    onClick={startRecording}
                    title="开始录音"
                />
            ) : (
                <PauseCircleOutlined
                    style={{ color: '#f5222d' }}
                    onClick={stopRecording}
                    title="停止录音"
                />
            )}
        </div>
    )
}

export default SpeetchtoTText
